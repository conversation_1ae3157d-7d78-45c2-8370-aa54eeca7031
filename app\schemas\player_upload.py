import uuid
from typing import TYPE_CHECKING
from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)
if TYPE_CHECKING:
    from app.schemas.player_record import PlayerRecord


class PlayerUploadUpdate(ExtendedUpdateBase):
    ...


class PlayerUploadCreate(ExtendedCreateBase):
    player_id: uuid.UUID
    id: str
    name: str

class PlayerUpload(PlayerUploadCreate, ExtendedBase):
    ...

    class Config:
        orm_mode = True
        use_cache = True
