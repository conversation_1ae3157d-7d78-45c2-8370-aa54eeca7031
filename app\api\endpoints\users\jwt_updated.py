from fastapi_users import models
from fastapi_users.authentication import (
    JWTStrategy
)
from fastapi import Request
from fastapi_users.jwt import generate_jwt, decode_jwt
from app.api.utils import get_user_modules
from typing import Optional
from fastapi_users.manager import BaseUserManager, UserManagerDependency
from fastapi_users import exceptions, models
import jwt
from firebase_admin import auth, initialize_app, delete_app, get_app, credentials
from app.config import settings
from app.api.utils import decode_firebase_jwt, refresh_token, delete_refresh_token
import requests
from app import crud
from app.db.session import session_maker
from datetime import datetime, timezone

cred = credentials.Certificate(settings.FIREBASE_CREDS)
try:
    initialize_app(credential=cred)
except:
    pass


class JWTUpStrategy(JWTStrategy):
    def __init__(self, secret: str, lifetime_seconds: int, remember_me_lifetime_seconds: int):
        super().__init__(secret=secret, lifetime_seconds=lifetime_seconds)
        self.remember_me_lifetime_seconds = remember_me_lifetime_seconds

    

    async def write_token(self, user: models.UP) -> str:
        refreshed_id_token = refresh_token(user.id)
        return refreshed_id_token['id_token']
        
    
    async def read_token(
        self, token: Optional[str], user_manager: BaseUserManager[models.UP, models.ID]
    ) -> Optional[models.UP]:
        if token is None:
            return None
        fb_user = decode_firebase_jwt(token)
        user_id = fb_user.get("sub")
        new_token = token

        if fb_user.get('remember_me'):
            if fb_user.get('exp') <= datetime.now(timezone.utc).timestamp():
                try:
                    new_token = refresh_token(user_id)['id_token']
                    fb_user = decode_firebase_jwt(new_token)  # Decode new token
                except:

                    return None      
        try:
            data = auth.verify_id_token(new_token, check_revoked=True, clock_skew_seconds=30)
            parsed_id = user_manager.parse_id(user_id)
            return await user_manager.get(parsed_id)
        except jwt.ExpiredSignatureError:
            delete_refresh_token(user_manager.parse_id(user_id))
        except auth.RevokedIdTokenError:
                print('Token revoked')
            # Token revoked, inform the user to reauthenticate or signOut().
                # raise auth.RevokedIdTokenError('You must sign in again.')
                return None
        except auth.UserDisabledError:
            print('Token invalid1')
            # Token belongs to a disabled user record.
            raise auth.UserDisabledError('User no longer has access.')
        except auth.InvalidIdTokenError:
            print('Token invalid')
            # Token is invalid
            # raise auth.InvalidIdTokenError('Token is no longer valid, please reauthenticate.')
            return None
        except jwt.PyJWTError:
            return None
        
        
