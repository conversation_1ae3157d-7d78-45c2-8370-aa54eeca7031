"""Assigned_to_id - multiple

Revision ID: dc28e4ba940d
Revises: c1e2d1da266a
Create Date: 2024-10-21 15:19:32.813872

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'dc28e4ba940d'
down_revision = 'c1e2d1da266a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('assigned_to_record',
    sa.Column('contact_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('activity_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['activity_id'], ['crm_test.activity.id'], ),
    sa.ForeignKeyConstraint(['contact_id'], ['crm_test.contacts.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['player_id'], ['crm_test.player_records.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_assigned_to_record_activity_id'), 'assigned_to_record', ['activity_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_assigned_to_record_contact_id'), 'assigned_to_record', ['contact_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_assigned_to_record_player_id'), 'assigned_to_record', ['player_id'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_test_assigned_to_record_player_id'), table_name='assigned_to_record', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_assigned_to_record_contact_id'), table_name='assigned_to_record', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_assigned_to_record_activity_id'), table_name='assigned_to_record', schema='crm_test')
    op.drop_table('assigned_to_record', schema='crm_test')
    # ### end Alembic commands ###