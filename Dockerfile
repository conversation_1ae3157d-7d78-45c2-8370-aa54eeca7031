FROM python:3.10-bullseye
RUN apt-get update
RUN apt-get -y install sudo
# RUN apt-get -y install libblas-dev  liblapack-dev 
# RUN  apt-get -y install gfortran
WORKDIR /usr/src/app

COPY requirements.txt ./
COPY creds.json ./
COPY creds_testing.json ./
COPY creds_staging.json ./


RUN curl -sSL https://sdk.cloud.google.com | bash
ENV PATH $PATH:/root/google-cloud-sdk/bin

RUN gcloud auth activate-service-account --key-file=creds.json
RUN export GOOGLE_APPLICATION_CREDENTIALS=creds.json

RUN pip install --no-cache-dir -r requirements.txt
RUN pip install pyjwt==2.10.1
RUN pip install firebase-admin==6.6.0

COPY app app/
COPY Dockerfile ./

CMD uvicorn app.main:app --host 0.0.0.0 --port $PORT
