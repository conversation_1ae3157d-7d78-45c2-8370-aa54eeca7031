"""added rank tables

Revision ID: c33888c80995
Revises: b951aea6a61e
Create Date: 2022-08-24 08:15:13.606425

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c33888c80995'
down_revision = 'b951aea6a61e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('rank_records',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('purpose', sa.String(), nullable=True),
    sa.Column('for_who', sa.String(), nullable=True),
    sa.Column('config', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_dev.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_dev.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_table('rank_outputs',
    sa.Column('rank_record_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('playerId', sa.Integer(), nullable=False),
    sa.Column('results', sa.String(), nullable=True),
    sa.Column('player_rating', sa.Float(), nullable=True),
    sa.Column('player_rank', sa.Integer(), nullable=True),
    # sa.ForeignKeyConstraint(['playerId'], ['wyscout.player_info2.playerId'], ),
    sa.ForeignKeyConstraint(['rank_record_id'], ['crm_dev.rank_records.id'], ),
    sa.PrimaryKeyConstraint('rank_record_id', 'playerId'),
    schema='crm_dev'
    )
    op.alter_column('default_comparable_positions', 'main_role',
               existing_type=sa.TEXT(),
               nullable=False,
               schema='player_quality')
    op.alter_column('default_filters', 'competition_scaling',
               existing_type=sa.TEXT(),
               nullable=False,
               schema='player_quality')
    op.alter_column('default_position_weights', 'main_role',
               existing_type=sa.TEXT(),
               nullable=False,
               schema='player_quality')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('default_position_weights', 'main_role',
               existing_type=sa.TEXT(),
               nullable=True,
               schema='player_quality')
    op.alter_column('default_filters', 'competition_scaling',
               existing_type=sa.TEXT(),
               nullable=True,
               schema='player_quality')
    op.alter_column('default_comparable_positions', 'main_role',
               existing_type=sa.TEXT(),
               nullable=True,
               schema='player_quality')
    op.drop_table('rank_outputs', schema='crm_dev')
    op.drop_table('rank_records', schema='crm_dev')
    # ### end Alembic commands ###