import uuid
from typing import Optional
from pydantic import BaseModel, validator
from app.schemas.enums import Features


class PlayerFeaturesUpdate(BaseModel):
    speed: Optional[Features]
    explosive: Optional[Features]
    dynamic: Optional[Features]
    build: Optional[Features]
    intelligence: Optional[Features]
    scanning: Optional[Features]
    pressing: Optional[Features]
    aggresive: Optional[Features]
    leader: Optional[Features]
    teamwork: Optional[Features]
    education: Optional[Features]
    entourage: Optional[Features]
    first_touch: Optional[Features]
    dribble: Optional[Features]
    progress_with_the_ball: Optional[Features]
    play_with_the_back: Optional[Features]
    diagonal_passing: Optional[Features]
    through_ball_passing: Optional[Features]
    passes_to_final_third: Optional[Features]
    pushes_inbetween_lines: Optional[Features]
    defensive_positioning: Optional[Features]
    attacking_positioning: Optional[Features]
    interceptions: Optional[Features]
    aerial_duels: Optional[Features]
    finishing: Optional[Features]
    heading: Optional[Features]
    cut_inside: Optional[Features]
    gk_1_vs_one_duels: Optional[Features]
    defensive_1_vs_one_duels: Optional[Features]
    closing_space: Optional[Features]
    ball_control: Optional[Features]
    reflexes: Optional[Features]
    set_pieces: Optional[Features]

    class Config:
        orm_mode = True

    @validator("*", pre=True)
    def allow_none(cls, v):
        return None if v == "" else v


class PlayerFeatures(PlayerFeaturesUpdate):
    player_id: Optional[uuid.UUID]
