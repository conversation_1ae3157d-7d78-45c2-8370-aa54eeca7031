from typing import Any, List
from datetime import date
from app import models, crud
from fastapi import APIRouter, Depends, Query
from app.api import deps, utils
from sqlalchemy.orm import Session

try:
    from ...config import settings
except:
    from app.config import settings
import pandas as pd
from app.db.session import engine

router = APIRouter()


@router.get("/{playerId}")
def get_clubs(
    *,
    db: Session = Depends(deps.get_db),
    playerId: str,
    league: bool,
    culture: bool,
    position: bool,
    division_level: List[str] = Query(None),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    player_to_transfer = pd.read_sql(
        f"""select c.culture, c1.region as league_region, pi2.role_name, pi2.team_area_name
     from wyscout.player_info2 pi2
      join public.countries c on c.alpha3code = pi2."birthArea_alpha3code" 
      left join public.countries c1 on c1.country_name = pi2.team_area_name 
      where "playerId" = {playerId}""",
        engine,
    )
    culture_q = player_to_transfer.iloc[0].culture
    left_region_q = player_to_transfer.iloc[0].league_region or "none"
    role_name = player_to_transfer.iloc[0].role_name
    team_area_name = player_to_transfer.iloc[0].team_area_name

    if not team_area_name:
        last_team_player = pd.read_sql(
            f"""      select ti.area_name, c.region as league_region from wyscout.player_match_info sp
      join wyscout.team_info2 ti on ti."teamId" = sp."teamId" 
      join public.countries c on c.country_name = ti.area_name 
       where "playerId" = '7910' 
      order by date desc
      limit 1""",
            engine,
        )
        left_region_q = last_team_player.iloc[0].league_region or "none"
        team_area_name = last_team_player.iloc[0].area_name

    query = f"""with main_tbl as (select *
from (
        select sd.staff_id,
            sd.current_team_id,
            tt.joined_name,
            td."shortName",
            tt."date",
            tt.tm_player_id,
            tt.left_name,
            (current_date - sd.birth_date::date) / 365::int as age,
            sd.appointed,
            sd.in_charge_until,
            sd.role,
            sd.name
        from transfermarkt.staff_data sd
            join transfermarkt.tm_transfers tt on tt.joined_id = sd.current_team_id
            join transfermarkt.tm_to_ws_ids ttwi on tt.tm_player_id = ttwi.tm_player_id
            join wyscout.player_info2 td on td."playerId" = ttwi."playerId"
            left join transfermarkt.tm_teams tt2 on tt2.team_id = tt.left_id
            left join transfermarkt.tm_teams tt3 on tt3.team_id = tt.joined_id
        where role in (
                'Manager',
                'Assistant Manager',
                'Caretaker Manager',
                'Sporting Director',
                'Scout',
                'Director of Football',
                'Technical Director',
                'Chief Scout',
                'Chief Analyst',
                'Head of Scouting',
                'Head of Football Operations',
                'Managing Director Sport',
                'Managing Director Professional Football',
                'Director of Professional Football and Scout',
                'Global Sports Director',
                'Sporting CEO, Director of Sport',
                'President',
                'Owner',
                'Chief Executive Officer'
            )
            and sd.status = 'alive'
            and tt.joined_name is not null
            and appointed::date < tt."date"
            and coalesce(in_charge_until, CURRENT_DATE::text)::date >= tt."date"
            and tt.new_type != 'back from loan'
            and tt.joined_id not in ('515', '75')
     """

    if league:
        if left_region_q == "Macedonia FYR":
            query += f"""and tt2.league_country in (select 'North Macedonia' from public.countries where region = '{left_region_q}')
      and tt3.league_country not in (select 'North Macedonia' from public.countries where region = '{left_region_q}')"""
        elif left_region_q == "turkey":
            query += f"""and tt2.league_country in (select 'Türkiye' from public.countries where region = '{left_region_q}')
      and tt3.league_country not in (select 'Türkiye' from public.countries where region = '{left_region_q}')"""
        else:
            query += f"""and coalesce(tt2.league_country, 'none') in (select country_name from public.countries where region = '{left_region_q}')
      and coalesce(tt3.league_country, 'none') not in (select country_name from public.countries where region = '{left_region_q}')"""
    if culture:
        query += f"""and td."birthArea_name" in (select country_name from public.countries where culture = '{culture_q}')"""
    if division_level[0]:
        unkown_division = False
        for i in range(len(division_level)):
            if division_level[i] == "Unknown division":
                unkown_division = " or tt2.league_country is null)"
                division_level[i] = "null"
                break
        if unkown_division:
            query += (
                f"""and (tt2.league_tier in ({", ".join(division_level)})"""
                + unkown_division
            )
        else:
            query += f"""and tt2.league_tier in ({", ".join(division_level)})"""
    if position:
        query += f"""and td.role_name = '{role_name}'"""
    query += """) as s)
, main_tbl_w_min_date as (
    select *, min("date"::date) over (partition by "tm_player_id", "joined_name") as player_first_join
    from main_tbl
)
select distinct on ("tm_player_id", "staff_id", "joined_name") * 
from main_tbl_w_min_date
where "date" <= player_first_join::date + interval '18 months'"""

    relevant_staff = pd.read_sql(
        query,
        engine,
    )
    dd = {}  # staff_id : [transfer, transfer, transfer, ...]
    relevant_staff = relevant_staff.fillna("")
    for index, row in relevant_staff.iterrows():
        staff_id = row["staff_id"]
        if staff_id in dd:
            dd[staff_id].append(row)
        else:
            dd[staff_id] = [row]

    staff_ids = []

    for s, t in sorted(dd.items(), key=lambda x: len(x[1]), reverse=True):
        staff_ids.append(s)

    def get_club_for_staff(staff_ids):
        query_staff = f"""select sd.staff_id,tt.team_name,tt.league_name, tt.league_country,coalesce(sum(td.current_value),0) as squad_value
            from transfermarkt.staff_data sd
            join transfermarkt.tm_teams tt on tt.team_id = sd.current_team_id 
            join transfermarkt.transfermarkt_data td on tt.team_id = td.current_club_id 
            where staff_id in ({", ".join(["'"+str(i)+"'" for i in staff_ids])})
            and (in_charge_until is null or in_charge_until > '{date.today()}')
            and tt.league_country != '{team_area_name}'"""

        query_staff += (
            " group by sd.staff_id,tt.team_name,tt.league_name, tt.league_country"
        )

        return pd.read_sql(
            query_staff,
            engine,
        )

    if not staff_ids:
        return [False]
    clubs = get_club_for_staff(staff_ids)
    staff_ids = set()

    rows = {}
    for i, r in clubs.iterrows():
        if r["staff_id"] not in staff_ids:
            if r["team_name"] not in rows:
                rows[r["team_name"]] = {
                    "current_team": r["team_name"],
                    "current_league": r["league_name"],
                    "transfer_count": len(dd[r["staff_id"]]),
                    "id": r["staff_id"],
                    "league_country": r["league_country"],
                    "squad_value": r["squad_value"],
                    "transfers": dd[r["staff_id"]],
                }
            else:
                if len(dd[r["staff_id"]]) > rows[r["team_name"]]["transfer_count"]:
                    rows[r["team_name"]]["transfer_count"] = len(dd[r["staff_id"]])
                rows[r["team_name"]]["transfers"].extend(dd[r["staff_id"]])
            staff_ids.add(r["staff_id"])
    # return list(dict(sorted(rows.items(), key=lambda x:x[1], reverse=True)).keys())
    return [v for k, v in rows.items()]
