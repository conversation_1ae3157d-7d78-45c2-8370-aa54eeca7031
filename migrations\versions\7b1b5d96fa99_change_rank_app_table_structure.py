"""Change rank app table structure

Revision ID: 7b1b5d96fa99
Revises: c33888c80995
Create Date: 2022-08-24 10:14:57.917367

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7b1b5d96fa99'
down_revision = 'c33888c80995'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rank_outputs', 'results', schema='crm_dev')
    op.add_column('rank_records', sa.Column('results', sa.String(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rank_records', 'results', schema='crm_dev')
    op.add_column('rank_outputs', sa.Column('results', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_dev')
    # ### end Alembic commands ###