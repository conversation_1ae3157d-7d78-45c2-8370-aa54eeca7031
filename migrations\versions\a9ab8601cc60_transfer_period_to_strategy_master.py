"""Transfer period to strategy - master

Revision ID: a9ab8601cc60
Revises: bde604daf0bb
Create Date: 2024-09-17 09:38:55.095173

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a9ab8601cc60'
down_revision = 'bde604daf0bb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('transfer_strategy', sa.String(), nullable=True), schema='crm')
    op.drop_column('player_records', 'transfer_period', schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('transfer_period', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True), schema='crm')
    op.drop_column('player_records', 'transfer_strategy', schema='crm')
    # ### end Alembic commands ###