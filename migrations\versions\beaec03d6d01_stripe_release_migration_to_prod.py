"""Stripe release migration to prod

Revision ID: beaec03d6d01
Revises: 24144feb420e
Create Date: 2024-03-11 18:45:44.309974

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'beaec03d6d01'
down_revision = '24144feb420e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('message_subscription',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('token', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('approved', sa.<PERSON>(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['crm.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_message_subscription_created_at'), 'message_subscription', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_message_subscription_user_id'), 'message_subscription', ['user_id'], unique=False, schema='crm')
    op.create_table('refresh_token',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('refresh_token', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['id'], ['crm.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_refresh_token_id'), 'refresh_token', ['id'], unique=False, schema='crm')
    op.add_column('organizations', sa.Column('billing_email', sa.String(), nullable=True), schema='crm')
    op.add_column('user', sa.Column('accept_terms', sa.Boolean(), nullable=True), schema='crm')
    op.add_column('user', sa.Column('accept_marketing_emails', sa.Boolean(), nullable=True), schema='crm')
    op.add_column('user', sa.Column('date_created', sa.DateTime(), nullable=True), schema='crm')
    op.drop_constraint('user_email_unique', 'user', schema='crm', type_='unique')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('user_email_unique', 'user', ['email'], schema='crm')
    op.drop_column('user', 'date_created', schema='crm')
    op.drop_column('user', 'accept_marketing_emails', schema='crm')
    op.drop_column('user', 'accept_terms', schema='crm')
    op.drop_column('organizations', 'billing_email', schema='crm')
    op.drop_index(op.f('ix_crm_refresh_token_id'), table_name='refresh_token', schema='crm')
    op.drop_table('refresh_token', schema='crm')
    op.drop_index(op.f('ix_crm_message_subscription_user_id'), table_name='message_subscription', schema='crm')
    op.drop_index(op.f('ix_crm_message_subscription_created_at'), table_name='message_subscription', schema='crm')
    op.drop_table('message_subscription', schema='crm')
    # ### end Alembic commands ###