"""removed assigned_id

Revision ID: 43ee5e7082bc
Revises: dc28e4ba940d
Create Date: 2024-10-22 16:59:22.550905

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '43ee5e7082bc'
down_revision = 'dc28e4ba940d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_crm_test_activity_assigned_to_id', table_name='activity', schema='crm_test')
    op.drop_constraint('activity_assigned_to_id_fkey', 'activity', schema='crm_test', type_='foreignkey')
    op.drop_column('activity', 'assigned_to_id', schema='crm_test')
    op.drop_index('ix_crm_test_player_records_assigned_to_id', table_name='player_records', schema='crm_test')
    op.drop_constraint('player_records_assigned_to_id_fkey', 'player_records', schema='crm_test', type_='foreignkey')
    op.drop_column('player_records', 'assigned_to_id', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('assigned_to_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm_test')
    op.create_foreign_key('player_records_assigned_to_id_fkey', 'player_records', 'contacts', ['assigned_to_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    op.create_index('ix_crm_test_player_records_assigned_to_id', 'player_records', ['assigned_to_id'], unique=False, schema='crm_test')
    op.add_column('activity', sa.Column('assigned_to_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm_test')
    op.create_foreign_key('activity_assigned_to_id_fkey', 'activity', 'contacts', ['assigned_to_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    op.create_index('ix_crm_test_activity_assigned_to_id', 'activity', ['assigned_to_id'], unique=False, schema='crm_test')
    # ### end Alembic commands ###