"""Hide team field in shadow requests

Revision ID: ba009e91e267
Revises: a3221f0902ea
Create Date: 2025-05-29 11:24:00.199523

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ba009e91e267'
down_revision = 'a3221f0902ea'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_requests', sa.Column('is_team_hidden', sa.<PERSON>(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('field_requests', 'is_team_hidden', schema='crm_test')
    # ### end Alembic commands ###