"""Added video link in community proposals

Revision ID: cbd4abdf21c2
Revises: eb4de28aa78d
Create Date: 2024-11-14 11:44:39.090114

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'cbd4abdf21c2'
down_revision = 'eb4de28aa78d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('community_proposals', sa.Column('video_link', sa.String(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('community_proposals', 'video_link', schema='crm_test')
    # ### end Alembic commands ###