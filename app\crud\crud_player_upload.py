from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.player_upload import PlayerU<PERSON>loadCreate, PlayerUploadUpdate
from sqlalchemy.orm import Session
from typing import List, Optional, TypeVar
from app.db.base_class import Base
from sqlalchemy.orm import Session, selectinload, lazyload, Load

ModelType = TypeVar("ModelType", bound=Base)
class CRUDPlayerUpload(CRUDBase[models.PlayerUpload, PlayerUploadCreate, PlayerUploadUpdate]):
    def get_files_for_player(self, db: Session, player_id: str, org_id: str) -> Optional[List[ModelType]]:
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).filter(self.model.player_id == player_id, self.model.organization_id == org_id).all()


player_upload = CRUDPlayerUpload(models.PlayerUpload)