"""On delete cascade

Revision ID: b677c202eae5
Revises: 71daff6eaf8a
Create Date: 2024-11-28 10:37:48.895441

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b677c202eae5'
down_revision = '71daff6eaf8a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('community_deal_community_proposal_id_fkey', 'community_deal', schema='crm_test', type_='foreignkey')
    op.create_foreign_key(None, 'community_deal', 'community_proposals', ['community_proposal_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'community_deal', schema='crm_test', type_='foreignkey')
    op.create_foreign_key('community_deal_community_proposal_id_fkey', 'community_deal', 'community_proposals', ['community_proposal_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    # ### end Alembic commands ###