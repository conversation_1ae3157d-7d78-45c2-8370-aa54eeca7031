import traceback
import uuid
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Response
from sqlalchemy.orm import Session

from app.schemas.player_upload import PlayerUpload, PlayerUploadCreate
from app import crud, models
from app.api import deps, utils
from app.utils.cloud_storage import CloudStorageManager

router = APIRouter()


@router.get("/file_info/", response_model=List[PlayerUpload])
def read_player_uploads(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all files' info.
    """
    utils.check_get_all(PlayerUpload, current_user)
    return crud.player_record.get_all_w_org(
        db, current_user.organization_id, utils.can_access_sensitive(current_user)
    )


@router.get("/file_info/{id}", response_model=PlayerUpload)
def get_file_info(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get file info by ID.
    """
    player_upload = crud.player_upload.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_get_one(player_upload, current_user)
    return player_upload

@router.get("/files_info/{player_id}", response_model=List[PlayerUpload])
def get_files_for_a_player(
    *,
    db: Session = Depends(deps.get_db),
    player_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get all files for a specific player.
    """
    player_uploads = crud.player_upload.get_files_for_player(db, player_id, current_user.organization_id)
    utils.check_get_all(player_uploads, current_user)
    return player_uploads

@router.get(
    "/download/{id}",
)
def download_file(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get contact by ID.
    """
    # first check if user can actually access the player file:
    player_upload = crud.player_upload.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_get_one(player_upload, current_user)
    # if all is ok, download file
    cm = CloudStorageManager()
    media_type = {
        "jpg": "image/jpeg",
        "jpeg": "image/jpeg",
        "png": "image/png",
        "pdf": "application/pdf",
    }.get(id.split(".")[-1])
    return Response(cm.get_blob(id), media_type=media_type)


@router.post(
    "/bulk/{player_id}",
    response_description="Upload multiple files at once",
    response_model=List[PlayerUpload],
)
def upload_bulk_files(
    *, 
    db: Session = Depends(deps.get_db),
    player_id: str,
    files: List[UploadFile] = File(),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    utils.check_create(PlayerUpload, current_user)
    files_for_writing = {}
    for f in files:
            ext = f.filename.split(".")[-1]
            if ext not in {"pdf", "png", "jpeg", "jpg", "docx", 'xlsx', 'csv'}:
                raise HTTPException(415, "Unsupported media type")
            filename = f"{str(uuid.uuid4())}.{ext}"
            files_for_writing[filename] = (PlayerUploadCreate(player_id=player_id, id=filename, name=f.filename.split(".")[0]), f)
    written_files = crud.player_upload.bulk_create_with_user(
        db=db, objs_in=[x[0] for x in files_for_writing.values()], user=current_user
    )
    try:
        cm = CloudStorageManager()
        for k, v in files_for_writing.items():
            cm.upload_blob(k, v[1].file)
        return written_files
    except:
        for k in files_for_writing:
            crud.player_upload.remove(db=db, id=k)

        raise HTTPException(500, "Failed to upload document")


@router.delete("/{id}", response_model=PlayerUpload)
def delete_player_upload(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a player file.
    """
    player_upload = crud.player_upload.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_delete(player_upload, current_user)
    player_upload_out = PlayerUpload.from_orm(player_upload)
    crud.player_upload.remove(db=db, id=id)
    try:
        cm = CloudStorageManager()
        cm.delete_blob(player_upload.id)
        return player_upload_out
    except:
        player_upload = crud.player_upload.create_with_user(
            db=db,
            obj_in=player_upload_out,
            user=current_user,
        )
        traceback.format_exc()
        raise HTTPException(500, "Failed to delete document")
