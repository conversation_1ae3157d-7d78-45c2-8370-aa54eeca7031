"""add contact channel col to player records

Revision ID: 4a06e1ba9525
Revises: 1b793c89d150
Create Date: 2022-10-03 12:36:45.093919

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4a06e1ba9525'
down_revision = '1b793c89d150'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_crm_dev_contacts_created_at', table_name='contacts', schema='crm')
    op.drop_index('ix_crm_dev_contacts_created_by', table_name='contacts', schema='crm')
    op.drop_index('ix_crm_dev_contacts_is_sensitive', table_name='contacts', schema='crm')
    op.drop_index('ix_crm_dev_contacts_last_updated', table_name='contacts', schema='crm')
    op.drop_index('ix_crm_dev_contacts_organization_id', table_name='contacts', schema='crm')
    op.create_index(op.f('ix_crm_contacts_created_at'), 'contacts', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contacts_created_by'), 'contacts', ['created_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contacts_is_sensitive'), 'contacts', ['is_sensitive'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contacts_last_updated'), 'contacts', ['last_updated'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contacts_organization_id'), 'contacts', ['organization_id'], unique=False, schema='crm')
    op.create_foreign_key(None, 'contacts', 'user', ['created_by'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'contacts', 'organizations', ['organization_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.alter_column('contract_uploads', 'id',
               existing_type=postgresql.UUID(),
               type_=sa.String(),
               existing_nullable=False,
               schema='crm')
    op.drop_index('ix_crm_dev_contract_uploads_contract_id', table_name='contract_uploads', schema='crm')
    op.drop_index('ix_crm_dev_contract_uploads_created_at', table_name='contract_uploads', schema='crm')
    op.drop_index('ix_crm_dev_contract_uploads_created_by', table_name='contract_uploads', schema='crm')
    op.drop_index('ix_crm_dev_contract_uploads_is_sensitive', table_name='contract_uploads', schema='crm')
    op.drop_index('ix_crm_dev_contract_uploads_last_updated', table_name='contract_uploads', schema='crm')
    op.drop_index('ix_crm_dev_contract_uploads_organization_id', table_name='contract_uploads', schema='crm')
    op.create_index(op.f('ix_crm_contract_uploads_contract_id'), 'contract_uploads', ['contract_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contract_uploads_created_at'), 'contract_uploads', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contract_uploads_created_by'), 'contract_uploads', ['created_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contract_uploads_is_sensitive'), 'contract_uploads', ['is_sensitive'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contract_uploads_last_updated'), 'contract_uploads', ['last_updated'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contract_uploads_organization_id'), 'contract_uploads', ['organization_id'], unique=False, schema='crm')
    op.create_foreign_key(None, 'contract_uploads', 'organizations', ['organization_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'contract_uploads', 'user', ['created_by'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'contract_uploads', 'contracts', ['contract_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.drop_index('ix_crm_dev_contracts_active_status', table_name='contracts', schema='crm')
    op.drop_index('ix_crm_dev_contracts_agent_id', table_name='contracts', schema='crm')
    op.drop_index('ix_crm_dev_contracts_created_at', table_name='contracts', schema='crm')
    op.drop_index('ix_crm_dev_contracts_created_by', table_name='contracts', schema='crm')
    op.drop_index('ix_crm_dev_contracts_end_date', table_name='contracts', schema='crm')
    op.drop_index('ix_crm_dev_contracts_is_sensitive', table_name='contracts', schema='crm')
    op.drop_index('ix_crm_dev_contracts_last_updated', table_name='contracts', schema='crm')
    op.drop_index('ix_crm_dev_contracts_organization_id', table_name='contracts', schema='crm')
    op.drop_index('ix_crm_dev_contracts_player_id', table_name='contracts', schema='crm')
    op.drop_index('ix_crm_dev_contracts_start_date', table_name='contracts', schema='crm')
    op.drop_index('ix_crm_dev_contracts_teamId', table_name='contracts', schema='crm')
    op.create_index(op.f('ix_crm_contracts_active_status'), 'contracts', ['active_status'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contracts_agent_id'), 'contracts', ['agent_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contracts_created_at'), 'contracts', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contracts_created_by'), 'contracts', ['created_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contracts_end_date'), 'contracts', ['end_date'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contracts_is_sensitive'), 'contracts', ['is_sensitive'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contracts_last_updated'), 'contracts', ['last_updated'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contracts_organization_id'), 'contracts', ['organization_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contracts_player_id'), 'contracts', ['player_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contracts_start_date'), 'contracts', ['start_date'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_contracts_teamId'), 'contracts', ['teamId'], unique=False, schema='crm')
    op.create_foreign_key(None, 'contracts', 'player_records', ['player_id'], ['id'], source_schema='crm', referent_schema='crm', ondelete='SET NULL')
    op.create_foreign_key(None, 'contracts', 'contacts', ['agent_id'], ['id'], source_schema='crm', referent_schema='crm', ondelete='SET NULL')
    op.create_foreign_key(None, 'contracts', 'organizations', ['organization_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'contracts', 'user', ['created_by'], ['id'], source_schema='crm', referent_schema='crm')
    op.drop_index('ix_crm_dev_player_features_player_id', table_name='player_features', schema='crm')
    op.create_index(op.f('ix_crm_player_features_player_id'), 'player_features', ['player_id'], unique=False, schema='crm')
    op.create_foreign_key(None, 'player_features', 'player_records', ['player_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.drop_index('ix_crm_dev_player_record_changes_edit_at', table_name='player_record_changes', schema='crm')
    op.drop_index('ix_crm_dev_player_record_changes_edit_by', table_name='player_record_changes', schema='crm')
    op.drop_index('ix_crm_dev_player_record_changes_player_id', table_name='player_record_changes', schema='crm')
    op.create_index(op.f('ix_crm_player_record_changes_edit_at'), 'player_record_changes', ['edit_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_record_changes_edit_by'), 'player_record_changes', ['edit_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_record_changes_player_id'), 'player_record_changes', ['player_id'], unique=False, schema='crm')
    op.create_foreign_key(None, 'player_record_changes', 'user', ['edit_by'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'player_record_changes', 'player_records', ['player_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.add_column('player_records', sa.Column('contact_channel', sa.ARRAY(sa.String()), nullable=True), schema='crm')
    op.drop_index('ix_crm_dev_player_records_assigned_to_id', table_name='player_records', schema='crm')
    op.drop_index('ix_crm_dev_player_records_created_at', table_name='player_records', schema='crm')
    op.drop_index('ix_crm_dev_player_records_created_by', table_name='player_records', schema='crm')
    op.drop_index('ix_crm_dev_player_records_is_sensitive', table_name='player_records', schema='crm')
    op.drop_index('ix_crm_dev_player_records_last_updated', table_name='player_records', schema='crm')
    op.drop_index('ix_crm_dev_player_records_organization_id', table_name='player_records', schema='crm')
    op.drop_index('ix_crm_dev_player_records_playerId', table_name='player_records', schema='crm')
    op.drop_index('ix_crm_dev_player_records_source_id', table_name='player_records', schema='crm')
    op.create_index(op.f('ix_crm_player_records_assigned_to_id'), 'player_records', ['assigned_to_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_records_created_at'), 'player_records', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_records_created_by'), 'player_records', ['created_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_records_is_sensitive'), 'player_records', ['is_sensitive'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_records_last_updated'), 'player_records', ['last_updated'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_records_organization_id'), 'player_records', ['organization_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_records_playerId'), 'player_records', ['playerId'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_records_source_id'), 'player_records', ['source_id'], unique=False, schema='crm')
    op.create_foreign_key(None, 'player_records', 'contacts', ['source_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'player_records', 'contacts', ['assigned_to_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'player_records', 'organizations', ['organization_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'player_records', 'user', ['created_by'], ['id'], source_schema='crm', referent_schema='crm')
    op.drop_index('ix_crm_dev_proposals_created_at', table_name='proposals', schema='crm')
    op.drop_index('ix_crm_dev_proposals_created_by', table_name='proposals', schema='crm')
    op.drop_index('ix_crm_dev_proposals_is_sensitive', table_name='proposals', schema='crm')
    op.drop_index('ix_crm_dev_proposals_last_updated', table_name='proposals', schema='crm')
    op.drop_index('ix_crm_dev_proposals_organization_id', table_name='proposals', schema='crm')
    op.drop_index('ix_crm_dev_proposals_player_id', table_name='proposals', schema='crm')
    op.drop_index('ix_crm_dev_proposals_request_id', table_name='proposals', schema='crm')
    op.create_index(op.f('ix_crm_proposals_created_at'), 'proposals', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_proposals_created_by'), 'proposals', ['created_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_proposals_is_sensitive'), 'proposals', ['is_sensitive'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_proposals_last_updated'), 'proposals', ['last_updated'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_proposals_organization_id'), 'proposals', ['organization_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_proposals_player_id'), 'proposals', ['player_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_proposals_request_id'), 'proposals', ['request_id'], unique=False, schema='crm')
    op.create_foreign_key(None, 'proposals', 'organizations', ['organization_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'proposals', 'team_requests', ['request_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'proposals', 'player_records', ['player_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'proposals', 'user', ['created_by'], ['id'], source_schema='crm', referent_schema='crm')
    op.drop_index('ix_crm_dev_purchases_active_until', table_name='purchases', schema='crm')
    op.drop_index('ix_crm_dev_purchases_created_at', table_name='purchases', schema='crm')
    op.drop_index('ix_crm_dev_purchases_module_id', table_name='purchases', schema='crm')
    op.drop_index('ix_crm_dev_purchases_organization_id', table_name='purchases', schema='crm')
    op.create_index(op.f('ix_crm_purchases_active_until'), 'purchases', ['active_until'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_purchases_created_at'), 'purchases', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_purchases_module_id'), 'purchases', ['module_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_purchases_organization_id'), 'purchases', ['organization_id'], unique=False, schema='crm')
    op.create_foreign_key(None, 'purchases', 'modules', ['module_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'purchases', 'organizations', ['organization_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'rank_outputs', 'rank_records', ['id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'rank_records', 'organizations', ['organization_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'rank_records', 'user', ['created_by'], ['id'], source_schema='crm', referent_schema='crm')
    op.drop_index('ix_crm_dev_reports_created_at', table_name='reports', schema='crm')
    op.drop_index('ix_crm_dev_reports_created_by', table_name='reports', schema='crm')
    op.drop_index('ix_crm_dev_reports_is_sensitive', table_name='reports', schema='crm')
    op.drop_index('ix_crm_dev_reports_last_updated', table_name='reports', schema='crm')
    op.drop_index('ix_crm_dev_reports_organization_id', table_name='reports', schema='crm')
    op.drop_index('ix_crm_dev_reports_player_id', table_name='reports', schema='crm')
    op.create_index(op.f('ix_crm_reports_created_at'), 'reports', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_reports_created_by'), 'reports', ['created_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_reports_is_sensitive'), 'reports', ['is_sensitive'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_reports_last_updated'), 'reports', ['last_updated'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_reports_organization_id'), 'reports', ['organization_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_reports_player_id'), 'reports', ['player_id'], unique=False, schema='crm')
    op.create_foreign_key(None, 'reports', 'player_records', ['player_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'reports', 'organizations', ['organization_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'reports', 'user', ['created_by'], ['id'], source_schema='crm', referent_schema='crm')
    op.drop_index('ix_crm_dev_team_request_changes_edit_at', table_name='team_request_changes', schema='crm')
    op.drop_index('ix_crm_dev_team_request_changes_edit_by', table_name='team_request_changes', schema='crm')
    op.drop_index('ix_crm_dev_team_request_changes_team_request_id', table_name='team_request_changes', schema='crm')
    op.create_index(op.f('ix_crm_team_request_changes_edit_at'), 'team_request_changes', ['edit_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_team_request_changes_edit_by'), 'team_request_changes', ['edit_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_team_request_changes_team_request_id'), 'team_request_changes', ['team_request_id'], unique=False, schema='crm')
    op.create_foreign_key(None, 'team_request_changes', 'team_requests', ['team_request_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'team_request_changes', 'user', ['edit_by'], ['id'], source_schema='crm', referent_schema='crm')
    op.drop_index('ix_crm_dev_team_requests_created_at', table_name='team_requests', schema='crm')
    op.drop_index('ix_crm_dev_team_requests_created_by', table_name='team_requests', schema='crm')
    op.drop_index('ix_crm_dev_team_requests_is_sensitive', table_name='team_requests', schema='crm')
    op.drop_index('ix_crm_dev_team_requests_last_updated', table_name='team_requests', schema='crm')
    op.drop_index('ix_crm_dev_team_requests_organization_id', table_name='team_requests', schema='crm')
    op.drop_index('ix_crm_dev_team_requests_teamId', table_name='team_requests', schema='crm')
    op.create_index(op.f('ix_crm_team_requests_created_at'), 'team_requests', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_team_requests_created_by'), 'team_requests', ['created_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_team_requests_is_sensitive'), 'team_requests', ['is_sensitive'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_team_requests_last_updated'), 'team_requests', ['last_updated'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_team_requests_organization_id'), 'team_requests', ['organization_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_team_requests_teamId'), 'team_requests', ['teamId'], unique=False, schema='crm')
    op.create_foreign_key(None, 'team_requests', 'organizations', ['organization_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'team_requests', 'user', ['created_by'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'team_requests', 'contacts', ['source_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.drop_index('ix_crm_dev_user_email', table_name='user', schema='crm')
    op.create_index(op.f('ix_crm_user_email'), 'user', ['email'], unique=True, schema='crm')
    op.create_foreign_key(None, 'user', 'organizations', ['organization_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_foreign_key(None, 'user', 'user_roles', ['role_id'], ['id'], source_schema='crm', referent_schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'user', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_user_email'), table_name='user', schema='crm')
    op.create_index('ix_crm_dev_user_email', 'user', ['email'], unique=False, schema='crm')
    op.drop_constraint(None, 'team_requests', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'team_requests', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'team_requests', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_team_requests_teamId'), table_name='team_requests', schema='crm')
    op.drop_index(op.f('ix_crm_team_requests_organization_id'), table_name='team_requests', schema='crm')
    op.drop_index(op.f('ix_crm_team_requests_last_updated'), table_name='team_requests', schema='crm')
    op.drop_index(op.f('ix_crm_team_requests_is_sensitive'), table_name='team_requests', schema='crm')
    op.drop_index(op.f('ix_crm_team_requests_created_by'), table_name='team_requests', schema='crm')
    op.drop_index(op.f('ix_crm_team_requests_created_at'), table_name='team_requests', schema='crm')
    op.create_index('ix_crm_dev_team_requests_teamId', 'team_requests', ['teamId'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_team_requests_organization_id', 'team_requests', ['organization_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_team_requests_last_updated', 'team_requests', ['last_updated'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_team_requests_is_sensitive', 'team_requests', ['is_sensitive'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_team_requests_created_by', 'team_requests', ['created_by'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_team_requests_created_at', 'team_requests', ['created_at'], unique=False, schema='crm')
    op.drop_constraint(None, 'team_request_changes', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'team_request_changes', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_team_request_changes_team_request_id'), table_name='team_request_changes', schema='crm')
    op.drop_index(op.f('ix_crm_team_request_changes_edit_by'), table_name='team_request_changes', schema='crm')
    op.drop_index(op.f('ix_crm_team_request_changes_edit_at'), table_name='team_request_changes', schema='crm')
    op.create_index('ix_crm_dev_team_request_changes_team_request_id', 'team_request_changes', ['team_request_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_team_request_changes_edit_by', 'team_request_changes', ['edit_by'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_team_request_changes_edit_at', 'team_request_changes', ['edit_at'], unique=False, schema='crm')
    op.drop_constraint(None, 'reports', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'reports', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'reports', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_reports_player_id'), table_name='reports', schema='crm')
    op.drop_index(op.f('ix_crm_reports_organization_id'), table_name='reports', schema='crm')
    op.drop_index(op.f('ix_crm_reports_last_updated'), table_name='reports', schema='crm')
    op.drop_index(op.f('ix_crm_reports_is_sensitive'), table_name='reports', schema='crm')
    op.drop_index(op.f('ix_crm_reports_created_by'), table_name='reports', schema='crm')
    op.drop_index(op.f('ix_crm_reports_created_at'), table_name='reports', schema='crm')
    op.create_index('ix_crm_dev_reports_player_id', 'reports', ['player_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_reports_organization_id', 'reports', ['organization_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_reports_last_updated', 'reports', ['last_updated'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_reports_is_sensitive', 'reports', ['is_sensitive'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_reports_created_by', 'reports', ['created_by'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_reports_created_at', 'reports', ['created_at'], unique=False, schema='crm')
    op.drop_constraint(None, 'rank_records', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'rank_records', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'rank_outputs', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'purchases', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'purchases', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_purchases_organization_id'), table_name='purchases', schema='crm')
    op.drop_index(op.f('ix_crm_purchases_module_id'), table_name='purchases', schema='crm')
    op.drop_index(op.f('ix_crm_purchases_created_at'), table_name='purchases', schema='crm')
    op.drop_index(op.f('ix_crm_purchases_active_until'), table_name='purchases', schema='crm')
    op.create_index('ix_crm_dev_purchases_organization_id', 'purchases', ['organization_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_purchases_module_id', 'purchases', ['module_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_purchases_created_at', 'purchases', ['created_at'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_purchases_active_until', 'purchases', ['active_until'], unique=False, schema='crm')
    op.drop_constraint(None, 'proposals', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'proposals', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'proposals', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'proposals', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_proposals_request_id'), table_name='proposals', schema='crm')
    op.drop_index(op.f('ix_crm_proposals_player_id'), table_name='proposals', schema='crm')
    op.drop_index(op.f('ix_crm_proposals_organization_id'), table_name='proposals', schema='crm')
    op.drop_index(op.f('ix_crm_proposals_last_updated'), table_name='proposals', schema='crm')
    op.drop_index(op.f('ix_crm_proposals_is_sensitive'), table_name='proposals', schema='crm')
    op.drop_index(op.f('ix_crm_proposals_created_by'), table_name='proposals', schema='crm')
    op.drop_index(op.f('ix_crm_proposals_created_at'), table_name='proposals', schema='crm')
    op.create_index('ix_crm_dev_proposals_request_id', 'proposals', ['request_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_proposals_player_id', 'proposals', ['player_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_proposals_organization_id', 'proposals', ['organization_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_proposals_last_updated', 'proposals', ['last_updated'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_proposals_is_sensitive', 'proposals', ['is_sensitive'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_proposals_created_by', 'proposals', ['created_by'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_proposals_created_at', 'proposals', ['created_at'], unique=False, schema='crm')
    op.drop_constraint(None, 'player_records', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'player_records', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'player_records', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'player_records', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_player_records_source_id'), table_name='player_records', schema='crm')
    op.drop_index(op.f('ix_crm_player_records_playerId'), table_name='player_records', schema='crm')
    op.drop_index(op.f('ix_crm_player_records_organization_id'), table_name='player_records', schema='crm')
    op.drop_index(op.f('ix_crm_player_records_last_updated'), table_name='player_records', schema='crm')
    op.drop_index(op.f('ix_crm_player_records_is_sensitive'), table_name='player_records', schema='crm')
    op.drop_index(op.f('ix_crm_player_records_created_by'), table_name='player_records', schema='crm')
    op.drop_index(op.f('ix_crm_player_records_created_at'), table_name='player_records', schema='crm')
    op.drop_index(op.f('ix_crm_player_records_assigned_to_id'), table_name='player_records', schema='crm')
    op.create_index('ix_crm_dev_player_records_source_id', 'player_records', ['source_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_player_records_playerId', 'player_records', ['playerId'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_player_records_organization_id', 'player_records', ['organization_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_player_records_last_updated', 'player_records', ['last_updated'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_player_records_is_sensitive', 'player_records', ['is_sensitive'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_player_records_created_by', 'player_records', ['created_by'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_player_records_created_at', 'player_records', ['created_at'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_player_records_assigned_to_id', 'player_records', ['assigned_to_id'], unique=False, schema='crm')
    op.drop_column('player_records', 'contact_channel', schema='crm')
    op.drop_constraint(None, 'player_record_changes', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'player_record_changes', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_player_record_changes_player_id'), table_name='player_record_changes', schema='crm')
    op.drop_index(op.f('ix_crm_player_record_changes_edit_by'), table_name='player_record_changes', schema='crm')
    op.drop_index(op.f('ix_crm_player_record_changes_edit_at'), table_name='player_record_changes', schema='crm')
    op.create_index('ix_crm_dev_player_record_changes_player_id', 'player_record_changes', ['player_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_player_record_changes_edit_by', 'player_record_changes', ['edit_by'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_player_record_changes_edit_at', 'player_record_changes', ['edit_at'], unique=False, schema='crm')
    op.drop_constraint(None, 'player_features', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_player_features_player_id'), table_name='player_features', schema='crm')
    op.create_index('ix_crm_dev_player_features_player_id', 'player_features', ['player_id'], unique=False, schema='crm')
    op.drop_constraint(None, 'contracts', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'contracts', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'contracts', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'contracts', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_contracts_teamId'), table_name='contracts', schema='crm')
    op.drop_index(op.f('ix_crm_contracts_start_date'), table_name='contracts', schema='crm')
    op.drop_index(op.f('ix_crm_contracts_player_id'), table_name='contracts', schema='crm')
    op.drop_index(op.f('ix_crm_contracts_organization_id'), table_name='contracts', schema='crm')
    op.drop_index(op.f('ix_crm_contracts_last_updated'), table_name='contracts', schema='crm')
    op.drop_index(op.f('ix_crm_contracts_is_sensitive'), table_name='contracts', schema='crm')
    op.drop_index(op.f('ix_crm_contracts_end_date'), table_name='contracts', schema='crm')
    op.drop_index(op.f('ix_crm_contracts_created_by'), table_name='contracts', schema='crm')
    op.drop_index(op.f('ix_crm_contracts_created_at'), table_name='contracts', schema='crm')
    op.drop_index(op.f('ix_crm_contracts_agent_id'), table_name='contracts', schema='crm')
    op.drop_index(op.f('ix_crm_contracts_active_status'), table_name='contracts', schema='crm')
    op.create_index('ix_crm_dev_contracts_teamId', 'contracts', ['teamId'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contracts_start_date', 'contracts', ['start_date'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contracts_player_id', 'contracts', ['player_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contracts_organization_id', 'contracts', ['organization_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contracts_last_updated', 'contracts', ['last_updated'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contracts_is_sensitive', 'contracts', ['is_sensitive'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contracts_end_date', 'contracts', ['end_date'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contracts_created_by', 'contracts', ['created_by'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contracts_created_at', 'contracts', ['created_at'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contracts_agent_id', 'contracts', ['agent_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contracts_active_status', 'contracts', ['active_status'], unique=False, schema='crm')
    op.drop_constraint(None, 'contract_uploads', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'contract_uploads', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'contract_uploads', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_contract_uploads_organization_id'), table_name='contract_uploads', schema='crm')
    op.drop_index(op.f('ix_crm_contract_uploads_last_updated'), table_name='contract_uploads', schema='crm')
    op.drop_index(op.f('ix_crm_contract_uploads_is_sensitive'), table_name='contract_uploads', schema='crm')
    op.drop_index(op.f('ix_crm_contract_uploads_created_by'), table_name='contract_uploads', schema='crm')
    op.drop_index(op.f('ix_crm_contract_uploads_created_at'), table_name='contract_uploads', schema='crm')
    op.drop_index(op.f('ix_crm_contract_uploads_contract_id'), table_name='contract_uploads', schema='crm')
    op.create_index('ix_crm_dev_contract_uploads_organization_id', 'contract_uploads', ['organization_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contract_uploads_last_updated', 'contract_uploads', ['last_updated'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contract_uploads_is_sensitive', 'contract_uploads', ['is_sensitive'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contract_uploads_created_by', 'contract_uploads', ['created_by'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contract_uploads_created_at', 'contract_uploads', ['created_at'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contract_uploads_contract_id', 'contract_uploads', ['contract_id'], unique=False, schema='crm')
    op.alter_column('contract_uploads', 'id',
               existing_type=sa.String(),
               type_=postgresql.UUID(),
               existing_nullable=False,
               schema='crm')
    op.drop_constraint(None, 'contacts', schema='crm', type_='foreignkey')
    op.drop_constraint(None, 'contacts', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_contacts_organization_id'), table_name='contacts', schema='crm')
    op.drop_index(op.f('ix_crm_contacts_last_updated'), table_name='contacts', schema='crm')
    op.drop_index(op.f('ix_crm_contacts_is_sensitive'), table_name='contacts', schema='crm')
    op.drop_index(op.f('ix_crm_contacts_created_by'), table_name='contacts', schema='crm')
    op.drop_index(op.f('ix_crm_contacts_created_at'), table_name='contacts', schema='crm')
    op.create_index('ix_crm_dev_contacts_organization_id', 'contacts', ['organization_id'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contacts_last_updated', 'contacts', ['last_updated'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contacts_is_sensitive', 'contacts', ['is_sensitive'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contacts_created_by', 'contacts', ['created_by'], unique=False, schema='crm')
    op.create_index('ix_crm_dev_contacts_created_at', 'contacts', ['created_at'], unique=False, schema='crm')
    # ### end Alembic commands ###