"""Add columns manually

Revision ID: c3272efdd564
Revises: b199f6a70b37
Create Date: 2023-08-29 13:39:37.917935

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c3272efdd564'
down_revision = 'b199f6a70b37'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('user', sa.Column('first_name', sa.String(), nullable=True), schema='crm_test')
    op.add_column('user', sa.Column('last_name', sa.String(), nullable=True), schema='crm_test')


def downgrade():
    op.drop_column('user', 'first_name', schema='crm_test')
    op.drop_column('user', 'last_name', schema='crm_test')