from app import models, crud
from fastapi import APIRouter, Depends, Query
from app.api import deps, utils
from sqlalchemy.orm import Session
from app.utils.matching_helpers import get_window_from_date

try:
    from ...config import settings
except:
    from app.config import settings
import pandas as pd
import numpy as np

router = APIRouter()

from typing import Any, List, Dict
import sqlalchemy

def ********************************(
    db: Session, player_id: int, team_ids: List[int]
) -> List[Dict[str, Any]]:
    """
    Given one player_id and a list of team_ids, returns a list of:
      [
        {"team_id": ..., "suitability_score": ..., "top5_avg_value": ...},
        ...
      ]

    """

    # If no teams, return empty
    if not team_ids:
        return []

    # Build a comma-separated string for the WHERE ... IN clause
    team_ids_str = ", ".join(str(t) for t in team_ids)

    # The raw SQL: two CTEs (player_qry and team_qry), then CROSS JOIN
    # to calculate the final `suitability_score` with financial factors and category filtering.
    raw_sql = f"""
    WITH player_qry AS (
        SELECT
            pi."playerId" AS player_id,
            cte.rating AS player_team_rating,
            ce.comp_median_rating AS player_comp_rating,
            pym.avg_mins_in_year,
            pi.current_value,
            cte.category AS player_team_category
        FROM wyscout.player_info2 pi
        LEFT JOIN wyscout.competition_teams cte
            ON pi."currentTeamId" = cte."teamId"
        LEFT JOIN wyscout.competition_ratings ce
            ON cte."competitionId" = ce."competitionId"
        LEFT JOIN wyscout.player_year_minutes pym
            ON pi."playerId" = pym."playerId"
        WHERE pi."playerId" = {player_id}
    ),
    team_qry AS (
        SELECT
            cte."teamId"           AS team_id,
            cte.rating             AS team_rating,
            ce.comp_median_rating  AS team_comp_rating,
            cte.category           AS team_category
        FROM wyscout.competition_teams cte
        LEFT JOIN wyscout.competition_ratings ce
            ON cte."competitionId" = ce."competitionId"
        WHERE cte."teamId" IN ({team_ids_str})
    ),
    top5_avg_values AS (
        SELECT
            team_id,
            AVG(current_value) as top5_avg_value
        FROM (
            SELECT
                "currentTeamId" as team_id,
                current_value,
                ROW_NUMBER() OVER (PARTITION BY "currentTeamId" ORDER BY current_value DESC) as rn
            FROM wyscout.player_info2
            WHERE current_value IS NOT NULL
            AND "currentTeamId" IN ({team_ids_str})
        ) ranked
        WHERE rn <= 5
        GROUP BY team_id
    )
    SELECT
        team_qry.team_id,
        -- Calculate base suitability components
        (
          (
            0.45 * (
              1 - LEAST(
                    ABS(player_qry.player_team_rating - team_qry.team_rating)
                     / team_qry.team_rating,
                    1
                  )
            )
            * (
              1 - (
                   ABS(player_qry.player_team_rating - team_qry.team_rating)
                   / team_qry.team_rating
                  )
            )
          )
          +
          (
            0.35 * (
              1 - LEAST(
                    ABS(player_qry.player_comp_rating - team_qry.team_comp_rating)
                     / team_qry.team_comp_rating,
                    1
                  )
            )
            * (
              1 - (
                   ABS(player_qry.player_comp_rating - team_qry.team_comp_rating)
                   / team_qry.team_comp_rating
                  )
            )
          )
          +
          (
            0.20 * (
              ( LEAST(70.0, COALESCE(player_qry.avg_mins_in_year, 0.0)) / 70.0 )
              * ( LEAST(70.0, COALESCE(player_qry.avg_mins_in_year, 0.0)) / 70.0 )
            )
          )
        )
        *
        (
          (
            0.45 * (
              1 - LEAST(
                    ABS(player_qry.player_team_rating - team_qry.team_rating)
                     / team_qry.team_rating,
                    1
                  )
            )
            * (
              1 - (
                   ABS(player_qry.player_team_rating - team_qry.team_rating)
                   / team_qry.team_rating
                  )
            )
          )
          +
          (
            0.35 * (
              1 - LEAST(
                    ABS(player_qry.player_comp_rating - team_qry.team_comp_rating)
                     / team_qry.team_comp_rating,
                    1
                  )
            )
            * (
              1 - (
                   ABS(player_qry.player_comp_rating - team_qry.team_comp_rating)
                   / team_qry.team_comp_rating
                  )
            )
          )
          +
          (
            0.20 * (
              ( LEAST(70.0, COALESCE(player_qry.avg_mins_in_year, 0.0)) / 70.0 )
              * ( LEAST(70.0, COALESCE(player_qry.avg_mins_in_year, 0.0)) / 70.0 )
            )
          )
        )
        -- Apply financial suitability factor using top 5 most valuable players
        * (
          1 - 0.6 * (
            1 - CASE
              WHEN player_qry.current_value IS NOT NULL
                   AND player_qry.current_value > 0
                   AND COALESCE(top5_avg_values.top5_avg_value, 0) > 0
              THEN LEAST(1.0, top5_avg_values.top5_avg_value / player_qry.current_value)
              ELSE 1.0
            END
          )
        )
        AS suitability_score,
        top5_avg_values.top5_avg_value
    FROM player_qry
    CROSS JOIN team_qry
    LEFT JOIN top5_avg_values ON team_qry.team_id = top5_avg_values.team_id
    -- Add category filtering condition
    WHERE NOT (
        player_qry.player_team_category = 'default'
        AND team_qry.team_category != 'default'
    )
    """

    # Execute the raw SQL
    rows = db.execute(sqlalchemy.text(raw_sql)).fetchall()

    # Convert to a list of dicts
    result = []
    for row in rows:
        result.append(
            {"team_id": row[0], "suitability_score": row[1], "top5_avg_value": row[2]}
        )
    return result


new_thresh = [
    np.inf,
    0.720042164,
    0.640061431,
    0.560080697,
    0.480099963,
    0.400119229,
    0.320138495,
    0.240157762,
    0.160177028,
    0.080196294 - np.inf,
]
new_thresh.reverse()

old_thresh = [
    np.inf,
    0.764995077,
    0.679995624,
    0.594996171,
    0.509996718,
    0.424997265,
    0.339997812,
    0.254998359,
    0.169998906,
    0.084999453,
    -np.inf,
]
old_thresh.reverse()

labels = [5, 4.5, 4, 3.5, 3, 2.5, 2, 1.5, 1, 0]
labels.reverse()
max_min_pct = 0.7

qry = """
select case when suitability_score_w_min_coalesce < 0.1 then 0
when suitability_score_w_min_coalesce between 0.1 and 0.2 then 1
when suitability_score_w_min_coalesce between 0.2 and 0.3 then 1.5
when suitability_score_w_min_coalesce between 0.3 and 0.4 then 2
when suitability_score_w_min_coalesce between 0.4 and 0.5 then 2.5
when suitability_score_w_min_coalesce between 0.5 and 0.6 then 3
when suitability_score_w_min_coalesce between 0.6 and 0.7 then 3.5
when suitability_score_w_min_coalesce between 0.7 and 0.8 then 4
when suitability_score_w_min_coalesce between 0.8 and 0.9 then 4.5
when suitability_score_w_min_coalesce > 0.9 then 5
else null end as suitability_rating,pr."playerId", tr."teamId" from {schema}.player_records pr
join {schema}.team_requests tr on 1=1
join wyscout.suit_scores ss on ss."playerId" = pr."playerId" and ss.hiring_team_id = tr."teamId" 
where pr.id = '{player_id}' and tr.id = '{request_id}'
"""

matching_qry = """
select * from (with suit_scores as (select ti3."officialName" as team_name, c."name", c."area_name", c."divisionLevel", tr.id as request_id, pr2.id as player_id, tr."position" as requested_pos, pr2."position" as player_pos, 
tr.transfer_period, pi3."firstName", pi3."lastName", tr."type", tr.max_value, tr.max_net_salary, ti3."teamId" as "teamId2",
case when tr.is_community and tr.organization_id != '{organization_id}' then true 
else false
end as is_community, 
(
        SELECT concat(c2.email, ', ', c2.contact_organization, ': ')
        FROM {schema}.source_to_record str
        JOIN {schema}.contacts c2 ON c2.id = str.source_id
        WHERE str.team_request_id = tr.id -- Filter by the specific team_request
        limit 1
    ) AS source, tr.last_updated, 
ss.suitability_score_w_min_coalesce as suitability_score
from {schema}.team_requests tr
join {schema}.player_records pr2 on pr2.id = '{player}'
join wyscout.player_info2 pi3 on pi3."playerId" = pr2."playerId" 
join wyscout.team_info2 ti3 on tr."teamId" = ti3."teamId" 
left join wyscout.suit_scores ss on pr2."playerId" = ss."playerId" and ss.hiring_team_id = ti3."teamId"
join wyscout.competition_teams ti on ti."teamId" = ti3."teamId"
join wyscout.competitions c on c."competitionId" = ti."competitionId"
where
(tr.organization_id = pr2.organization_id  or tr.is_community)
and tr."position" = ANY(pr2."position"::text[])
and '{window}' = ANY(tr.transfer_period)
)
select distinct on (ss."teamId2", "requested_pos", ss."transfer_period") *, 
case when suitability_score < 0.1 then 0
when suitability_score between 0.1 and 0.2 then 1
when suitability_score between 0.2 and 0.3 then 1.5
when suitability_score between 0.3 and 0.4 then 2
when suitability_score between 0.4 and 0.5 then 2.5
when suitability_score between 0.5 and 0.6 then 3
when suitability_score between 0.6 and 0.7 then 3.5
when suitability_score between 0.7 and 0.8 then 4
when suitability_score between 0.8 and 0.9 then 4.5
when suitability_score > 0.9 then 5
else null end as suitability_rating
from suit_scores ss
left join 
(
    SELECT
        "teamId",
        "position",
        transfer_period,
        (SELECT ARRAY_AGG(DISTINCT u.email)
        FROM unnest(ARRAY_AGG(DISTINCT created_by)) AS user_id
        JOIN {schema}.user u ON u.id = user_id) as created_by_emails,
        COUNT(*) as count_duplicates
    FROM
        {schema}.team_requests
    WHERE
        is_community = true
    GROUP BY
        "teamId",
        "position",
        transfer_period
) tc on ss."teamId2" = tc."teamId" and ss."requested_pos" = tc."position" and ss."transfer_period" = tc."transfer_period"
where 1=1)as subq
ORDER BY suitability_score DESC nulls last 
"""

players_qry = """with suit_scores as (select pi3.team_name, tr.id as request_id, pr2.id as player_id, tr."position" as requested_pos, pr2."position" as player_pos, pi3."firstName", pi3."lastName" ,
pi3."birthDate",pi3."birthArea_name" as birth_area, pi3.passport, pi3.eu , pr2.quality, pr2.potential, pr2.control_stage, pr2.transfer_period, pr2.club_asking_price, pr2.current_gross_salary, pi3.foot,
pi3.player_role, pi3.tm_value, pi3.agent, pr2.last_updated,pi3.player_url, pi3."shortName", pi3."playerId" ,(
        SELECT concat(c2.email, ', ', c2.contact_organization, ': ')
        FROM {schema}.source_to_record str
        JOIN {schema}.contacts c2 ON c2.id = str.source_id
        WHERE str.player_id = pr2.id -- Filter by the specific team_request
        limit 1
    ) AS source,
    case when tr.is_community and tr.organization_id != '{organization_id}' then true 
else false
end as is_community, ti3."officialName" as request_name, c."area_name", 
pr2.organization_id, ss.suitability_score_w_min_coalesce as suitability_score
from {schema}.player_records pr2
join {schema}.team_requests tr on tr.id = '{request_id}'
join wyscout.player_info2 pi3 on pi3."playerId" = pr2."playerId" 
join wyscout.team_info2 ti3 on tr."teamId" = ti3."teamId" 
left join wyscout.suit_scores ss on pr2."playerId" = ss."playerId" and ss.hiring_team_id = ti3."teamId"
join wyscout.competition_teams ti on ti."teamId" = ti3."teamId"
join wyscout.competitions c on c."competitionId" = ti."competitionId"
where tr."position" = ANY(pr2."position"::text[]) 
)
select *, 
case when suitability_score < 0.1 then 0
when suitability_score between 0.1 and 0.2 then 1
when suitability_score between 0.2 and 0.3 then 1.5
when suitability_score between 0.3 and 0.4 then 2
when suitability_score between 0.4 and 0.5 then 2.5
when suitability_score between 0.5 and 0.6 then 3
when suitability_score between 0.6 and 0.7 then 3.5
when suitability_score between 0.7 and 0.8 then 4
when suitability_score between 0.8 and 0.9 then 4.5
when suitability_score > 0.9 then 5
else null end as suitability_rating
from suit_scores
where 1=1 and organization_id = '{organization_id}'
order by suitability_score desc nulls last """


@router.get("/player_suitability")
def get_player_suitability_to_all(
    *,
    db: Session = Depends(deps.get_db),
    organization_id: str,
    # window: str,
    player_id: str,
):
    # qry2 = matching_qry.format_map(
    #     {
    #         "schema": settings.PG_SCHEMA,
    #         "window": window,
    #         "player": player_id,
    #         "organization_id": organization_id,
    #     }
    # )
    # out = db.execute(qry2).all()

    # return out

    return crud.suit_score_joined.get_matches_for_player(
        db,
        organization_id,
        player_id,
        #  , window
    )


@router.get("/request_suitability")
def get_request_suitability_to_all(
    *,
    db: Session = Depends(deps.get_db),
    request_id: str,
    organization_id: str,
):
    qry2 = players_qry.format_map(
        {
            "schema": settings.PG_SCHEMA,
            "request_id": request_id,
            "organization_id": organization_id,
            "max_mins_threshold": 90 * max_min_pct,
        }
    )
    out = db.execute(qry2).all()

    return out


@router.get("/player_request_suitability")
def get_player_suitability_to_one_request(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    request_id: str,
    player_id: str,
):
    # qry2 = qry.format_map({'schema': settings.PG_SCHEMA, 'request_id': request_id, 'player_id': player_id})
    # out = db.execute(qry2).all()

    # return out

    return crud.suit_score_joined.get_player_request_suitability(
        db, current_user.organization_id, request_id, player_id
    )
