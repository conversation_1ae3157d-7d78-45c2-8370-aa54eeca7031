from datetime import datetime
from typing import Optional
import uuid
from pydantic import BaseModel


class PurchaseUpdate(BaseModel):
    price: Optional[float]
    active_until: Optional[datetime]
    active: Optional[bool]
    module_id: Optional[uuid.UUID]
    organization_id: Optional[uuid.UUID]


class PurchaseCreate(PurchaseUpdate):
    price: float = 420
    active_until: datetime = datetime(2122, 1, 1)
    active: bool = True
    module_id: uuid.UUID
    organization_id: uuid.UUID


class Purchase(PurchaseCreate):
    id: uuid.UUID

    class Config:
        orm_mode = True