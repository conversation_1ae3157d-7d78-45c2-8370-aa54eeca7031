"""Add contract expiry to shadow players

Revision ID: 9aa04f67ad28
Revises: 872689e1ef5b
Create Date: 2025-05-15 17:18:34.701141

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9aa04f67ad28'
down_revision = '872689e1ef5b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_players', sa.Column('contract_expiry', sa.DateTime(), nullable=True), schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('field_players', 'contract_expiry', schema='crm')
    # ### end Alembic commands ###