from app.schemas.community_proposal import (
    CommunityProposal
)
from typing import Any

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app import crud, models
from app.api import deps, utils

router = APIRouter()

@router.get("/request/{request_id}")
def get_all_for_request(
    request_id: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve proposals.
    """
    utils.check_get_all(CommunityProposal, current_user)
    proposed_players = crud.community_proposal.get_all_proposals_to_a_request(
        db, request_id
    )

    return proposed_players

@router.get("/{request_id}/{player_id}")
def check_proposed_player(
    request_id: str,
    player_id: str,
    db: Session = Depends(deps.get_db),
):
    """
    Retrieve proposals to teamId.
    """
    proposals = crud.community_proposal.get_all_proposals_between_team_and_player(
        db=db, request_id=request_id, player_id=player_id
    )

    return proposals


@router.get("/player/{player_id}")
def get_all_for_player(
    player_id: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve proposals.
    """
    utils.check_get_all(CommunityProposal, current_user)
    proposed_players = crud.community_proposal.get_all_proposals_to_a_player(
        db, player_id
    )

    return proposed_players