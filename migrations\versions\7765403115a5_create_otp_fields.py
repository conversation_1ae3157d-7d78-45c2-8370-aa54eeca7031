"""Create OTP fields

Revision ID: 7765403115a5
Revises: b5e5d69f4a29
Create Date: 2023-11-15 14:56:15.145780

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7765403115a5'
down_revision = 'b5e5d69f4a29'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # op.create_table('suit_scores',
    # sa.Column('playerId', sa.Integer(), nullable=False),
    # sa.Column('hiring_team_id', sa.Integer(), nullable=False),
    # sa.Column('suitability_score_w_min_coalesce', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    # sa.PrimaryKeyConstraint('playerId', 'hiring_team_id'),
    # schema='wyscout'
    # )
    # op.create_index('idx_player_id_suit_1', 'suit_scores', ['playerId'], unique=False, schema='wyscout', postgresql_include=[])
    # op.create_index('idx_team_id_suit_1', 'suit_scores', ['hiring_team_id'], unique=False, schema='wyscout', postgresql_include=[])
    # op.create_index('suit_scores_playerid_idx_1', 'suit_scores', ['playerId', 'hiring_team_id'], unique=False, schema='wyscout', postgresql_include=[])
    # op.create_index('suit_scores_team_score_idx_1', 'suit_scores', ['hiring_team_id', sa.text('suitability_score_w_min_coalesce DESC NULLS LAST')], unique=False, schema='wyscout', postgresql_include=[])
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # op.drop_index('suit_scores_team_score_idx_1', table_name='suit_scores', schema='wyscout', postgresql_include=[])
    # op.drop_index('suit_scores_playerid_idx_1', table_name='suit_scores', schema='wyscout', postgresql_include=[])
    # op.drop_index('idx_team_id_suit_1', table_name='suit_scores', schema='wyscout', postgresql_include=[])
    # op.drop_index('idx_player_id_suit_1', table_name='suit_scores', schema='wyscout', postgresql_include=[])
    # op.drop_table('suit_scores', schema='wyscout')
    # ### end Alembic commands ###
    pass