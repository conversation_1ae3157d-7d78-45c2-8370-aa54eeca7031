from app import models, crud
from fastapi import APIRouter, Depends, Query
from app.api import deps, utils
from sqlalchemy.orm import Session
from app.utils.matching_helpers import get_window_from_date

try:
    from ...config import settings
except:
    from app.config import settings
import pandas as pd
import numpy as np

router = APIRouter()

new_thresh = [
    np.inf,
    0.720042164,
    0.640061431,
    0.560080697,
    0.480099963,
    0.400119229,
    0.320138495,
    0.240157762,
    0.160177028,
    0.080196294 - np.inf,
]
new_thresh.reverse()

old_thresh = [
    np.inf,
    0.764995077,
    0.679995624,
    0.594996171,
    0.509996718,
    0.424997265,
    0.339997812,
    0.254998359,
    0.169998906,
    0.084999453,
    -np.inf,
]
old_thresh.reverse()

labels = [5, 4.5, 4, 3.5, 3, 2.5, 2, 1.5, 1, 0]
labels.reverse()
max_min_pct = 0.7

qry = """
with suit_scores as (select ti3."officialName" as team_name, c."name", c."area_name", c."divisionLevel", tr.id as request_id, pr2.id as player_id, tr."position" as requested_pos, pr2."position" as player_pos, 
tr.transfer_period, pi3."firstName", pi3."lastName" , (1-abs(ti2.rating - ti.rating) / ti.rating ) team_difference, 
(1-abs(cr2.comp_median_rating - cr1.comp_median_rating)/cr1.comp_median_rating ) club_difference, (least(90, avg_mins_in_year)/90) minutes_pct


, ti.rating request_rating, ti2.rating player_rating
, cr1.comp_median_rating request_comp_median_rating, cr2.comp_median_rating player_comp_median_rating
, avg_mins_in_year


, (0.45*(1-abs(ti2.rating - ti.rating) / ti.rating )*(1-abs(ti2.rating - ti.rating) / ti.rating )
+ 0.35*(1-abs(cr2.comp_median_rating - cr1.comp_median_rating)/cr1.comp_median_rating )*(1-abs(cr2.comp_median_rating - cr1.comp_median_rating)/cr1.comp_median_rating )
+ 0.2*(least({max_mins_threshold}, avg_mins_in_year)/{max_mins_threshold})*(least({max_mins_threshold}, avg_mins_in_year)/{max_mins_threshold}))^2 as suitability_score


from {schema}.team_requests tr
, wyscout.competition_teams ti
, {schema}.player_records pr2
, wyscout.player_info2 pi3 
, wyscout.competition_teams ti2, wyscout.competition_ratings cr1
, wyscout.competition_ratings cr2
, wyscout.player_year_minutes pmp
, wyscout.team_info2 ti3 , wyscout.competitions c 
where 1=1
and tr."teamId" = ti."teamId" 
and (tr.organization_id = pr2.organization_id  or tr.is_community)
-- and tr."position" = ANY(pr2."position"::text[])
and pi3."playerId" = pr2."playerId" 
and pi3."currentTeamId" = ti2."teamId" 
and ti."competitionId" = cr1."competitionId"
and ti2."competitionId" = cr2."competitionId"
and pmp."playerId" = pi3."playerId"
and ti."teamId" = ti3."teamId"
and ti."competitionId" = c."competitionId" 
)

select *, 
case when suitability_score < 0.1 then 0
when suitability_score between 0.1 and 0.2 then 1
when suitability_score between 0.2 and 0.3 then 1.5
when suitability_score between 0.3 and 0.4 then 2
when suitability_score between 0.4 and 0.5 then 2.5
when suitability_score between 0.5 and 0.6 then 3
when suitability_score between 0.6 and 0.7 then 3.5
when suitability_score between 0.7 and 0.8 then 4
when suitability_score between 0.8 and 0.9 then 4.5
when suitability_score > 0.9 then 5
else null end as suitability_rating
from suit_scores
where 1=1
"""

matching_qry = """
select * from (with suit_scores as (select ti3."officialName" as team_name, c."name", c."area_name", c."divisionLevel", tr.id as request_id, pr2.id as player_id, tr."position" as requested_pos, pr2."position" as player_pos, 
tr.transfer_period, pi3."firstName", pi3."lastName" , (1-abs(ti2.rating - ti.rating) / ti.rating ) team_difference, tr."type", tr.max_value, tr.max_net_salary, tr."teamId" ,
case when tr.is_community and tr.organization_id != '{organization_id}' then true 
else false
end as is_community, 
(
        SELECT concat(c2.email, ', ', c2.contact_organization, ': ')
        FROM {schema}.source_to_record str
        JOIN {schema}.contacts c2 ON c2.id = str.source_id
        WHERE str.team_request_id = tr.id -- Filter by the specific team_request
        limit 1
    ) AS source, tr.last_updated, 
(1-abs(cr2.comp_median_rating - cr1.comp_median_rating)/cr1.comp_median_rating ) club_difference, (least(90, avg_mins_in_year)/90) minutes_pct
, ti.rating request_rating, ti2.rating player_rating
, cr1.comp_median_rating request_comp_median_rating, cr2.comp_median_rating player_comp_median_rating
, avg_mins_in_year
, (0.45*(1-abs(ti2.rating - ti.rating) / ti.rating )*(1-abs(ti2.rating - ti.rating) / ti.rating )
+ 0.35*(1-abs(cr2.comp_median_rating - cr1.comp_median_rating)/cr1.comp_median_rating )*(1-abs(cr2.comp_median_rating - cr1.comp_median_rating)/cr1.comp_median_rating )
+ 0.2*(least({max_mins_threshold}, avg_mins_in_year)/{max_mins_threshold})*(least({max_mins_threshold}, avg_mins_in_year)/{max_mins_threshold}))^2 as suitability_score
from {schema}.team_requests tr
, wyscout.competition_teams ti
, (select * from {schema}.player_records where 1=1
and id = '{player}') pr2
, wyscout.player_info2 pi3 
, wyscout.competition_teams ti2, wyscout.competition_ratings cr1
, wyscout.competition_ratings cr2
, wyscout.player_year_minutes pmp
, wyscout.team_info2 ti3 , wyscout.competitions c 
where 1=1
and tr."teamId" = ti."teamId" 
and (tr.organization_id = pr2.organization_id  or tr.is_community)
and tr."position" = ANY(pr2."position"::text[])
and '{window}' = ANY(tr.transfer_period)
and pi3."playerId" = pr2."playerId" 
and pi3."currentTeamId" = ti2."teamId" 
and ti."competitionId" = cr1."competitionId"
and ti2."competitionId" = cr2."competitionId"
and pmp."playerId" = pi3."playerId"
and ti."teamId" = ti3."teamId"
and ti."competitionId" = c."competitionId" 
)
select distinct on (ss."teamId", "requested_pos", ss."transfer_period") *, 
case when suitability_score < 0.1 then 0
when suitability_score between 0.1 and 0.2 then 1
when suitability_score between 0.2 and 0.3 then 1.5
when suitability_score between 0.3 and 0.4 then 2
when suitability_score between 0.4 and 0.5 then 2.5
when suitability_score between 0.5 and 0.6 then 3
when suitability_score between 0.6 and 0.7 then 3.5
when suitability_score between 0.7 and 0.8 then 4
when suitability_score between 0.8 and 0.9 then 4.5
when suitability_score > 0.9 then 5
else null end as suitability_rating
from suit_scores ss
left join 
(
    SELECT
        "teamId",
        "position",
        transfer_period,
        ARRAY_AGG(DISTINCT created_by) AS created_by_array,
        COUNT(*) as count_duplicates
    FROM
        {schema}.team_requests
    WHERE
        is_community = true
    GROUP BY
        "teamId",
        "position",
        transfer_period
) tc on ss."teamId" = tc."teamId" and ss."requested_pos" = tc."position" and ss."transfer_period" = tc."transfer_period"
where 1=1)as subq
ORDER BY suitability_score DESC
"""

players_qry = """with suit_scores as (select pi3.team_name, tr.id as request_id, pr2.id as player_id, tr."position" as requested_pos, pr2."position" as player_pos, pi3."firstName", pi3."lastName" ,
pi3."birthDate",pi3."birthArea_name" as birth_area, pi3.passport, pi3.eu , pr2.quality, pr2.potential, pr2.control_stage, pr2.transfer_period, pr2.club_asking_price, pr2.current_gross_salary, pi3.foot,
pi3.player_role, pi3.tm_value, pi3.agent, pr2.last_updated,pi3.player_url ,(
        SELECT concat(c2.email, ', ', c2.contact_organization, ': ')
        FROM {schema}.source_to_record str
        JOIN {schema}.contacts c2 ON c2.id = str.source_id
        WHERE str.player_id = pr2.id -- Filter by the specific team_request
        limit 1
    ) AS source,
    case when tr.is_community and tr.organization_id != '{organization_id}' then true 
else false
end as is_community, ti3."officialName" as request_name, c."area_name", 
(1-abs(ti2.rating - ti.rating) / ti.rating ) team_difference, pr2.organization_id ,
(1-abs(cr2.comp_median_rating - cr1.comp_median_rating)/cr1.comp_median_rating ) club_difference, (least(90, avg_mins_in_year)/90) minutes_pct
, ti.rating request_rating, ti2.rating player_rating
, cr1.comp_median_rating request_comp_median_rating, cr2.comp_median_rating player_comp_median_rating
, avg_mins_in_year
, (0.45*(1-abs(ti2.rating - ti.rating) / ti.rating )*(1-abs(ti2.rating - ti.rating) / ti.rating )
+ 0.35*(1-abs(cr2.comp_median_rating - cr1.comp_median_rating)/cr1.comp_median_rating )*(1-abs(cr2.comp_median_rating - cr1.comp_median_rating)/cr1.comp_median_rating )
+ 0.2*(least({max_mins_threshold}, avg_mins_in_year)/{max_mins_threshold})*(least({max_mins_threshold}, avg_mins_in_year)/{max_mins_threshold}))^2 as suitability_score
from {schema}.team_requests tr
, wyscout.competition_teams ti
, {schema}.player_records pr2
, wyscout.player_info2 pi3 
, wyscout.competition_teams ti2, wyscout.competition_ratings cr1
, wyscout.competition_ratings cr2
, wyscout.player_year_minutes pmp
, wyscout.team_info2 ti3 , wyscout.competitions c 
where 1=1
and tr."teamId" = ti."teamId" 
and tr."position" = ANY(pr2."position"::text[])
and pi3."playerId" = pr2."playerId" 
and pi3."currentTeamId" = ti2."teamId" 
and ti."competitionId" = cr1."competitionId"
and ti2."competitionId" = cr2."competitionId"
and pmp."playerId" = pi3."playerId"
and ti."teamId" = ti3."teamId"
and ti."competitionId" = c."competitionId" 
)
select *, 
case when suitability_score < 0.1 then 0
when suitability_score between 0.1 and 0.2 then 1
when suitability_score between 0.2 and 0.3 then 1.5
when suitability_score between 0.3 and 0.4 then 2
when suitability_score between 0.4 and 0.5 then 2.5
when suitability_score between 0.5 and 0.6 then 3
when suitability_score between 0.6 and 0.7 then 3.5
when suitability_score between 0.7 and 0.8 then 4
when suitability_score between 0.8 and 0.9 then 4.5
when suitability_score > 0.9 then 5
else null end as suitability_rating
from suit_scores
where 1=1
and request_id = '{request_id}' and organization_id = '{organization_id}'
order by suitability_score desc"""


@router.get("/player_suitability")
def get_player_suitability_to_all(
    *,
    db: Session = Depends(deps.get_db),
    player_id: str,
    organization_id: str,
    window: str = get_window_from_date(),
):
    qry2 = matching_qry.format_map(
        {
            "schema": settings.PG_SCHEMA,
            "window": window,
            "player": player_id,
            "organization_id": organization_id,
             'max_mins_threshold': 90*max_min_pct
        }
    )
    out = db.execute(qry2).all()

    return out


@router.get("/request_suitability")
def get_request_suitability_to_all(
    *,
    db: Session = Depends(deps.get_db),
    request_id: str,
    organization_id: str,
):
    qry2 = players_qry.format_map({'schema': settings.PG_SCHEMA, 'request_id': request_id, 'organization_id': organization_id, 'max_mins_threshold': 90*max_min_pct})
    out = db.execute(qry2).all()

    return out


@router.get("/player_request_suitability")
def get_player_suitability_to_one_request(
    *,
    db: Session = Depends(deps.get_db),
    request_id: str,
    player_id: str,
):
    qry2 = qry.format_map({'schema': settings.PG_SCHEMA, 'max_mins_threshold': 90*max_min_pct})
    qry2 += f" and request_id = \'{request_id}\' and player_id = \'{player_id}\'"
    out = db.execute(qry2).all()
    return out
