"""Date deal created + new stages

Revision ID: 2a3e88e56ff7
Revises: e0999b48ec3a
Create Date: 2024-12-06 14:57:38.129976

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2a3e88e56ff7'
down_revision = 'e0999b48ec3a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('activity', sa.Column('date_deal_created', sa.DateTime(), nullable=True), schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('activity', 'date_deal_created', schema='crm')
    # ### end Alembic commands ###