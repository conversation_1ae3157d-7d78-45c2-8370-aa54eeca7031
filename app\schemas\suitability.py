from pydantic import BaseModel
from app.schemas.user import UserShort
from app.schemas.contract import ContractShort

class SuitScore(BaseModel):
    playerId: int
    hiring_team_id: int
    suitability_score_w_min_coalesce: float

    class Config:
        orm_mode = True

class MostSuitablePlayer(BaseModel):
    playerId: int
    hiring_team_id: int
    suitability_score_w_min_coalesce: float
    primary_ws_position: str
    secondary_ws_position: str

    class Config:
        orm_mode = True