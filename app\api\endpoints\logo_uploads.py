import traceback
import uuid
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Response
from sqlalchemy.orm import Session

from app.schemas.logo_upload import LogoUpload, LogoUploadCreate
from app import crud, models
from app.api import deps, utils
from app.utils.cloud_storage import CloudStorageManager
from starlette.responses import StreamingResponse

router = APIRouter()


@router.get("/file_info/", response_model=List[LogoUpload])
def read_football_field(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all files' info.
    """
    utils.check_get_all(LogoUpload, current_user)
    return crud.football_field.get_all_w_org(
        db, current_user.organization_id, utils.can_access_sensitive(current_user)
    )


@router.get("/file_info/{id}", response_model=LogoUpload)
def get_file_info(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get file info by ID.
    """
    logo_upload = crud.logo_upload.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_get_one(logo_upload, current_user)
    return logo_upload


@router.get(
    "/download/{id}",
)
def download_file(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get contact by ID.
    """
    # first check if user can actually access the logo file:
    logo_upload = crud.logo_upload.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_get_one(logo_upload, current_user)
    # if all is ok, download file
    cm = CloudStorageManager()
    media_type = {
        "jpg": "image/jpeg",
        "jpeg": "image/jpeg",
        "png": "image/png",
        "pdf": "application/pdf",
    }.get(id.split(".")[-1])
    return Response(cm.get_blob(id), media_type=media_type)


@router.post(
    "/bulk/{football_field_id}",
    response_description="Upload multiple files at once",
    response_model=List[LogoUpload],
)
def upload_bulk_files(
    *, 
    db: Session = Depends(deps.get_db),
    football_field_id: str,
    files: List[UploadFile] = File(),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    utils.check_create(LogoUpload, current_user)
    files_for_writing = {}
    for f in files:
            ext = f.filename.split(".")[-1]
            if ext not in {"pdf", "png", "jpeg", "jpg"}:
                raise HTTPException(415, "Unsupported media type")
            filename = f"{str(uuid.uuid4())}.{ext}"
            files_for_writing[filename] = (LogoUploadCreate(football_field_id=football_field_id, id=filename), f)
    written_files = crud.logo_upload.bulk_create_with_user(
        db=db, objs_in=[x[0] for x in files_for_writing.values()], user=current_user
    )
    try:
        cm = CloudStorageManager()
        for k, v in files_for_writing.items():
            cm.upload_blob(k, v[1].file)
        return written_files
    except Exception as e:
        print(e)
        for k in files_for_writing:
            crud.logo_upload.remove(db=db, id=k)

        raise HTTPException(500, "Failed to upload document")


@router.delete("/{id}", response_model=LogoUpload)
def delete_logo_upload(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a Logo uppload.
    """
    logo_upload = crud.logo_upload.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_delete(logo_upload, current_user)
    logo_upload_out = LogoUpload.from_orm(logo_upload)
    crud.logo_upload.remove(db=db, id=id)
    try:
        cm = CloudStorageManager()
        cm.delete_blob(logo_upload.id)
        return logo_upload_out
    except:
        logo_upload = crud.logo_upload.create_with_user(
            db=db,
            obj_in=logo_upload_out,
            user=current_user,
        )
        traceback.format_exc()
        raise HTTPException(500, "Failed to delete document")
