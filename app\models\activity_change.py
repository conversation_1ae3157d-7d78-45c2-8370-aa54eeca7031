from sqlalchemy import Column, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from app.db.base_class import Base
from app.models.change_base_mixin import ChangeBaseMixin
from app.config import settings


class ActivityChange(Base, ChangeBaseMixin):
    __tablename__ = "activity_change"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    activity_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.activity.id"),
        index=True,
    )
    # player = relationship("PlayerRecord", back_populates="changelog")
