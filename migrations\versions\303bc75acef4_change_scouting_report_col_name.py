"""change scouting report col name

Revision ID: 303bc75acef4
Revises: 95f1c660052a
Create Date: 2023-04-24 16:14:56.666986

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '303bc75acef4'
down_revision = '95f1c660052a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reports', sa.Column('reported_rank', sa.String(), nullable=True), schema='crm_dev')
    op.drop_column('reports', 'model_ranks', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reports', sa.Column('model_ranks', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_dev')
    op.drop_column('reports', 'reported_rank', schema='crm_dev')
    # ### end Alembic commands ###