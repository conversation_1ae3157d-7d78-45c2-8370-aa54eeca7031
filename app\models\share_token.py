from sqlalchemy import Column, Inte<PERSON>, String, Boolean, DateTime
from sqlalchemy.orm import declarative_base
import datetime
import uuid
from app.db.base_class import Base
from app.config import settings
from sqlalchemy.dialects.postgresql import UUID

class ShareToken(Base):
    __tablename__ = "share_tokens"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    id = Column(UUID(as_uuid=True), primary_key=True, index=True, default=uuid.uuid4)
    resource_type = Column(String(50), nullable=False)
    resource_id = Column(UUID(as_uuid=True), nullable=False)
    token = Column(UUID(as_uuid=True), unique=True, nullable=False, default=uuid.uuid4)
    expires_at = Column(DateTime, nullable=False)
    is_disabled = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.now)
    hide_field_view = Column(Boolean, default=False)
