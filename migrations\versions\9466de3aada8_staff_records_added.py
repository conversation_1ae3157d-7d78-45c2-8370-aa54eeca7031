"""Staff records added

Revision ID: 9466de3aada8
Revises: 579e50434321
Create Date: 2025-02-11 13:48:38.321382

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '9466de3aada8'
down_revision = '579e50434321'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('staff_records',
    sa.Column('staff_id', sa.Integer(), nullable=True),
    sa.Column('current_salary', sa.Float(), nullable=True),
    sa.Column('expected_salary', sa.Float(), nullable=True),
    sa.Column('early_termination_fee', sa.Float(), nullable=True),
    sa.Column('licenses', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('languages', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('control_stage', sa.String(), nullable=True),
    sa.Column('transfer_strategy', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('roles', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('regions_of_interest', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('roles_of_interest', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('linked_tm_members', postgresql.ARRAY(sa.Integer()), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', sa.UUID(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_dev.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_dev.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_staff_records_created_at'), 'staff_records', ['created_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_staff_records_created_by'), 'staff_records', ['created_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_staff_records_is_sensitive'), 'staff_records', ['is_sensitive'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_staff_records_last_updated'), 'staff_records', ['last_updated'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_staff_records_organization_id'), 'staff_records', ['organization_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_staff_records_staff_id'), 'staff_records', ['staff_id'], unique=False, schema='crm_dev')
    op.create_table('staff_uploads',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('staff_id', sa.UUID(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', sa.UUID(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_dev.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_dev.organizations.id'], ),
    sa.ForeignKeyConstraint(['staff_id'], ['crm_dev.staff_records.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_staff_uploads_created_at'), 'staff_uploads', ['created_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_staff_uploads_created_by'), 'staff_uploads', ['created_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_staff_uploads_is_sensitive'), 'staff_uploads', ['is_sensitive'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_staff_uploads_last_updated'), 'staff_uploads', ['last_updated'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_staff_uploads_organization_id'), 'staff_uploads', ['organization_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_staff_uploads_staff_id'), 'staff_uploads', ['staff_id'], unique=False, schema='crm_dev')
    op.add_column('activity', sa.Column('staff_id', sa.Integer(), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_activity_staff_id'), 'activity', ['staff_id'], unique=False, schema='crm_dev')
    op.add_column('assigned_to_record', sa.Column('staff_record_id', sa.UUID(), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_assigned_to_record_staff_record_id'), 'assigned_to_record', ['staff_record_id'], unique=False, schema='crm_dev')
    op.create_foreign_key(None, 'assigned_to_record', 'staff_records', ['staff_record_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.add_column('comments_activity', sa.Column('staff_id', sa.UUID(), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_comments_activity_staff_id'), 'comments_activity', ['staff_id'], unique=False, schema='crm_dev')
    op.create_foreign_key(None, 'comments_activity', 'staff_records', ['staff_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.add_column('contracts', sa.Column('staff_id', sa.UUID(), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contracts_staff_id'), 'contracts', ['staff_id'], unique=False, schema='crm_dev')
    op.create_foreign_key(None, 'contracts', 'staff_records', ['staff_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', ondelete='SET NULL')
    op.add_column('user', sa.Column('phone_number', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('user', sa.Column('use_whatsapp', sa.Boolean(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'use_whatsapp', schema='crm_dev')
    op.drop_column('user', 'phone_number', schema='crm_dev')
    op.drop_constraint(None, 'contracts', schema='crm_dev', type_='foreignkey')
    op.drop_index(op.f('ix_crm_dev_contracts_staff_id'), table_name='contracts', schema='crm_dev')
    op.drop_column('contracts', 'staff_id', schema='crm_dev')
    op.drop_constraint(None, 'comments_activity', schema='crm_dev', type_='foreignkey')
    op.drop_index(op.f('ix_crm_dev_comments_activity_staff_id'), table_name='comments_activity', schema='crm_dev')
    op.drop_column('comments_activity', 'staff_id', schema='crm_dev')
    op.drop_constraint(None, 'assigned_to_record', schema='crm_dev', type_='foreignkey')
    op.drop_index(op.f('ix_crm_dev_assigned_to_record_staff_record_id'), table_name='assigned_to_record', schema='crm_dev')
    op.drop_column('assigned_to_record', 'staff_record_id', schema='crm_dev')
    op.drop_constraint(None, 'activity', schema='crm_dev', type_='foreignkey')
    op.drop_index(op.f('ix_crm_dev_activity_staff_id'), table_name='activity', schema='crm_dev')
    op.drop_column('activity', 'staff_id', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_staff_uploads_staff_id'), table_name='staff_uploads', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_staff_uploads_organization_id'), table_name='staff_uploads', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_staff_uploads_last_updated'), table_name='staff_uploads', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_staff_uploads_is_sensitive'), table_name='staff_uploads', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_staff_uploads_created_by'), table_name='staff_uploads', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_staff_uploads_created_at'), table_name='staff_uploads', schema='crm_dev')
    op.drop_table('staff_uploads', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_staff_records_staff_id'), table_name='staff_records', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_staff_records_organization_id'), table_name='staff_records', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_staff_records_last_updated'), table_name='staff_records', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_staff_records_is_sensitive'), table_name='staff_records', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_staff_records_created_by'), table_name='staff_records', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_staff_records_created_at'), table_name='staff_records', schema='crm_dev')
    op.drop_table('staff_records', schema='crm_dev')
    # ### end Alembic commands ###