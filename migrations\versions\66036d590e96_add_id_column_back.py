"""add id column back

Revision ID: 66036d590e96
Revises: 5d183fafbb5e
Create Date: 2023-07-07 13:56:23.200513

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '66036d590e96'
down_revision = '5d183fafbb5e'
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###