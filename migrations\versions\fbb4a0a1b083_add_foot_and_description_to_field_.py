"""Add foot and description to field players

Revision ID: fbb4a0a1b083
Revises: 9aa04f67ad28
Create Date: 2025-05-20 17:15:22.874611

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fbb4a0a1b083'
down_revision = '9aa04f67ad28'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_players', sa.Column('foot', sa.String(), nullable=True), schema='crm_test')
    op.add_column('field_players', sa.Column('description', sa.String(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('field_players', 'description', schema='crm_test')
    op.drop_column('field_players', 'foot', schema='crm_test')
    # ### end Alembic commands ###