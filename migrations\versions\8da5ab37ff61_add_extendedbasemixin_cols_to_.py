"""add extendedbasemixin cols to contractupload

Revision ID: 8da5ab37ff61
Revises: 311193345e27
Create Date: 2022-09-21 09:07:42.458404

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '8da5ab37ff61'
down_revision = '311193345e27'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contract_uploads', sa.Column('created_at', sa.DateTime(), nullable=True), schema='crm_dev')
    op.add_column('contract_uploads', sa.Column('last_updated', sa.DateTime(), nullable=True), schema='crm_dev')
    op.add_column('contract_uploads', sa.Column('is_sensitive', sa.<PERSON>(), nullable=True), schema='crm_dev')
    op.add_column('contract_uploads', sa.Column('notes', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('contract_uploads', sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True), schema='crm_dev')
    op.add_column('contract_uploads', sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contract_uploads_created_at'), 'contract_uploads', ['created_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contract_uploads_created_by'), 'contract_uploads', ['created_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contract_uploads_is_sensitive'), 'contract_uploads', ['is_sensitive'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contract_uploads_last_updated'), 'contract_uploads', ['last_updated'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contract_uploads_organization_id'), 'contract_uploads', ['organization_id'], unique=False, schema='crm_dev')
    op.create_foreign_key(None, 'contract_uploads', 'organizations', ['organization_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.create_foreign_key(None, 'contract_uploads', 'user', ['created_by'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'contract_uploads', schema='crm_dev', type_='foreignkey')
    op.drop_constraint(None, 'contract_uploads', schema='crm_dev', type_='foreignkey')
    op.drop_index(op.f('ix_crm_dev_contract_uploads_organization_id'), table_name='contract_uploads', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contract_uploads_last_updated'), table_name='contract_uploads', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contract_uploads_is_sensitive'), table_name='contract_uploads', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contract_uploads_created_by'), table_name='contract_uploads', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contract_uploads_created_at'), table_name='contract_uploads', schema='crm_dev')
    op.drop_column('contract_uploads', 'organization_id', schema='crm_dev')
    op.drop_column('contract_uploads', 'created_by', schema='crm_dev')
    op.drop_column('contract_uploads', 'notes', schema='crm_dev')
    op.drop_column('contract_uploads', 'is_sensitive', schema='crm_dev')
    op.drop_column('contract_uploads', 'last_updated', schema='crm_dev')
    op.drop_column('contract_uploads', 'created_at', schema='crm_dev')
    # ### end Alembic commands ###