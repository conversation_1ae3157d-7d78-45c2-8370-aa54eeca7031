"""Change request_creator to be an Array - 3

Revision ID: 88cc2913eb9b
Revises: bf948bca1cc9
Create Date: 2023-09-11 13:49:23.479478

"""
from alembic import op
from sqlalchemy.dialects import postgresql
from sqlalchemy import String

# revision identifiers, used by Alembic.
revision = '88cc2913eb9b'
down_revision = 'bf948bca1cc9'
branch_labels = None
depends_on = None


def upgrade():
    # Alter the column to be ARRAY(String) and cast it explicitly
    op.execute('ALTER TABLE crm_test.community_proposals ALTER COLUMN request_creator TYPE character varying[] USING request_creator::character varying[]')

def downgrade():
    # Alter the column back to its original type if needed
    op.execute('ALTER TABLE crm_test.community_proposals ALTER COLUMN request_creator TYPE character varying[]')