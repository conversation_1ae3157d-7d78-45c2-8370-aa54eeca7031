from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON><PERSON>, String, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from fastapi_users_db_sqlalchemy import SQLAlchemyBaseUserTableUUID

from app.db.base_class import Base
from app.config import settings

class User(SQLAlchemyBaseUserTableUUID, Base):
    __table_args__ = {"schema": settings.PG_SCHEMA}

    is_enabled = Column(Boolean, default=False)
    organization_id = Column(
        UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.organizations.id")
    )
    organization = relationship("Organization", back_populates="users", lazy="joined")
    role_id = Column(
        UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.user_roles.id")
    )
    role = relationship("UserRole", back_populates="users", lazy="joined")
    first_name = Column(String, default="")
    last_name = Column(String, default="")
    accept_terms = Column(Boolean, default=False)
    accept_marketing_emails = Column(Boolean, default=False)
    date_created = Column(DateTime, default=datetime.now)
    phone_number = Column(String, default="")
    use_whatsapp = Column(Boolean, default=False)
    # otp_enabled = Column(Boolean, default = False)
    # otp_url = Column(String)
    # otp_base32 = Column(String)
    # otp_verified = Column(Boolean, default = False)


class UserDefaultRoles(Base):
    __tablename__ = "user_roles_default"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    
    user_id = Column(
        UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.user.id"), primary_key=True
    )
    role_id = Column(
        UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.user_roles.id")
    )
    role = relationship("UserRole", back_populates="user_roles_default", lazy="joined")
