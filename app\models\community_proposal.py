from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Float, String
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, backref
from app.models.extended_base_mixin import ExtendedBaseMixin
from app.db.base_class import Base
from app.config import settings
from sqlalchemy.schema import UniqueConstraint


class CommunityProposal(Base, ExtendedBaseMixin):
    __tablename__ = "community_proposals"
    __table_args__ = __table_args__ = (UniqueConstraint('request_id', 'player_id', 'organization_id', name='_community_request_player_org_uc_new'), {"schema": settings.PG_SCHEMA})


    club_asking_price = Column(Float)
    expected_salary = Column(Float)
    are_you_the_agent = Column(String)
    description = Column(String)
    request_creator = Column(ARRAY(String))
    position = Column(ARRAY(String))
    foot = Column(String)
    player_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.player_records.id"),
        index=True,
    )
    request_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.team_requests.id"),
        index=True,
    )
    player_records = relationship("PlayerRecord", backref=backref("community_proposals", cascade="all, delete-orphan"))
    team_requests = relationship("TeamRequest", backref=backref("community_proposals", cascade="all, delete-orphan"))
    creator = relationship("User")
    video_link = Column(String)