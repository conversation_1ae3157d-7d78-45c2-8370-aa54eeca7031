"""Unique constraint

Revision ID: 5d183fafbb5e
Revises: ba15a4f3a6b3
Create Date: 2023-07-07 13:52:07.811681

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5d183fafbb5e'
down_revision = 'ba15a4f3a6b3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('notification_settings',
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('contract_notifications', sa.<PERSON>(), nullable=True),
    sa.Column('player_notifications', sa.<PERSON>(), nullable=True),
    sa.ForeignKeyConstraint(['player_id'], ['crm_dev.player_records.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['crm_dev.user.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'player_id'),
    sa.UniqueConstraint('user_id', 'player_id', name='_user_player_uc')
    )
    op.create_index(op.f('ix_notification_settings_player_id'), 'notification_settings', ['player_id'], unique=False)
    op.create_index(op.f('ix_notification_settings_user_id'), 'notification_settings', ['user_id'], unique=False)
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    op.drop_index(op.f('ix_notification_settings_user_id'), table_name='notification_settings')
    op.drop_index(op.f('ix_notification_settings_player_id'), table_name='notification_settings')
    op.drop_table('notification_settings')
    # ### end Alembic commands ###