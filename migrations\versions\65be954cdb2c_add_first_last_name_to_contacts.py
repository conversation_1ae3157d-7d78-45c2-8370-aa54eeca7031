"""add first last name to contacts

Revision ID: 65be954cdb2c
Revises: 8b2351dd34fd
Create Date: 2022-08-03 14:05:02.770923

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '65be954cdb2c'
down_revision = '8b2351dd34fd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contacts', sa.Column('first_name', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('contacts', sa.Column('last_name', sa.String(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('contacts', 'last_name', schema='crm_dev')
    op.drop_column('contacts', 'first_name', schema='crm_dev')
    # ### end Alembic commands ###