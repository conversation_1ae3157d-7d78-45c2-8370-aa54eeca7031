"""Add contact fields to squads

Revision ID: a3221f0902ea
Revises: 14fb06a836f9
Create Date: 2025-05-28 12:21:32.941966

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a3221f0902ea'
down_revision = '14fb06a836f9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('football_fields', sa.Column('contact_name', sa.String(), nullable=True), schema='crm_test')
    op.add_column('football_fields', sa.Column('contact_email', sa.String(), nullable=True), schema='crm_test')
    op.add_column('football_fields', sa.Column('contact_phone', sa.String(), nullable=True), schema='crm_test')
    op.add_column('request_fields', sa.Column('contact_name', sa.String(), nullable=True), schema='crm_test')
    op.add_column('request_fields', sa.Column('contact_email', sa.String(), nullable=True), schema='crm_test')
    op.add_column('request_fields', sa.Column('contact_phone', sa.String(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('request_fields', 'contact_phone', schema='crm_test')
    op.drop_column('request_fields', 'contact_email', schema='crm_test')
    op.drop_column('request_fields', 'contact_name', schema='crm_test')
    op.drop_column('football_fields', 'contact_phone', schema='crm_test')
    op.drop_column('football_fields', 'contact_email', schema='crm_test')
    op.drop_column('football_fields', 'contact_name', schema='crm_test')
    # ### end Alembic commands ###