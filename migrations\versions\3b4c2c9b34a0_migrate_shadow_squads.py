"""Migrate shadow squads

Revision ID: 3b4c2c9b34a0
Revises: 9e552157d160
Create Date: 2023-10-20 11:18:55.290053

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '3b4c2c9b34a0'
down_revision = '9e552157d160'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('football_fields',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('club_name', sa.String(), nullable=True),
    sa.Column('teamId', sa.Integer(), nullable=True),
    sa.Column('for_date', sa.DateTime(), nullable=True),
    sa.Column('formation', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_dev.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_dev.organizations.id'], ),
    # sa.ForeignKeyConstraint(['teamId'], ['wyscout.team_info2.teamId'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_football_fields_created_at'), 'football_fields', ['created_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_football_fields_created_by'), 'football_fields', ['created_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_football_fields_is_sensitive'), 'football_fields', ['is_sensitive'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_football_fields_last_updated'), 'football_fields', ['last_updated'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_football_fields_name'), 'football_fields', ['name'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_football_fields_organization_id'), 'football_fields', ['organization_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_football_fields_teamId'), 'football_fields', ['teamId'], unique=False, schema='crm_dev')
    op.create_table('field_players',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('field_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('playerId', sa.Integer(), nullable=True),
    sa.Column('field_position', sa.String(), nullable=True),
    sa.Column('suitability_score', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['field_id'], ['crm_dev.football_fields.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_field_players_field_id'), 'field_players', ['field_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_field_players_playerId'), 'field_players', ['playerId'], unique=False, schema='crm_dev')
    op.create_table('logo_upload',
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('football_field_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_dev.user.id'], ),
    sa.ForeignKeyConstraint(['football_field_id'], ['crm_dev.football_fields.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_dev.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_logo_upload_created_at'), 'logo_upload', ['created_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_logo_upload_created_by'), 'logo_upload', ['created_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_logo_upload_football_field_id'), 'logo_upload', ['football_field_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_logo_upload_is_sensitive'), 'logo_upload', ['is_sensitive'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_logo_upload_last_updated'), 'logo_upload', ['last_updated'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_logo_upload_organization_id'), 'logo_upload', ['organization_id'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_dev_logo_upload_organization_id'), table_name='logo_upload', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_logo_upload_last_updated'), table_name='logo_upload', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_logo_upload_is_sensitive'), table_name='logo_upload', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_logo_upload_football_field_id'), table_name='logo_upload', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_logo_upload_created_by'), table_name='logo_upload', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_logo_upload_created_at'), table_name='logo_upload', schema='crm_dev')
    op.drop_table('logo_upload', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_field_players_playerId'), table_name='field_players', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_field_players_field_id'), table_name='field_players', schema='crm_dev')
    op.drop_table('field_players', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_football_fields_teamId'), table_name='football_fields', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_football_fields_organization_id'), table_name='football_fields', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_football_fields_name'), table_name='football_fields', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_football_fields_last_updated'), table_name='football_fields', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_football_fields_is_sensitive'), table_name='football_fields', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_football_fields_created_by'), table_name='football_fields', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_football_fields_created_at'), table_name='football_fields', schema='crm_dev')
    op.drop_table('football_fields', schema='crm_dev')
    # ### end Alembic commands ###