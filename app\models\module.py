import uuid

from sqlalchemy import Column, String
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from app.db.base_class import Base
from app.config import settings


class Module(Base):
    __tablename__ = "modules"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String)
    description = Column(String)
    organizations = relationship("Purchase", back_populates="module")
