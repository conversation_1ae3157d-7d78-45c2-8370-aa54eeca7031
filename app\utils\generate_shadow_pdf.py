
from weasyprint import HTML
from jinja2 import Template


def generate_pdf(players_data, field_data):
    template_str = """
<!DOCTYPE html>
<html>
<head>
<style>
@font-face {
  font-family: 'Questrial';
  src: url('https://fonts.googleapis.com/css2?family=Questrial&display=swap') format('truetype');
}
@page {
    size: 805pt 1015pt;
    margin: 0;
}
.logo{
  width: 286px;
  height: 140px;
  position: relative;
  left: 420px;
  top: 516px;
  opacity: 0.4;
}
body{
  margin: 0px;
}
h1,h2,h3, p {
  font-family: 'Questrial', sans-serif;
}
.field {
  position: relative;
  background: rgb(18, 18, 18);
  color: white;
  width: 1130px;
  height: 1430px;
  margin: 0;
  transform-origin: top left;
}
.player {
  position: absolute;
  background: #2c2c2e;
  border-radius: 8px;
  padding: 3px 6px;
  width: 180px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-family: 'Questrial', sans-serif;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
  color: white;
  overflow: hidden;
}

.player-image-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 32px;
  height: 32px;
  margin-right: 4px;
}

.player-img {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  object-fit: cover;
}

.position-label {
  position: absolute;
  bottom: -6px;
  left: 0;
  right: 0;
  background: #444;
  color: white;
  font-size: 8px;
  border-radius: 8px;
  text-align: center;
  white-space: nowrap;
  height: 12px;
  line-height: 12px;
  z-index: 2;
}

.player-details {
  flex-grow: 1;
  max-width: 155px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.player-name {
  font-size: 10px;
  margin: 0;
  line-height: 1.2;
}

.info-line {
  font-size: 8.5px;
  color: #ccc;
  margin: 0;
  line-height: 1.2;
}

.player-flags {
  display: flex;
  flex-direction: column;
  gap: 1px;
  margin-left: auto;
}

.flag {
  height: 10px;
}
.divider {
    width: 2px;
    position: absolute;
    height: 80px;
    background-color: #ffffff42;
    top: 1180px;
    left: 563px;
}
.enskai { position: absolute;
    width: 85px;
    top: 1180px;
    left: 445px; }
.benskai { position: absolute;
    width: 85px;
    top: 1180px;
    left: 521px; }
.team-name { position: absolute; width: 600px; top: 10px; left: 107px;  overflow: hidden; white-space: nowrap; text-overflow: ellipsis;  margin-top: 5px;}
.team-img { position: absolute;
    width: 100px;
    top: 1170px;
    left: 591px; }
.pitch { position: absolute;  width: 1130px; height: 1170px; }
.club-name { position: absolute; max-width: 300px; top: 960px; left: 400px; }
.names, .values { height: 60px; }
body p, .player p, .names p, .values p, a p {
    text-decoration: none !important;
}
.age, .tf-s, .name { width: 55px; margin-right: 0px ; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
p {font-size: 10px; text-decoration: initial; }
.player-img-small { width: 40px; height: 50px; margin-left: -5px}

.contact-info {
  position: absolute;
  width: 400px;
  top: 1300px;
  left: 365px;
  text-align: center;
  color: #cccccc;
  font-family: 'Questrial';
}
</style>
</head>
<body>
<div class="field">
  {% if field_data.teamImg %}
  <a href='https://ensk.ai' target='_blank'><img src='https://26951654.fs1.hubspotusercontent-eu1.net/hubfs/26951654/logomark_white.png' class="enskai" /></a>
   <div class="divider"></div>
    <img src='{{field_data.teamImg}}' class="team-img"/>
  {% else %}
  <a href='https://ensk.ai' target='_blank'><img src='https://26951654.fs1.hubspotusercontent-eu1.net/hubfs/26951654/logomark_white.png' class="benskai" /></a>
  {% endif %}
  <h3 class="team-name" >{{field_data.name}}</h3>
   <img src='http://testing.enskai.com/svg-igrishte.svg' class="pitch" />
  {% for player in players %}
    <a href="{{ player.link }}" style="text-decoration:none;" target="_blank">
      <div class="player" style="top: {{ player.y_px }}; left: {{ player.x_px }};">
        <div class="player-image-wrapper">
          <img class="player-img" src="{{ player.playerImg }}" />
          {% if player.position %}
          <div class="position-label">{{ player.position }}</div>
          {% endif %}
        </div>
        <div class="player-details">
          <p class="player-name"><strong>{{ player.name }}</strong></p>
          <p class="info-line">TF: {{ player.transfer_fee }} | S: {{ player.salary }} | Age: {{ player.age }}</p>
        </div>
        <div class="player-flags">
          <img class="flag" src="{{ player.passport }}"/>
          {% if player.passport != player.nationality %}
          <img class="flag" src="{{ player.nationality }}"/>
          {% endif %}
        </div>
      </div>
    </a>
  {% endfor %}
  
  {% if field_data.contact_name or field_data.contact_email or field_data.contact_phone %}
  <div class="contact-info">
    {% set contacts = [] %}
    {% if field_data.contact_name %}{% set _ = contacts.append(field_data.contact_name) %}{% endif %}
    {% if field_data.contact_email %}{% set _ = contacts.append(field_data.contact_email) %}{% endif %}
    {% if field_data.contact_phone %}{% set _ = contacts.append(field_data.contact_phone) %}{% endif %}
    {{ " | ".join(contacts) }}
  </div>
  {% endif %}
</div>
</body>
</html>
    """
    template = Template(template_str)
    html_content = template.render(players=players_data, field_data=field_data)

    pdf_document = HTML(
        string=html_content
    ).write_pdf()  # This will return a byte stream

    return pdf_document

def generate_request_pdf(requests_data, field_data):
    template_str = """
<!DOCTYPE html>
<html>
<head>
<style>
@font-face {
  font-family: 'Questrial';
  src: url('https://fonts.googleapis.com/css2?family=Questrial&display=swap') format('truetype');
}
@page {
    size: 805pt 1015pt;
    margin: 0;
}
.logo{
  width: 286px;
  height: 140px;
  position: relative;
  left: 420px;
  top: 516px;
  opacity: 0.4;
}
body{
  margin: 0px;
}
h1,h2,h3, p {
  font-family: 'Questrial', sans-serif;
}
.field {
  position: relative;
  background: rgb(18, 18, 18);
  color: white;
  width: 1130px;
  height: 1430px;
  margin: 0;
  transform-origin: top left;
}
.request {
  position: absolute;
  background: #2c2c2e;
  border-radius: 8px;
  padding: 3px 6px;
  width: 180px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-family: 'Questrial', sans-serif;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
  color: white;
  overflow: hidden;
}

.request-image-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 32px;
  height: 32px;
  margin-right: 8px;
}

.request-img {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  object-fit: cover;
}

.position-label {
  position: absolute;
  bottom: -3px;
  left: -5px;
  right: 0;
  background: #444;
  color: white;
  font-size: 8px;
  border-radius: 8px;
  text-align: center;
  white-space: nowrap;
  height: 12px;
  line-height: 12px;
  z-index: 2;
}

.request-details {
  flex-grow: 1;
  max-width: 155px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.request-name {
  font-size: 10px;
  margin: 0;
  line-height: 1.2;
}

.info-line {
  font-size: 8.5px;
  color: #ccc;
  margin: 0;
  line-height: 1.2;
}

.request-flags {
  display: flex;
  flex-direction: column;
  gap: 1px;
  margin-left: auto;
}

.flag {
  height: 8.5px;
}
.divider {
    width: 2px;
    position: absolute;
    height: 80px;
    background-color: #ffffff42;
    top: 1180px;
    left: 563px;
}
.enskai { position: absolute;
    width: 85px;
    top: 1180px;
    left: 445px; }
.benskai { position: absolute;
    width: 85px;
    top: 1180px;
    left: 521px; }
.team-name { position: absolute; width: 600px; top: 10px; left: 107px;  overflow: hidden; white-space: nowrap; text-overflow: ellipsis;  margin-top: 5px;}
.team-img { position: absolute;
    width: 100px;
    top: 1170px;
    left: 591px; }
.pitch { position: absolute;  width: 1130px; height: 1170px; }
.club-name { position: absolute; max-width: 300px; top: 960px; left: 400px; }
.names, .values { height: 60px; }
body p, .request p, .names p, .values p, a p {
    text-decoration: none !important;
}
.age, .tf-s, .name { width: 55px; margin-right: 0px ; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
p {font-size: 10px; text-decoration: initial; }
.request-img-small { width: 40px; height: 50px; margin-left: -5px}

.contact-info {
  position: absolute;
  width: 400px;
  top: 1300px;
  left: 365px;
  text-align: center;
  color: #cccccc;
  font-family: 'Questrial';
}
</style>
</head>
<body>
<div class="field">
  <a href='https://ensk.ai' target='_blank'><img src='https://26951654.fs1.hubspotusercontent-eu1.net/hubfs/26951654/logomark_white.png' class="benskai" /></a>
  <h3 class="team-name" >{{field_data.name}}</h3>
   <img src='http://testing.enskai.com/svg-igrishte.svg' class="pitch" />
  {% for request in requests %}
      <div class="request" style="top: {{ request.y_px }}; left: {{ request.x_px }};">
        <div class="request-image-wrapper">
          {% if not request.is_team_hidden %}
          <img class="request-img" src="{{ request.teamImg }}" />
          {% else %}
            <img class="request-img" src="{{ request.area_name }}"/>
          {% endif %}
          {% if request.position %}
          <div class="position-label">{{ request.position }}</div>
          {% endif %}
        </div>
        <div class="request-details">
          <p class="request-name"><strong>
            {% if request.is_team_hidden %}
              {% if request.division_level %}{{ request.area_name_text }} | D{{ request.division_level }}{% else %}{{ request.area_name_text }}{% endif %}
            {% else %}
              {{ request.name }}
            {% endif %}
          </strong></p>
          <p class="info-line">TF: {{ request.transfer_fee }} | S: {{ request.salary }}</p>
        </div>
        <div class="request-flags">
        {% if not request.is_team_hidden %}
          <img class="flag" src="{{ request.area_name }}"/>
        {% endif %}
        {% if request.is_eu %}
          <img class="flag" src="{{ request.is_eu }}"/>
          {% endif %}
        </div>
      </div>
  {% endfor %}
    {% if field_data.contact_name or field_data.contact_email or field_data.contact_phone %}
  <div class="contact-info">
    {% set contacts = [] %}
    {% if field_data.contact_name %}{% set _ = contacts.append(field_data.contact_name) %}{% endif %}
    {% if field_data.contact_email %}{% set _ = contacts.append(field_data.contact_email) %}{% endif %}
    {% if field_data.contact_phone %}{% set _ = contacts.append(field_data.contact_phone) %}{% endif %}
    {{ " | ".join(contacts) }}
  </div>
  {% endif %}
</div>
</body>
</html>
    """
    template = Template(template_str)
    html_content = template.render(requests=requests_data, field_data=field_data)

    pdf_document = HTML(
        string=html_content
    ).write_pdf()  # This will return a byte stream

    return pdf_document
