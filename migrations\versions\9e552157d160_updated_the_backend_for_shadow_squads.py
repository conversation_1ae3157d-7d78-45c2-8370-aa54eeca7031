"""Updated the backend for shadow squads

Revision ID: 9e552157d160
Revises: d25547246b5c
Create Date: 2023-10-16 14:22:04.275163

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '9e552157d160'
down_revision = 'd25547246b5c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('logo_upload',
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.<PERSON>an(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('football_field_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['football_field_id'], ['crm_test.football_fields.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_logo_upload_created_at'), 'logo_upload', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_logo_upload_created_by'), 'logo_upload', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_logo_upload_football_field_id'), 'logo_upload', ['football_field_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_logo_upload_is_sensitive'), 'logo_upload', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_logo_upload_last_updated'), 'logo_upload', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_logo_upload_organization_id'), 'logo_upload', ['organization_id'], unique=False, schema='crm_test')
    op.add_column('field_players', sa.Column('field_position', sa.String(), nullable=True), schema='crm_test')
    op.add_column('football_fields', sa.Column('teamId', sa.Integer(), nullable=True), schema='crm_test')
    op.create_index(op.f('ix_crm_test_football_fields_teamId'), 'football_fields', ['teamId'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_test_football_fields_teamId'), table_name='football_fields', schema='crm_test')
    op.drop_column('football_fields', 'teamId', schema='crm_test')
    op.drop_column('field_players', 'field_position', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_logo_upload_organization_id'), table_name='logo_upload', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_logo_upload_last_updated'), table_name='logo_upload', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_logo_upload_is_sensitive'), table_name='logo_upload', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_logo_upload_football_field_id'), table_name='logo_upload', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_logo_upload_created_by'), table_name='logo_upload', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_logo_upload_created_at'), table_name='logo_upload', schema='crm_test')
    op.drop_table('logo_upload', schema='crm_test')
    # ### end Alembic commands ###