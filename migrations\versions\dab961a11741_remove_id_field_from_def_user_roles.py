"""Remove id field from def user roles

Revision ID: dab961a11741
Revises: 19fc322edac9
Create Date: 2024-04-05 15:30:49.895962

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'dab961a11741'
down_revision = '19fc322edac9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user_roles_default', 'user_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_test')
    op.drop_column('user_roles_default', 'id', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_roles_default', sa.Column('id', postgresql.UUID(), autoincrement=False, nullable=False), schema='crm_test')
    op.alter_column('user_roles_default', 'user_id',
               existing_type=postgresql.UUID(),
               nullable=True,
               schema='crm_test')
    # ### end Alembic commands ###