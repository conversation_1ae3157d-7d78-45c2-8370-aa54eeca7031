from typing import Optional
from pydantic import BaseModel

from app.schemas.enums import Foot


class PlayerAggregateStats(BaseModel):
    playerId: int
    num_games_last_3years: Optional[int]
    total_minutes_last_3years: Optional[int]
    num_games_last_1year: Optional[int]
    total_minutes_last_1year: Optional[int]
    priority: Optional[int]
    priority_reason: Optional[str]
    likely_agent_contract_expiry: Optional[int]
    speed_estimate: Optional[str]
    expected_transfer_value: Optional[float]


    class Config:
        orm_mode = True