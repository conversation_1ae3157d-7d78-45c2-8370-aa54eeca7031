from app.db.base import Mandate, ClubContract, RepresentationAgreement, CommissionAgreement
from app.db.session import session_maker
import datetime


def main(org_id: str, user_id: str, player_id: str):

    db = session_maker()

    end_date = datetime.datetime.now() + datetime.timedelta(days=365)
    start_date = datetime.datetime.now()

    contracts = [
        {
            "contract_type": "mandate",
            "coverage": ["Bulgaria", "Germany", "France", "Spain"],
        },
        {
            "contract_type": "representation_agreement",
            "exclusive": True,
            "termination_fee": 10 ** 5,
            "pct_commission_agreed": 20, 
            "registered_in": "Bulgaria"
        },
        {
            "contract_type": "commission_agreement",
            "installments": [
                {"due_date": "2022-12-01", "amount": 10**5},
                {"due_date": "2023-06-01", "amount": 30**5},

            ]
        },
        {
            "contract_type": "club_contract",
            "gross_salary": 10**6,
            "signing_fee": 5*10**5,
            "goal_bonus": 10**4,
            "assist_bonus": 2* 10**3,
            "matches_played_bonus": 2* 10**4,
            "minimum_fee_release_clause": 3*10**6,
            "option_years": 2
        }
    ]
    for c in contracts:
        model = next(
        filter(
            lambda x: x.__mapper_args__["polymorphic_identity"]
            == c["contract_type"],
            {
                Mandate,
                RepresentationAgreement,
                ClubContract,
                CommissionAgreement,
            },
        ),
        None,
    )
        db.add(
            model(
                **c,
                start_date=start_date,
                end_date=end_date,
                organization_id=org_id,
                created_by=user_id,
                active_status=True,
                currency="USD",
                player_id=player_id,
            )
        )
    db.commit()


if __name__ == "__main__":
    org_id = "ebfdb98e-9af4-4ba0-a437-4ce1997132e1"
    user_id = "3abadf30-c4da-4c23-bd34-aba058872c8e"
    player_id = "df1b8ca0-aa83-454b-8529-d8e471cdc161"

    main(org_id, user_id, player_id)