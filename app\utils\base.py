from typing import Union
from sqlalchemy import create_engine
import uuid
import requests
from sqlalchemy.sql import text
from datetime import datetime
from passlib.hash import bcrypt
from typing import Union
from app.models.user import User
from app.models.user_role import UserRole
from app.models.organization import Organization
from app.models.purchase import Purchase
from app.models.module import Module
from app.schemas.player_record import PlayerRecordCreate
from app.schemas.team_request import TeamRequestCreate
from app import crud
from app.db.session import session_maker
try:
    from app.config import settings
except:
    from ...app.config import settings

import pandas as pd


def add_purchases_decorator(func, modules: list = None):
    def wrapper(*args, **kwargs):
        (org_uuid, schema) = func(*args, **kwargs)
        add_purchases(org_uuid, schema, modules)

    return wrapper


# Not supplying a module will get them all
def add_purchases(
    org_uuid: uuid, schema: str = settings.PG_SCHEMA, modules: list = None
):
    if modules is None:
        modules = []
    engine = create_engine(settings.PG_URL)
    if not modules:
        dd = pd.read_sql(f"SELECT id from {schema}.modules", engine)
    else:
        print(
            f"Adding following modules to organization: {tuple(modules)}"
            .replace(",)", ")")
        )
        dd = pd.read_sql(
            f"select * from {schema}.modules where name in {tuple(modules)}"
            .replace(",)", ")"),
            engine,
        )

    # Inserting the actual purchases
    # values = [
    #     f"""('{uuid.uuid4()}', '{org_uuid}', '{module_id}', '{datetime.now()}',
    #   '420', '2122-08-20 10:34:16.452', True)"""
    #     for module_id in dd["id"]
    # ]

    row_ids = [uuid.uuid4() for module_id in dd["id"]]
    if dd.shape[0] > 1:
        df = pd.DataFrame(
            {
                "id": row_ids,
                "organization_id": str(org_uuid),
                "module_id": dd["id"].values,
                "created_at": datetime.now(),
                "price": 420,
                "active_until": "2122-08-20 10:34:16.452",
                "active": True,
            }
        )
    else:
        df = pd.DataFrame(
            {
                "id": row_ids,
                "organization_id": str(org_uuid),
                "module_id": dd["id"].values,
                "created_at": datetime.now(),
                "price": 420,
                "active_until": "2122-08-20 10:34:16.452",
                "active": True,
            },
            index=[0],
        )

    df = df.drop_duplicates(subset=["module_id", "organization_id"])

    df.to_sql(
        "purchases",
        schema=schema,
        con=engine,
        if_exists="append",
        index=False,
        method="multi",
    )

    #  with engine.connect() as conn:
    #     text(
    #         f"""INSERT INTO {schema}.purchases (id, organization_id, module_id,
    #         created_at, price, active_until, active) VALUES {', '.join(values)}"""
    #     )
    # )
    # engine.dispose()


# @add_purchases_decorator # Add purchases while adding the organization
def create_org(
    name: str,
    password: str,
    schema=settings.PG_SCHEMA,
    modules: list = None,
    org_uuid=None,
):
    print(
        f"Creating the org with name: {name}. Warning: Organizations with"
        " duplicate names are allowed."
    )
    engine = create_engine(settings.PG_URL)
    if org_uuid is None:
        org_uuid = uuid.uuid4()
    # print(org_uuid)
    # You can either supply a schema or use the default one
    df = pd.DataFrame(
        {"id": str(org_uuid), "name": name, "password": password}, index=[0]
    )

    df.head(1).to_sql(
        "organizations",
        schema=schema,
        con=engine,
        if_exists="append",
        index=False,
        method="multi",
    )

    org_uuid = df.head(1).id.values[0]
    add_purchases(org_uuid, modules=modules)

    # Postgres adds ghost records somehow, deleting those
    with engine.connect() as conn:
        conn.execute(
        text(f"""delete from {schema}.organizations where "name" = '{name}' and "id" not in (select "organization_id" from {schema}.purchases) """)
    )

    return org_uuid


def create_user(
    email: str,
    pwd: str,
    is_active: bool,
    is_superuser: bool,
    is_verified: bool,
    is_enabled: bool,
    org_id: uuid.UUID,
    role: str,
    schema=settings.PG_SCHEMA,
    id=None,
):
    print(f"Creating the user with email: {email}")
    hash_pwd = bcrypt.hash(pwd)
    if id is None:
        id = uuid.uuid4()
    engine = create_engine(settings.PG_URL)

    roles = pd.read_sql(
        f"""select * from {schema}.user_roles where "name"='{role}' """, engine
    )
    role_id = roles.id.values[0]

    df = pd.DataFrame(
        {
            "email": email,
            "hashed_password": hash_pwd,
            "is_active": is_active,
            "is_superuser": is_superuser,
            "is_verified": is_verified,
            "id": str(id),
            "is_enabled": is_enabled,
            "organization_id": str(org_id),
            "role_id": str(role_id),
        },
        index=[0],
    )
    df.head(1).to_sql(
        "user",
        schema=schema,
        con=engine,
        if_exists="append",
        index=False,
        method="multi",
    )

    engine.dispose()

    return email, id, role_id, role, org_id, is_enabled


def create_role(role: str, descr: str, schema=settings.PG_SCHEMA, id=None):
    if id is None:
        id = uuid.uuid4()
    engine = create_engine(settings.PG_URL)

    df = pd.DataFrame(
        {"id": str(id), "name": role, "description": descr}, index=[0]
    )
    df.head(1).to_sql(
        "user_roles",
        schema=schema,
        con=engine,
        if_exists="append",
        index=False,
        method="multi",
    )

    engine.dispose()

    return id, role


def delete_org(
    org_id: Union[str, uuid.UUID] = None, name=None, schema=settings.PG_SCHEMA
):
    if (org_id is None) & (name is None):
        ValueError("Either id or organization name must be not null")
    engine = create_engine(settings.PG_URL)

    # Deleting from user first because no delete cascade specified
    if name is None:
        with engine.connect() as conn:
            conn.execute(
            text(
                f""" delete from {schema}.user where "organization_id"='{str(org_id)}'
                """
            )
        )
        with engine.connect() as conn:
            conn.execute(
            text(
                f""" delete from {schema}.purchases where "organization_id"='{str(org_id)}'
                """
            )
        )
        with engine.connect() as conn:
            conn.execute(
            text(
                f""" delete from {schema}.organizations where "id"='{str(org_id)}'
                """
            )
        )
    else:
        if org_id is not None:
            with engine.connect() as conn:
                conn.execute(
                text(
                    f""" delete from {schema}.user where "organization_id"='{str(org_id)}' or "organization_id" in (select "id" from {schema}.organizations where "name" ='{name}')
                    """
                )
            )

            with engine.connect() as conn:
                conn.execute(
                text(
                    f""" delete from {schema}.purchases where "organization_id"='{str(org_id)}' or "organization_id" in (select "id" from {schema}.organizations where "name" ='{name}')
                    """
                )
            )

            with engine.connect() as conn:
                conn.execute(
                text(
                    f""" delete from {schema}.organizations where "id"='{str(org_id)}' or "organization_id" in (select "id" from {schema}.organizations where "name" ='{name}')
                    """
                )
            )
        else:
            with engine.connect() as conn:
                conn.execute(
                text(
                    f""" delete from {schema}.user where "organization_id" in (select "id" from {schema}.organizations where "name" ='{name}')
                    """
                )
            )

            with engine.connect() as conn:
                conn.execute(
                text(
                    f""" delete from {schema}.purchases where "organization_id" in (select "id" from {schema}.organizations where "name" ='{name}')
                    """
                )
            )

            with engine.connect() as conn:
                conn.execute(
                text(
                    f""" delete from {schema}.organizations where "id" in (select "id" from {schema}.organizations where "name" ='{name}')
                    """
                )
            )

    engine.dispose()


def delete_ranks(id: Union[str, uuid.UUID], schema=settings.PG_SCHEMA):
    engine = create_engine(settings.PG_URL)

    # Deleting from user first because no delete cascade specified
    if id:
        with engine.connect() as conn:
            conn.execute(
            text(
                f""" delete from {schema}.rank_outputs where "id"='{str(id)}'
                """
            )
        )

        with engine.connect() as conn:
            conn.execute(
            text(
                f""" delete from {schema}.rank_records where "id"='{str(id)}'
                """
            )
        )


def delete_user(
    id: Union[str, uuid.UUID] = None, schema=settings.PG_SCHEMA, email=None
):
    engine = create_engine(settings.PG_URL)

    # Deleting from user first because no delete cascade specified
    if id:
        with engine.connect() as conn:
            conn.execute(
            text(
                f""" delete from {schema}.user where "organization_id"='{str(id)}'
                """
            )
        )

    if email:
        with engine.connect() as conn:
            conn.execute(
            text(
                f""" delete from {schema}.user where "email"='{str(email)}'
                """
            )
        )


def update_modules_of_orgs(
    org_id: Union[str, uuid.UUID],
    schema=settings.PG_SCHEMA,
    modules: list = None,
    append=False,
):
    """
    If modules is None, deletes all purchases
    """
    engine = create_engine(settings.PG_URL)

    if append:
        print(
            "Append is 'True'. Specified modules will be ADDED to the existing"
            " ones."
        )
        modules_df = pd.read_sql(
            f"""select "name" from {schema}.modules where "id" in (select "module_id" from {schema}.purchases where "organization_id" = '{str(org_id)}')""",
            engine,
        )
        modules_old = modules_df.name
        modules = list(set([*modules, *modules_old]))

        with engine.connect() as conn:
            conn.execute(
            text(
                f"""
                delete from {schema}.purchases where "organization_id" = '{str(org_id)}'
                """
            )
        )

        print(list(modules_old))
        print(modules)
    else:
        print(
            "Append is 'False'. All existing modules will be overwritten with"
            " the new specified modules."
        )

        with engine.connect() as conn:
            conn.execute(
            text(
                f"""
                delete from {schema}.purchases where "organization_id" = '{str(org_id)}'
                """
            )
        )

    if modules is not None:
        add_purchases(org_id, modules=modules)


def define_user(email, user_id, role_id, org_id, role, modules):
    current = User(
        email=email,
        id=user_id,
        role_id=role_id,
        organization_id=org_id,
        is_enabled=True,
        role=UserRole(name=role, id=role_id),
        organization=Organization(
            id=org_id,
            modules=[
                Purchase(
                    module_id=uuid.uuid4(),
                    organization_id=org_id,
                    active_until="2122-08-20 10:34:16.452",
                    module=Module(id=uuid.uuid4(), name=module),
                )
                for module in modules
            ],
        ),
    )

    return current

async def add_player_request_new_org(user):
    db = session_maker()
    player_in = PlayerRecordCreate(
        playerId="427097",
        control_stage="signed",
        position=['st'],
        quality= "12",
        potential= "12",
        club_asking_price= 150000000,
        transfer_period= ["summer_2024"],
        priority_player= True,
        player_features= {},
        source_to_record=[]
    )
    player_record = crud.player_record.create_with_user(
        db=db,
        obj_in=player_in,
        user=user
    )
    request_in = TeamRequestCreate(
        position = "st",
        max_age = 28,
        max_value = 150000000,
        transfer_period=["summer_2024"],
        type=["loan_with_option","loan"],
        teamId="1612",
    )
    team_request = crud.team_request.create_with_user(
        db=db,
        obj_in=request_in,
        user=user,
    )


#create_user(
#    "<EMAIL>",
#    "password_here",
#    True,
#    True,
#    True,
#    True,
#    "92f31416-f6ec-4569-a7cb-4f280835ae4b",
#    "role_here_name",
#    "crm",
#)
