from fastapi import Request, HTTPException
from twilio.request_validator import <PERSON><PERSON><PERSON><PERSON>da<PERSON>
from sqlalchemy.orm import Session
from app.config import settings
from app.models.user import User
from sqlalchemy import func

async def is_valid_twilio_request(request: Request) -> bool:
    """
    Validates that the request came from <PERSON><PERSON><PERSON> by checking its signature.
    """
    validator = RequestValidator(settings.TWILIO_AUTH_TOKEN)
    signature = request.headers.get("X-Twilio-Signature", "")
    url = str(request.url)
    
    # Twilio sends request data as form parameters
    form_data = await request.form()

    return validator.validate(url, form_data, signature)


def get_user_by_phone(phone_number: str, db: Session):
    """
    Checks if a user with the given phone number exists and is allowed to use WhatsApp.
    """
    user = db.query(User).filter(
        func.regexp_replace(User.phone_number, r'\s+', '', 'g') == phone_number,
        User.use_whatsapp == True
    ).first()
    return user


async def validate_whatsapp_user(phone_number: str, db: Session):
    """
    2️⃣ Verifies if the phone number exists and is allowed to use WhatsApp.
    """
    # 2️⃣ Check User in Database
    user = get_user_by_phone(phone_number, db)
    if not user:
        raise HTTPException(status_code=403, detail="Unauthorized: This phone number is not registered for WhatsApp use")

    return user