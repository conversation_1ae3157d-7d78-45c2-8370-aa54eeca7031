from datetime import datetime
import uuid
from typing import Optional
from pydantic import (
    BaseModel,
    Field,
)

from app.schemas.user import UserShort


class CommentUpdate(BaseModel):
    time: Optional[datetime] = Field(default_factory=datetime.now)
    comment: Optional[str]
    creator: Optional[str]

class CommentCreate(CommentUpdate):
    time: datetime = Field(default_factory=datetime.now)
    comment: str
    creator: str
    activity_id: Optional[uuid.UUID]
    player_id: Optional[uuid.UUID]
    staff_id: Optional[uuid.UUID]


    class Config:
        orm_mode = True



class Comment(CommentCreate):
    id: uuid.UUID

    class Config:
        orm_mode = True
        use_cache=True