from typing import Any, Optional
from sqlalchemy.orm import Session

from app import models
from app.crud.crud_admin_change import admin_change
from app.schemas.admin_change import AdminChangeCreate


def log_admin_action(
    db: Session,
    *,
    user: models.User,
    action_type: str,
    target_type: str,
    target_id: str,
    field: Optional[str] = None,
    previous: Optional[Any] = None,
    updated: Optional[Any] = None,
    details: Optional[str] = None,
) -> models.AdminChange:
    """
    Log an admin action with details about what was changed.

    Args:
        db: Database session
        user: The admin user performing the action
        action_type: Type of action (e.g. "create_user", "delete_organization")
        target_type: Type of entity being modified (e.g. "user", "organization")
        target_id: ID of the entity being modified
        field: Optional field that was changed
        previous: Optional previous value
        updated: Optional new value
        details: Optional additional details about the change
    """
    change_in = AdminChangeCreate(
        action_type=action_type,
        target_type=target_type,
        target_id=target_id,
        field=field,
        previous=previous,
        updated=updated,
        details=details,
    )
    return admin_change.create_with_user(db=db, obj_in=change_in, user=user)
