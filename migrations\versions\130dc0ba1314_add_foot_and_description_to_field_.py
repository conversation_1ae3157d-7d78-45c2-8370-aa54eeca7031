"""Add foot and description to field players

Revision ID: 130dc0ba1314
Revises: 84ec09047252
Create Date: 2025-05-21 18:32:17.360950

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '130dc0ba1314'
down_revision = '84ec09047252'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_players', sa.Column('foot', sa.String(), nullable=True), schema='crm')
    op.add_column('field_players', sa.Column('description', sa.String(), nullable=True), schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('field_players', 'description', schema='crm')
    op.drop_column('field_players', 'foot', schema='crm')
    # ### end Alembic commands ###