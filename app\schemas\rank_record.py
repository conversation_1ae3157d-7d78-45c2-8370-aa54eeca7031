from typing import Optional
import uuid
from pydantic import BaseModel, validator
from datetime import datetime
from app.schemas.extended_base import empty_str_to_none

class RankRecordUpdate(BaseModel):
    name: Optional[str]
    description: Optional[str]
    purpose: Optional[str]
    for_who: Optional[str]
    config: Optional[str]
    youth: Optional[bool]
    target_position: Optional[str]
    default_quality: Optional[bool]

    _empty_str_to_none = validator("*", allow_reuse=True, pre=True)(empty_str_to_none)

class RankRecordCreate(RankRecordUpdate):
    name: Optional[str]
    description: Optional[str]
    purpose: Optional[str]
    for_who: Optional[str]
    config: str
    youth: Optional[bool]
    target_position: Optional[str]
    default_quality: Optional[bool]
    

class RankRecord(RankRecordCreate):
    id: uuid.UUID
    created_at: datetime
    created_by: uuid.UUID
    organization_id: uuid.UUID

    class Config:
        orm_mode = True

class RankRecordShort(BaseModel):
    id: uuid.UUID
    name: Optional[str]
    description: Optional[str]
    purpose: Optional[str]
    for_who: Optional[str]
    config: str
    youth: Optional[bool]
    target_position: Optional[str]
    created_at: Optional[datetime]

    class Config:
        orm_mode = True

class RankRecordShort2(BaseModel):
    id: uuid.UUID
    youth: Optional[bool]
    target_position: Optional[str]
    created_at: Optional[datetime]

    class Config:
        orm_mode = True


