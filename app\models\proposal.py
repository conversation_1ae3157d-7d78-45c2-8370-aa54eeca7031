from sqlalchemy import Column, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from app.models.extended_base_mixin import ExtendedBaseMixin
from app.db.base_class import Base
from app.config import settings
from sqlalchemy.schema import UniqueConstraint


class Proposal(Base, ExtendedBaseMixin):
    __tablename__ = "proposals"
    __table_args__ = __table_args__ = (UniqueConstraint('request_id', 'player_id', 'organization_id', name='_request_player_org_uc_new'), {"schema": settings.PG_SCHEMA})

    player_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.player_records.id"),
        index=True,
    )
    request_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.team_requests.id"),
        index=True,
    )

    player = relationship("PlayerRecord", back_populates="deals_with_player")
    team_request = relationship("TeamRequest", back_populates="proposed_players")
