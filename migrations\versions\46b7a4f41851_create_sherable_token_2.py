"""Create sherable token - 2

Revision ID: 46b7a4f41851
Revises: 9a30e5c5f6fd
Create Date: 2025-03-24 15:31:22.407021

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '46b7a4f41851'
down_revision = '9a30e5c5f6fd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('share_tokens',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('resource_type', sa.String(length=50), nullable=False),
    sa.Column('resource_id', sa.Integer(), nullable=False),
    sa.Column('token', sa.UUID(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.<PERSON>umn('is_disabled', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('token'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_share_tokens_id'), 'share_tokens', ['id'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'staff_records', schema='crm_test', type_='foreignkey')
    op.drop_constraint(None, 'activity', schema='crm_test', type_='foreignkey')
    op.drop_index(op.f('ix_crm_test_share_tokens_id'), table_name='share_tokens', schema='crm_test')
    op.drop_table('share_tokens', schema='crm_test')
    # ### end Alembic commands ###