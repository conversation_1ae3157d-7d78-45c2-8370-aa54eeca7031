"""Comments + contact new field

Revision ID: fd07059e2527
Revises: 71c4aff01f0b
Create Date: 2024-08-15 15:29:09.978269

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'fd07059e2527'
down_revision = '71c4aff01f0b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('comments_activity',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('comment', sa.String(), nullable=True),
    sa.Column('time', sa.DateTime(), nullable=True),
    sa.Column('creator', sa.String(), nullable=True),
    sa.Column('activity_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['activity_id'], ['crm_dev.activity.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_comments_activity_activity_id'), 'comments_activity', ['activity_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_comments_activity_time'), 'comments_activity', ['time'], unique=False, schema='crm_dev')
    op.add_column('activity', sa.Column('description', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('contacts', sa.Column('owner', sa.String(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('contacts', 'owner', schema='crm_dev')
    op.drop_column('activity', 'description', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_comments_activity_time'), table_name='comments_activity', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_comments_activity_activity_id'), table_name='comments_activity', schema='crm_dev')
    op.drop_table('comments_activity', schema='crm_dev')
    # ### end Alembic commands ###