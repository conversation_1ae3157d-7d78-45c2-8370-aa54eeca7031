from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy.orm import Session, selectinload, lazyload, Load
from sqlalchemy import desc

from app.db.base_class import Base
from app.models import User
from app import models
import uuid

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        """
        CRUD object with default methods to Create, Read, Update, Delete (CRUD).
        **Parameters**
        * `model`: A SQLAlchemy model class
        * `schema`: A Pydantic model (schema) class
        """
        self.model = model

    def get(self, db: Session, id: Any) -> Optional[ModelType]:
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).filter(self.model.id == id).first()
    
    def get_by_org(self, db: Session,id: Any, org_id: Any) -> Optional[ModelType]:
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).filter(self.model.organization_id == org_id,
             self.model.id == id).first()

    def get_all(
        self,
        db: Session,
    ) -> List[ModelType]:
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).all()
    
    def get_all_ordered(
        self,
        db: Session,
    ) -> List[ModelType]:
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).order_by(desc(self.model.created_at)).all()

    def get_all_w_org(
        self,
        db: Session,
        org_id: str,
        can_access_sensitive: bool
    ) -> List[ModelType]:
        filter_cond = self.model.is_sensitive != True if not can_access_sensitive else True
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).filter(self.model.organization_id == org_id, filter_cond).order_by(self.model.last_updated.desc()).all()

    def get_all_filtered(self,
        db: Session,
        id: Any
    ) -> List[ModelType]:
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).filter(self.model.id.in_(id)).all()
    
    def get_all_filtered_for_player_org(self,
        db: Session,
        id: Any,
        org_id: Any
    ) -> List[ModelType]:
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).filter(self.model.player_id == id, self.model.organization_id == org_id).all()

    def get_all_filtered_for_player(self,
        db: Session,
        id: Any,
    ) -> List[ModelType]:
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).filter(self.model.player_id == id).all()


    def get_all_filtered_for_staff_org(self,
        db: Session,
        id: Any,
        org_id: Any
    ) -> List[ModelType]:
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).filter(self.model.staff_id == id, self.model.organization_id == org_id).all()

    def get_all_filtered_for_staff(self,
        db: Session,
        id: Any,
    ) -> List[ModelType]:
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).filter(self.model.staff_id == id).all()

    def create(self, db: Session, *, obj_in: CreateSchemaType) -> ModelType:
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data)  
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def create_with_user(self, db:Session, *, obj_in: CreateSchemaType, user: User) -> ModelType:
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data, created_by=user.id, organization_id=user.organization_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def bulk_create_with_user(self, db:Session, *, objs_in: List[CreateSchemaType], user: User) -> ModelType:
        objects_with_user = []
        for obj_in in objs_in:
            obj_in_data = jsonable_encoder(obj_in)
            db_obj = self.model(**obj_in_data, created_by=user.id, organization_id=user.organization_id)
            objects_with_user.append(db_obj)
        db.add_all(objects_with_user)
        db.commit()
        db.refresh(db_obj)
        return objects_with_user

    def bulk_create_with_user_and_assigned_to(
        self, db: Session, *, objs_in: List[CreateSchemaType], user: User
    ) -> ModelType:
        objects_with_user = []
        for obj_in in objs_in:
            obj_in_data = jsonable_encoder(obj_in)

            assigned_in_data = obj_in_data.pop("assigned_to_record")

            ll_assignees = [
                    models.AssignedToRecord(contact_id=con, id=uuid.uuid4())
                    for con in assigned_in_data
                ]

            db_obj = self.model(
                **obj_in_data,
                created_by=user.id,
                organization_id=user.organization_id,
            )

            db_obj.assigned_to_record = ll_assignees

            objects_with_user.append(db_obj)
        db.add_all(objects_with_user)
        db.commit()
        db.refresh(db_obj)
        return objects_with_user

    def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
    ) -> ModelType:
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_none=True)
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: Any) -> ModelType:
        obj = db.query(self.model).get(id)
        db.delete(obj)
        db.commit()
        return obj

    def create_with_user_assigne(
        self, db: Session, *, obj_in: CreateSchemaType, user: User
    ) -> ModelType:
        obj_in_data = jsonable_encoder(obj_in)
        assigned_in_data = obj_in_data.pop('assigned_to_record')
        ll_assignees = []
        if (assigned_in_data):
            ll_assignees = [models.AssignedToRecord(contact_id=con, id=uuid.uuid4()) for con in assigned_in_data]
        db_obj = self.model(
            **obj_in_data,
            created_by=user.id,
            organization_id=user.organization_id,
            assigned_to_record=ll_assignees
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj