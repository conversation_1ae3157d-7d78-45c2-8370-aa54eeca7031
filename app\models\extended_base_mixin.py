from datetime import datetime
import uuid

from sqlalchemy import Column, ForeignKey, String, DateTime, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import declared_attr, relationship

from app.config import settings


class ExtendedBaseMixin(object):
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at = Column(DateTime, index=True, default=datetime.now)
    last_updated = Column(DateTime, index=True, default=datetime.now)
    is_sensitive = Column(Boolean, index=True)

    @declared_attr
    def created_by(cls):
        return Column(
            "created_by", ForeignKey(f"{settings.PG_SCHEMA}.user.id"), index=True
        )

    @declared_attr
    def creator(cls):
        return relationship("User", foreign_keys=cls.created_by)

    @declared_attr
    def organization_id(cls):
        return Column(
            "organization_id",
            ForeignKey(f"{settings.PG_SCHEMA}.organizations.id"),
            index=True,
        )

    @declared_attr
    def organization(cls):
        return relationship("Organization")

    notes = Column(String)
