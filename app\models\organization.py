import uuid
from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from app.db.base_class import Base
from app.config import settings

class Organization(Base):
    __tablename__ = "organizations"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String)
    password = Column(String)
    billing_email = Column(String)
    users = relationship("User", back_populates="organization")
    modules = relationship("Purchase", back_populates="organization", lazy="selectin")
    contacts = relationship("Contact", back_populates="organization")
    player_records = relationship("PlayerRecord", back_populates="organization")
    team_requests = relationship("TeamRequest", back_populates="organization")
    type = Column(String)
    agency_id = Column(Integer)
    agency_logo_url = Column(String)

