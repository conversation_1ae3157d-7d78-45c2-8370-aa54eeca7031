"""add match label to the model

Revision ID: 69cdfd32ebd4
Revises: 1df8e6a520ff
Create Date: 2023-04-26 14:46:53.880703

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '69cdfd32ebd4'
down_revision = '1df8e6a520ff'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reports', sa.Column('match_label', sa.ARRAY(sa.String()), nullable=True), schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('reports', 'match_label', schema='crm')
    # ### end Alembic commands ###