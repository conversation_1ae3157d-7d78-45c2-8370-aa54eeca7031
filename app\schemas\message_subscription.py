from typing import Optional
import uuid
from pydantic import BaseModel


class SubscriptionUpdate(BaseModel):
    user_id: Optional[uuid.UUID]
    approved: Optional[bool]
    token: Optional[str]
    email: Optional[str]

class SubscriptionCreate(SubscriptionUpdate):
    user_id: uuid.UUID
    approved: bool = False
    token: str
    email: Optional[str]


class Subscription(SubscriptionCreate):
    ...

    class Config:
        orm_mode = True