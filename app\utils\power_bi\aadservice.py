# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import msal
try:
    from ...config import settings
except:
    from app.config import settings

class AadService:

    def get_access_token():
        '''Generates and returns Access token

        Returns:
            string: Access token
        '''

        response = None
        try:
            if settings.AUTHENTICATION_MODE.lower() == 'masteruser':

                # Create a public client to authorize the app with the AAD app
                clientapp = msal.PublicClientApplication(settings.CLIENT_ID, authority=settings.AUTHORITY_URL)
                accounts = clientapp.get_accounts(username=settings.POWER_BI_USER)

                if accounts:
                    # Retrieve Access token from user cache if available
                    response = clientapp.acquire_token_silent(settings.SCOPE_BASE, account=accounts[0])

                if not response:
                    # Make a client call if Access token is not available in cache
                    response = clientapp.acquire_token_by_username_password(settings.POWER_BI_USER, settings.POWER_BI_PASS, scopes=settings.SCOPE_BASE)     

            # Service Principal auth is the recommended by Microsoft to achieve App Owns Data Power BI embedding
            elif settings.AUTHENTICATION_MODE.lower() == 'serviceprincipal':
                authority = settings.AUTHORITY_URL.replace('organizations',settings.TENANT_ID)
                clientapp = msal.ConfidentialClientApplication(settings.CLIENT_ID, client_credential=settings.CLIENT_SECRET, authority=authority)

                # Make a client call if Access token is not available in cache
                response = clientapp.acquire_token_for_client(scopes=settings.SCOPE_BASE)

            try:
                return response['access_token']
            except KeyError:
                raise Exception(response['error_description'])

        except Exception as ex:
            raise Exception('Error retrieving Access token\n' + str(ex))