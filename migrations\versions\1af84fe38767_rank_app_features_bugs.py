"""Rank app features/bugs

Revision ID: 1af84fe38767
Revises: 24f7982736eb
Create Date: 2022-11-15 10:16:47.110635

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1af84fe38767'
down_revision = '24f7982736eb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rank_outputs', sa.Column('feature_json', sa.String(), nullable=True), schema='crm')
    op.drop_column('rank_records', 'results', schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rank_records', sa.Column('results', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm')
    op.drop_column('rank_outputs', 'feature_json', schema='crm')
    # ### end Alembic commands ###