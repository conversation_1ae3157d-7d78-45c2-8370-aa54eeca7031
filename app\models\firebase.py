from sqlalchemy import Column, String, ForeignKey
from app.db.base_class import Base
from app.config import settings
from sqlalchemy.dialects.postgresql import UUID
import uuid


class RefreshToken(Base):
    __tablename__ = "refresh_token"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.user.id"),
        primary_key=True,
        index=True,
        default = uuid.uuid4
    )
    refresh_token = Column(String)
