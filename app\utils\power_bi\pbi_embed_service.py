from fastapi import HTT<PERSON>Exception
import requests
import json
from .models.reportconfig import ReportConfig
from .models.embedtoken import EmbedToken
from .models.embedconfig import EmbedConfig
from .models.embedtokenrequestbody import EmbedTokenRequestBody
from .aadservice import AadService

class PbiEmbedService:
    def get_embed_params_for_single_report(self, workspace_id, report_id,username,  additional_dataset_id=None):
        '''Get embed params for a report and a workspace

        Args:
            workspace_id (str): Workspace Id
            report_id (str): Report Id
            additional_dataset_id (str, optional): Dataset Id different than the one bound to the report. Defaults to None.

        Returns:
            EmbedConfig: Embed token and Embed URL
        '''

        report_url = f'https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/reports/{report_id}'       
        api_response = requests.get(report_url, headers=self.get_request_header())

        if api_response.status_code != 200:
            raise HTTPException(status_code=api_response.status_code, detail=api_response.reason)

        api_response = json.loads(api_response.text)
        report = ReportConfig(api_response['id'], api_response['name'], api_response['embedUrl'])
        dataset_ids = [api_response['datasetId']]

        # Append additional dataset to the list to achieve dynamic binding later
        if additional_dataset_id is not None:
            dataset_ids.append(additional_dataset_id)

        embed_token = self.get_embed_token_for_single_report_single_workspace(report_id, dataset_ids, workspace_id, username)
        embed_config = EmbedConfig(embed_token.tokenId, embed_token.token, embed_token.tokenExpiry, [report.__dict__])
        return json.dumps(embed_config.__dict__)
    
    def get_embed_token_for_single_report_single_workspace(self, report_id, dataset_ids, target_workspace_id=None, username=None):
        '''Get Embed token for single report, multiple datasets, and an optional target workspace

        Args:
            report_id (str): Report Id
            dataset_ids (list): Dataset Ids
            target_workspace_id (str, optional): Workspace Id. Defaults to None.

        Returns:
            EmbedToken: Embed token
        '''

        request_body = EmbedTokenRequestBody()

        for dataset_id in dataset_ids:
            request_body.datasets.append({'id': dataset_id})

        request_body.reports.append({'id': report_id})

        if target_workspace_id is not None:
            request_body.targetWorkspaces.append({'id': target_workspace_id})
            request_body.identities.append({'username': username, 'roles': ['RLS'], 'datasets': dataset_ids})

        # Generate Embed token for multiple workspaces, datasets, and reports. Refer https://aka.ms/MultiResourceEmbedToken
        embed_token_api = 'https://api.powerbi.com/v1.0/myorg/GenerateToken'
        api_response = requests.post(embed_token_api, data=json.dumps(request_body.__dict__), headers=self.get_request_header())
        if api_response.status_code != 200:
            raise HTTPException(status_code=api_response.status_code, detail=api_response.reason)

        api_response = json.loads(api_response.text)
        return EmbedToken(
            api_response['tokenId'],
            api_response['token'],
            api_response['expiration'],
        )

    def get_request_header(self):
        '''Get Power BI API request header

        Returns:
            Dict: Request header
        '''

        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {AadService.get_access_token()}',
        }