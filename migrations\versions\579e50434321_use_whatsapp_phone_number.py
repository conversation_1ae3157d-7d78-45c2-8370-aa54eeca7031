"""Use whatsapp + phone number
Revision ID: 579e50434321
Revises: 8f71db3b185f
Create Date: 2025-02-05 15:36:22.513083
"""
from alembic import op
import sqlalchemy as sa
# revision identifiers, used by Alembic.
revision = '579e50434321'
down_revision = '8f71db3b185f'
branch_labels = None
depends_on = None
def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('phone_number', sa.String(), nullable=True), schema='crm_test')
    op.add_column('user', sa.Column('use_whatsapp', sa.<PERSON>(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###
def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'use_whatsapp', schema='crm_test')
    op.drop_column('user', 'phone_number', schema='crm_test')
    # ### end Alembic commands ###