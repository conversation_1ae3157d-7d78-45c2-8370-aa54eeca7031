"""Main migrations

Revision ID: 5bf81585240a
Revises: 0829a5e02a99
Create Date: 2025-04-17 16:01:52.987068

"""
from alembic import op
import sqlalchemy as sa
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '5bf81585240a'
down_revision = '0829a5e02a99'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('request_fields',
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('organization_id', sa.UUID(), nullable=True),
    sa.Column('for_date', sa.DateTime(), nullable=True),
    sa.Column('formation', sa.String(), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Bo<PERSON>(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_request_fields_created_at'), 'request_fields', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_request_fields_created_by'), 'request_fields', ['created_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_request_fields_is_sensitive'), 'request_fields', ['is_sensitive'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_request_fields_last_updated'), 'request_fields', ['last_updated'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_request_fields_name'), 'request_fields', ['name'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_request_fields_organization_id'), 'request_fields', ['organization_id'], unique=False, schema='crm')
    op.create_table('field_requests',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('field_id', sa.UUID(), nullable=True),
    sa.Column('teamId', sa.Integer(), nullable=True),
    sa.Column('index_position', sa.Integer(), nullable=True),
    sa.Column('transfer_fee', sa.Float(), nullable=True),
    sa.Column('salary', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['field_id'], ['crm.request_fields.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_field_requests_field_id'), 'field_requests', ['field_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_field_requests_teamId'), 'field_requests', ['teamId'], unique=False, schema='crm')
    op.add_column('community_deal', sa.Column('suitability_score', sa.Float(), nullable=True), schema='crm')
    op.add_column('community_deal', sa.Column('phone_number', sa.String(), nullable=True), schema='crm')
    op.add_column('organizations', sa.Column('agency_id', sa.Integer(), nullable=True), schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organizations', 'agency_id', schema='crm')
    op.drop_column('community_deal', 'phone_number', schema='crm')
    op.drop_column('community_deal', 'suitability_score', schema='crm')
    op.drop_constraint(None, 'activity', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_field_requests_teamId'), table_name='field_requests', schema='crm')
    op.drop_index(op.f('ix_crm_field_requests_field_id'), table_name='field_requests', schema='crm')
    op.drop_table('field_requests', schema='crm')
    op.drop_index(op.f('ix_crm_request_fields_organization_id'), table_name='request_fields', schema='crm')
    op.drop_index(op.f('ix_crm_request_fields_name'), table_name='request_fields', schema='crm')
    op.drop_index(op.f('ix_crm_request_fields_last_updated'), table_name='request_fields', schema='crm')
    op.drop_index(op.f('ix_crm_request_fields_is_sensitive'), table_name='request_fields', schema='crm')
    op.drop_index(op.f('ix_crm_request_fields_created_by'), table_name='request_fields', schema='crm')
    op.drop_index(op.f('ix_crm_request_fields_created_at'), table_name='request_fields', schema='crm')
    op.drop_table('request_fields', schema='crm')
    # ### end Alembic commands ###