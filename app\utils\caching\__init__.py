import orjson
from fastapi_cache import caches
from fastapi_cache.backends.memory import InMemoryCacheBackend
from fastapi_cache.backends.redis import RedisCacheBackend

from app.config import settings

# if you want to test redis instead of in-memory, uncomment
# the line below (this will only work on the gcp vm because of
# the networking):
# settings.PROD = True


# settings.DEFAULT_TTL = 5
# used for testing ttl

if settings.PROD:
    from fastapi_cache.backends.redis import CACHE_KEY

    rc = RedisCacheBackend(settings.REDIS_HOST)
    caches.set(CACHE_KEY, rc)
    kwargs_dict = {"expire": settings.DEFAULT_TTL}
else:
    from fastapi_cache.backends.memory import CACHE_KEY

    imc = InMemoryCacheBackend()
    caches.set(CACHE_KEY, imc)
    # different kwargs dicts because they are name different in the different backends, shitty design by fastapi_cache
    kwargs_dict = {"ttl": settings.DEFAULT_TTL}


async def reset_cache_for_everyone(
    cache, resource: str, org_id: str, verbose: bool = False
):
    """Need this to reset both for sensitive and non-sensitive users"""
    if verbose:
        print(f"setting cache for {org_id}, {resource}")

    for b in (True, False):
        await cache.delete(f"{settings.CACHE_PREFIX}{org_id}_{resource}_{b}")

async def reset_cache_for_every_com_org(
    cache, resource: str, org_ids, verbose: bool = False
):
    """Need this to reset both for sensitive and non-sensitive users"""
    if verbose:
        print(f"setting cache for {org_id}, {resource}")
    for org_id in org_ids:
        for b in (True, False):
            await cache.delete(f"{settings.CACHE_PREFIX}{org_id[0]}_{resource}_{b}")


async def set_cache_for_everyone(
    cache, resource: str, org_id: str, content, verbose: bool = False
):
    if verbose:
        print(f"setting cache for {org_id}, {resource}")
    if resource != 'team_requests':
        filtered_content = [x for x in content if not x.get("is_sensitive")]
    else:
        filtered_content = {"community": [x for x in content['community'] if not x.get("is_sensitive")],
                             'private': [x for x in content['private'] if not x.get("is_sensitive")]}
    await cache.set(
        f"{settings.CACHE_PREFIX}{org_id}_{resource}_True",
        orjson.dumps(content),
        **kwargs_dict,
    )
    await cache.set(
        f"{settings.CACHE_PREFIX}{org_id}_{resource}_False",
        orjson.dumps(filtered_content),
        **kwargs_dict,
    )


def get_cache():
    return caches.get(CACHE_KEY)
