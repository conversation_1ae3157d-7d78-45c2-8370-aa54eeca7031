name: pytesting
on:
  push:
      branches:
        - pytest

jobs:
  test:
    runs-on: ubuntu-latest
    env:
      PROD: False
      STAGING: True
      TEST: True
      AGENT_API_URL_PROD: ${{ secrets.AGENT_API_URL_PROD }}
      MATCHING_API_URL_PROD: ${{ secrets.MATCHING_API_URL_PROD }}
      PG_SCHEMA_PROD: ${{ secrets.PG_SCHEMA_PROD }}
      PG_SCHEMA_DEV: ${{ secrets.PG_SCHEMA_DEV }}
      PG_URL_DEV: ${{ secrets.PG_URL_DEV }}
      PG_URL_TEST: ${{ secrets.PG_URL_TEST }}
      WYSCOUT_USR: ${{ secrets.WYSCOUT_USR }}
      WYSCOUT_PASS: ${{ secrets.WYSCOUT_PASS }}
      PG_URL_PROD: ${{ secrets.PG_URL_PROD }}
      POSTGRES_PROD_STR: ${{ secrets.POSTGRES_PROD_STR }}
      DB_URL_PROD: ${{ secrets.DB_URL_PROD }}
      CLOUD_STORAGE_BUCKET_NAME: ${{ secrets.CLOUD_STORAGE_BUCKET_NAME }}
      RANKS_API_USR: ${{ secrets.RANKS_API_USR }}
      RANKS_API_PASS: ${{ secrets.RANKS_API_PASS }}
      RANK_API_URL: ${{ secrets.RANK_API_URL_TEST }}
      OUTLOOK_ENSKAI_ALERTS_PASS: ${{ secrets.OUTLOOK_ENSKAI_ALERTS_PASS }}
      OUTLOOK_ENSKAI_ALERTS_ADDRESS: ${{ secrets.OUTLOOK_ENSKAI_ALERTS_ADDRESS }}
      LOOKUP_API_USR: ${{ secrets.LOOKUP_API_USR }}
      LOOKUP_API_PASS: ${{ secrets.LOOKUP_API_PASS }}
      LOOKUP_API_URL: ${{ secrets.LOOKUP_API_URL }}
      AGENT_API_USR: ${{ secrets.AGENT_API_USR }}
      AGENT_API_PASS: ${{ secrets.AGENT_API_PASS }}
      MATCHING_API_USR: ${{ secrets.MATCHING_API_USR }}
      MATCHING_API_PASS: ${{ secrets.MATCHING_API_PASS }}
      JWT_SECRET_KEY: ${{ secrets.JWT_SECRET_KEY }}
      REALM_APP_ID: ${{ secrets.REALM_APP_ID }}
      WORKSPACE_ID: ${{ secrets.WORKSPACE_ID }}
      REPORT_ID: ${{ secrets.REPORT_ID }}
      TENANT_ID: ${{ secrets.TENANT_ID }}
      CLIENT_ID: ${{ secrets.CLIENT_ID }}
      CLIENT_SECRET: ${{ secrets.CLIENT_SECRET }}
      POWER_BI_USER: ${{ secrets.POWER_BI_USER }}
      POWER_BI_PASS: ${{ secrets.POWER_BI_PASS }}
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python 3.9
      uses: actions/setup-python@v4
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install wheel
        pip install git+https://github.com/MagicStack/uvloop
        pip install -r requirements.txt
        pip install https://github.com/bboe/coveralls-python/archive/github_actions.zip
    - name: Lint with flake8
      run: |
        pip install flake8
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    - name: Create CloudSQL Connection
      uses: mattes/gce-cloudsql-proxy-action@v1
      with:
        creds: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
        instance: footballanalytics:europe-west1:enskai-managed-postgres
    - name: Test with pytest
      id: pytest
      run: |
        pip install pytest
        pip install pytest-cov
        pytest --doctest-modules --junitxml=junit/test-results.xml --cov=app --cov-report=xml --cov-report=html --cov-report term >> coverage.txt
        cat coverage.txt
    # - name: Print coverage
    #   id: covxml
    #   run: cat /home/<USER>/work/shadow-eleven-backend/shadow-eleven-backend/junit/test-results.xml

    - name: Make Coverage Badge
      uses: action-badges/cobertura-coverage-xml-badges@0.2.1
      with:
        file-name: coverage.svg
        badge-branch: badges
        github-token: '${{ secrets.GITHUB_TOKEN }}'
        coverage-file-name: ./coverage.xml
    
    - name: Archive code coverage xml
      uses: actions/upload-artifact@v3
      if: success() || steps.pytest.conclusion == 'failure'
      with:
        name: code-coverage-xml
        path: ./coverage.xml

    - name: Archive code coverage txt
      uses: actions/upload-artifact@v3
      with:
        name: code-coverage-txt
        path: ./coverage.txt

    - name: Archive code coverage html
      uses: actions/upload-artifact@v3
      with:
        name: code-coverage-html
        path: htmlcov
        retention-days: 5

  # create-pr-to-staging:
  #   needs: [test]
  #   runs-on: ubuntu-latest
  #   env:
  #     GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  #     TARGET_BRANCH: staging
  #     SOURCE_BRANCH: testing
  #   steps:
  #   - uses: actions/checkout@v3
  #   - uses: actions/download-artifact@v2
  #     with:
  #       name: code-coverage-txt
  #   - name: create pull request
  #     run: gh pr create --reviewer Aelvangunduz,ziruzavar -B $TARGET_BRANCH -H $SOURCE_BRANCH --title 'Tests passed. Automatically creating a PR.' --body 'Created by Github action, triggered by user ${{ github.actor }}'
  #     # run: gh pr edit $SOURCE_BRANCH -b 'Created by Github action, triggered by user ${{ github.actor }}' -t 'Tests passed. Automatically creating a PR.'  && gh pr reopen $SOURCE_BRANCH || gh pr create --fill -B $TARGET_BRANCH -H $SOURCE_BRANCH
  #   # - name: Print coverage
  #   #   id: covxml
  #   #   run: cat ./coverage.xml
  #   - name: comment the coverage report
  #     run: gh pr comment --body "$(cat ./coverage.txt)"
