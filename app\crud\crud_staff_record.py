from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.staff_record import StaffRecordUpdate, StaffRecordCreate
from sqlalchemy.orm import Session, Load, lazyload
from typing import Any, Optional, TypeVar, List, Union, Dict, Type
from app.db.base_class import Base
from sqlalchemy import text
from pydantic import BaseModel
from fastapi.encoders import jsonable_encoder
import uuid

ModelType = TypeVar("ModelType", bound=Base)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)
class CRUDStaffRecord(CRUDBase[models.StaffRecord, StaffRecordCreate, StaffRecordUpdate]):
        def get_staff_with_transfermarkt(self, db: Session, tmId: Any, org_id) -> Optional[ModelType]:
            return (
                db.query(self.model)
                .options(
                    Load(self.model).selectinload("*"),
                    lazyload("*"),
                )
                .filter(self.model.staff_id == tmId, self.model.organization_id == org_id)
                .first()
            )
        
        def get_staff_role(self, db: Session, tmId: Any) -> Optional[dict]:
            query = """
                SELECT "role"
                FROM transfermarkt.staff_info t
                WHERE "staff_id" = :staff_id
                """
            result = db.execute(text(query), {"staff_id": tmId})
            return result.mappings().fetchone()
        
        def get_all_w_org_history(
        self, db: Session, staff_id: str
    ) -> Optional[List[dict]]:
            query = """
            SELECT "team_name", "role", "appointed", in_charge_until
            FROM transfermarkt.staff_data t
            join transfermarkt.tm_teams tm on tm.team_id = t.current_team_id
            WHERE "staff_id" = :staff_id
            ORDER BY in_charge_until DESC
            """
            result = db.execute(text(query), {"staff_id": staff_id})
            return result.mappings().all()
    
        def update_with_assigned_to(
                    self,
                db: Session,
                *,
                db_obj: ModelType,
                obj_in: Union[UpdateSchemaType, Dict[str, Any]],
            ) -> ModelType:
                # we pop out player_features and compute changes separately:
                obj_in_data = jsonable_encoder(obj_in)
                db_obj_data = jsonable_encoder(db_obj)
                assigned_in_data = []
                if obj_in_data and db_obj_data and str(obj_in_data["assigned_to_record"]) == str(db_obj_data["assigned_to_record"]):
                    obj_in_data.pop("assigned_to_record")
                else:
                    assigned_in_data = obj_in_data.pop("assigned_to_record")

                ll_assigned = []
                if assigned_in_data:
                    ll_assigned = [models.AssignedToRecord(contact_id=con, id=uuid.uuid4())
                                for con in assigned_in_data]

                db_obj.assigned_to_record = ll_assigned
                for field, value in obj_in_data.items():
                    setattr(db_obj, field, value)

                db.add(db_obj)
                db.commit()
                db.refresh(db_obj)
                return db_obj
        

staff_record = CRUDStaffRecord(models.StaffRecord)