"""migrate test to test

Revision ID: 3471ad3b3a7e
Revises: 5853ca8b643c
Create Date: 2023-08-04 11:18:36.140512

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3471ad3b3a7e'
down_revision = '5853ca8b643c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # op.alter_column('community_proposals', 'are_you_the_agent',
    #            existing_type=sa.VARCHAR(),
    #            type_=sa.Boolean(),
    #            existing_nullable=True,
    #            schema='crm_test')
    op.drop_column('community_proposals', 'position', schema='crm_test')
    op.drop_column('community_proposals', 'foot', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('community_proposals', sa.Column('foot', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('community_proposals', sa.Column('position', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True), schema='crm_test')
    # op.alter_column('community_proposals', 'are_you_the_agent',
    #            existing_type=sa.Boolean(),
    #            type_=sa.VARCHAR(),
    #            existing_nullable=True,
    #            schema='crm_test')
    # ### end Alembic commands ###