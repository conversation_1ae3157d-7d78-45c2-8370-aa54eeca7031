"""create test schema

Revision ID: 3c201089067e
Revises: 7bf1c65913a3
Create Date: 2023-07-26 13:52:19.804737

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '3c201089067e'
down_revision = '7bf1c65913a3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('modules',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_table('organizations',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('password', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_table('user_roles',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    schema='crm_test'
    )
    op.create_table('purchases',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('module_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('active_until', sa.DateTime(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['module_id'], ['crm_test.modules.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_purchases_active_until'), 'purchases', ['active_until'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_purchases_created_at'), 'purchases', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_purchases_module_id'), 'purchases', ['module_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_purchases_organization_id'), 'purchases', ['organization_id'], unique=False, schema='crm_test')
    op.create_table('user',
    sa.Column('email', sa.String(length=320), nullable=False),
    sa.Column('hashed_password', sa.String(length=1024), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_superuser', sa.Boolean(), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('id', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=False),
    sa.Column('is_enabled', sa.Boolean(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('role_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['crm_test.user_roles.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_user_email'), 'user', ['email'], unique=True, schema='crm_test')
    op.create_table('contacts',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('contact_type', sa.String(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('contact_organization', sa.String(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_contacts_created_at'), 'contacts', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contacts_created_by'), 'contacts', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contacts_is_sensitive'), 'contacts', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contacts_last_updated'), 'contacts', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contacts_organization_id'), 'contacts', ['organization_id'], unique=False, schema='crm_test')
    op.create_table('rank_records',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('purpose', sa.String(), nullable=True),
    sa.Column('for_who', sa.String(), nullable=True),
    sa.Column('config', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('youth', sa.Boolean(), nullable=True),
    sa.Column('target_position', sa.String(), nullable=True),
    sa.Column('default_quality', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_table('team_requests',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('teamId', sa.Integer(), nullable=True),
    sa.Column('position', sa.String(), nullable=True),
    sa.Column('foot', sa.String(), nullable=True),
    sa.Column('stage', sa.String(), nullable=True),
    sa.Column('max_age', sa.Integer(), nullable=True),
    sa.Column('max_value', sa.Integer(), nullable=True),
    sa.Column('max_net_salary', sa.Integer(), nullable=True),
    sa.Column('transfer_period', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('reason_for_outcome', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('type', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('is_community', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    # sa.ForeignKeyConstraint(['teamId'], ['wyscout.team_info2.teamId'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_team_requests_created_at'), 'team_requests', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_team_requests_created_by'), 'team_requests', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_team_requests_is_sensitive'), 'team_requests', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_team_requests_last_updated'), 'team_requests', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_team_requests_organization_id'), 'team_requests', ['organization_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_team_requests_teamId'), 'team_requests', ['teamId'], unique=False, schema='crm_test')
    op.create_table('player_records',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('playerId', sa.Integer(), nullable=True),
    sa.Column('control_stage', sa.String(), nullable=True),
    sa.Column('position', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('quality', sa.Integer(), nullable=True),
    sa.Column('potential', sa.Integer(), nullable=True),
    sa.Column('birth_date', sa.DateTime(), nullable=True),
    sa.Column('club_asking_price', sa.Float(), nullable=True),
    sa.Column('transfer_period', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('agency', sa.String(), nullable=True),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('current_gross_salary', sa.Float(), nullable=True),
    sa.Column('priority_player', sa.Boolean(), nullable=True),
    sa.Column('proactively_scouted', sa.Boolean(), nullable=True),
    sa.Column('xtransfer_low', sa.Float(), nullable=True),
    sa.Column('xtransfer_high', sa.Float(), nullable=True),
    sa.Column('xgross_salary_low', sa.Float(), nullable=True),
    sa.Column('xgross_salary_high', sa.Float(), nullable=True),
    sa.Column('xtransfer_next_high', sa.Float(), nullable=True),
    sa.Column('xtransfer_next_low', sa.Float(), nullable=True),
    sa.Column('xgross_salary_next_high', sa.Float(), nullable=True),
    sa.Column('xgross_salary_next_low', sa.Float(), nullable=True),
    sa.Column('rec_max_investment', sa.Float(), nullable=True),
    sa.Column('assigned_to_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('contact_channel', postgresql.ARRAY(sa.String()), nullable=True),
    
    sa.ForeignKeyConstraint(['assigned_to_id'], ['crm_test.contacts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    # sa.ForeignKeyConstraint(['playerId'], ['wyscout.player_info2.playerId'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_player_records_assigned_to_id'), 'player_records', ['assigned_to_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_records_created_at'), 'player_records', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_records_created_by'), 'player_records', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_records_is_sensitive'), 'player_records', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_records_last_updated'), 'player_records', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_records_organization_id'), 'player_records', ['organization_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_records_playerId'), 'player_records', ['playerId'], unique=False, schema='crm_test')
    op.create_table('rank_outputs',
    sa.Column('playerId', sa.Integer(), nullable=False),
    sa.Column('player_rating', sa.Float(), nullable=True),
    sa.Column('player_rank', sa.Integer(), nullable=True),
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('feature_json', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['id'], ['crm_test.rank_records.id'], ),
    # sa.ForeignKeyConstraint(['playerId'], ['wyscout.player_info2.playerId'], ),
    sa.PrimaryKeyConstraint('id', 'playerId'),
    schema='crm_test'
    )
    op.create_table('team_request_changes',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('edit_at', sa.DateTime(), nullable=True),
    sa.Column('edit_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('previous', sa.String(), nullable=True),
    sa.Column('updated', sa.String(), nullable=True),
    sa.Column('team_request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('field', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['edit_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['team_request_id'], ['crm_test.team_requests.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_team_request_changes_edit_at'), 'team_request_changes', ['edit_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_team_request_changes_edit_by'), 'team_request_changes', ['edit_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_team_request_changes_team_request_id'), 'team_request_changes', ['team_request_id'], unique=False, schema='crm_test')
    op.create_table('contracts',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=True),
    sa.Column('end_date', sa.Date(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('coverage', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('agent_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('agent_alt_name', sa.String(), nullable=True),
    sa.Column('exclusive', sa.Boolean(), nullable=True),
    sa.Column('termination_fee', sa.Float(), nullable=True),
    sa.Column('pct_commission_agreed', sa.Float(), nullable=True),
    sa.Column('registered_in', sa.String(length=50), nullable=True),
    sa.Column('gross_salary', sa.JSON(), nullable=True),
    sa.Column('signing_fee', sa.Float(), nullable=True),
    sa.Column('goal_bonus', sa.Float(), nullable=True),
    sa.Column('assist_bonus', sa.Float(), nullable=True),
    sa.Column('matches_played_bonus', sa.Float(), nullable=True),
    sa.Column('minimum_fee_release_clause', sa.Float(), nullable=True),
    sa.Column('option_years', sa.Integer(), nullable=True),
    sa.Column('teamId', sa.Integer(), nullable=True),
    sa.Column('installments', sa.JSON(), nullable=True),
    sa.Column('active_status', sa.Boolean(), nullable=True),
    sa.Column('contract_type', sa.String(length=50), nullable=True),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['crm_test.contacts.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.ForeignKeyConstraint(['player_id'], ['crm_test.player_records.id'], ondelete='SET NULL'),
    # sa.ForeignKeyConstraint(['teamId'], ['wyscout.team_info2.teamId'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_contracts_active_status'), 'contracts', ['active_status'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contracts_agent_id'), 'contracts', ['agent_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contracts_created_at'), 'contracts', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contracts_created_by'), 'contracts', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contracts_end_date'), 'contracts', ['end_date'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contracts_is_sensitive'), 'contracts', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contracts_last_updated'), 'contracts', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contracts_organization_id'), 'contracts', ['organization_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contracts_player_id'), 'contracts', ['player_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contracts_start_date'), 'contracts', ['start_date'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contracts_teamId'), 'contracts', ['teamId'], unique=False, schema='crm_test')
    op.create_table('notifications_settings',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('contract_notifications', sa.Boolean(), nullable=True),
    sa.Column('player_notifications', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['player_id'], ['crm_test.player_records.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['crm_test.user.id'], ),
    sa.PrimaryKeyConstraint('id', 'user_id', 'player_id'),
    sa.UniqueConstraint('user_id', 'player_id', name='_user_player_uc_new'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_notifications_settings_player_id'), 'notifications_settings', ['player_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_notifications_settings_user_id'), 'notifications_settings', ['user_id'], unique=False, schema='crm_test')
    op.create_table('player_features',
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('speed', sa.String(), nullable=True),
    sa.Column('explosive', sa.String(), nullable=True),
    sa.Column('dynamic', sa.String(), nullable=True),
    sa.Column('build', sa.String(), nullable=True),
    sa.Column('intelligence', sa.String(), nullable=True),
    sa.Column('scanning', sa.String(), nullable=True),
    sa.Column('pressing', sa.String(), nullable=True),
    sa.Column('aggresive', sa.String(), nullable=True),
    sa.Column('leader', sa.String(), nullable=True),
    sa.Column('teamwork', sa.String(), nullable=True),
    sa.Column('education', sa.String(), nullable=True),
    sa.Column('entourage', sa.String(), nullable=True),
    sa.Column('first_touch', sa.String(), nullable=True),
    sa.Column('dribble', sa.String(), nullable=True),
    sa.Column('progress_with_the_ball', sa.String(), nullable=True),
    sa.Column('play_with_the_back', sa.String(), nullable=True),
    sa.Column('diagonal_passing', sa.String(), nullable=True),
    sa.Column('through_ball_passing', sa.String(), nullable=True),
    sa.Column('passes_to_final_third', sa.String(), nullable=True),
    sa.Column('pushes_inbetween_lines', sa.String(), nullable=True),
    sa.Column('defensive_positioning', sa.String(), nullable=True),
    sa.Column('attacking_positioning', sa.String(), nullable=True),
    sa.Column('interceptions', sa.String(), nullable=True),
    sa.Column('aerial_duels', sa.String(), nullable=True),
    sa.Column('finishing', sa.String(), nullable=True),
    sa.Column('heading', sa.String(), nullable=True),
    sa.Column('cut_inside', sa.String(), nullable=True),
    sa.Column('gk_1_vs_one_duels', sa.String(), nullable=True),
    sa.Column('defensive_1_vs_one_duels', sa.String(), nullable=True),
    sa.Column('closing_space', sa.String(), nullable=True),
    sa.Column('ball_control', sa.String(), nullable=True),
    sa.Column('reflexes', sa.String(), nullable=True),
    sa.Column('set_pieces', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['player_id'], ['crm_test.player_records.id'], ),
    sa.PrimaryKeyConstraint('player_id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_player_features_player_id'), 'player_features', ['player_id'], unique=False, schema='crm_test')
    op.create_table('player_record_changes',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('edit_at', sa.DateTime(), nullable=True),
    sa.Column('previous', sa.String(), nullable=True),
    sa.Column('updated', sa.String(), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('edit_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('field', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['edit_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['player_id'], ['crm_test.player_records.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_player_record_changes_edit_at'), 'player_record_changes', ['edit_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_record_changes_edit_by'), 'player_record_changes', ['edit_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_record_changes_player_id'), 'player_record_changes', ['player_id'], unique=False, schema='crm_test')
    op.create_table('proposals',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.ForeignKeyConstraint(['player_id'], ['crm_test.player_records.id'], ),
    sa.ForeignKeyConstraint(['request_id'], ['crm_test.team_requests.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('request_id', 'player_id', 'organization_id', name='_request_player_org_uc_new'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_proposals_created_at'), 'proposals', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_proposals_created_by'), 'proposals', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_proposals_is_sensitive'), 'proposals', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_proposals_last_updated'), 'proposals', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_proposals_organization_id'), 'proposals', ['organization_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_proposals_player_id'), 'proposals', ['player_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_proposals_request_id'), 'proposals', ['request_id'], unique=False, schema='crm_test')
    op.create_table('reports',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('strengths', sa.String(), nullable=True),
    sa.Column('weaknesses', sa.String(), nullable=True),
    sa.Column('current_ability', sa.Integer(), nullable=True),
    sa.Column('lookalike', sa.String(), nullable=True),
    sa.Column('conclusion', sa.String(), nullable=True),
    sa.Column('report_type', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('model_ranks', sa.String(), nullable=True),
    sa.Column('match_label', postgresql.ARRAY(sa.String()), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.ForeignKeyConstraint(['player_id'], ['crm_test.player_records.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_reports_created_at'), 'reports', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_reports_created_by'), 'reports', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_reports_is_sensitive'), 'reports', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_reports_last_updated'), 'reports', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_reports_organization_id'), 'reports', ['organization_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_reports_player_id'), 'reports', ['player_id'], unique=False, schema='crm_test')
    op.create_table('source_to_record',
    sa.Column('source_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('team_request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['player_id'], ['crm_test.player_records.id'], ),
    sa.ForeignKeyConstraint(['source_id'], ['crm_test.contacts.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['team_request_id'], ['crm_test.team_requests.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_source_to_record_player_id'), 'source_to_record', ['player_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_source_to_record_source_id'), 'source_to_record', ['source_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_source_to_record_team_request_id'), 'source_to_record', ['team_request_id'], unique=False, schema='crm_test')
    op.create_table('contract_uploads',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('contract_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['contract_id'], ['crm_test.contracts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_contract_uploads_contract_id'), 'contract_uploads', ['contract_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contract_uploads_created_at'), 'contract_uploads', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contract_uploads_created_by'), 'contract_uploads', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contract_uploads_is_sensitive'), 'contract_uploads', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contract_uploads_last_updated'), 'contract_uploads', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_contract_uploads_organization_id'), 'contract_uploads', ['organization_id'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_test_contract_uploads_organization_id'), table_name='contract_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contract_uploads_last_updated'), table_name='contract_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contract_uploads_is_sensitive'), table_name='contract_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contract_uploads_created_by'), table_name='contract_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contract_uploads_created_at'), table_name='contract_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contract_uploads_contract_id'), table_name='contract_uploads', schema='crm_test')
    op.drop_table('contract_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_source_to_record_team_request_id'), table_name='source_to_record', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_source_to_record_source_id'), table_name='source_to_record', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_source_to_record_player_id'), table_name='source_to_record', schema='crm_test')
    op.drop_table('source_to_record', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_reports_player_id'), table_name='reports', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_reports_organization_id'), table_name='reports', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_reports_last_updated'), table_name='reports', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_reports_is_sensitive'), table_name='reports', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_reports_created_by'), table_name='reports', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_reports_created_at'), table_name='reports', schema='crm_test')
    op.drop_table('reports', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_proposals_request_id'), table_name='proposals', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_proposals_player_id'), table_name='proposals', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_proposals_organization_id'), table_name='proposals', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_proposals_last_updated'), table_name='proposals', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_proposals_is_sensitive'), table_name='proposals', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_proposals_created_by'), table_name='proposals', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_proposals_created_at'), table_name='proposals', schema='crm_test')
    op.drop_table('proposals', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_record_changes_player_id'), table_name='player_record_changes', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_record_changes_edit_by'), table_name='player_record_changes', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_record_changes_edit_at'), table_name='player_record_changes', schema='crm_test')
    op.drop_table('player_record_changes', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_features_player_id'), table_name='player_features', schema='crm_test')
    op.drop_table('player_features', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_notifications_settings_user_id'), table_name='notifications_settings', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_notifications_settings_player_id'), table_name='notifications_settings', schema='crm_test')
    op.drop_table('notifications_settings', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contracts_teamId'), table_name='contracts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contracts_start_date'), table_name='contracts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contracts_player_id'), table_name='contracts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contracts_organization_id'), table_name='contracts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contracts_last_updated'), table_name='contracts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contracts_is_sensitive'), table_name='contracts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contracts_end_date'), table_name='contracts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contracts_created_by'), table_name='contracts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contracts_created_at'), table_name='contracts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contracts_agent_id'), table_name='contracts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contracts_active_status'), table_name='contracts', schema='crm_test')
    op.drop_table('contracts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_team_request_changes_team_request_id'), table_name='team_request_changes', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_team_request_changes_edit_by'), table_name='team_request_changes', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_team_request_changes_edit_at'), table_name='team_request_changes', schema='crm_test')
    op.drop_table('team_request_changes', schema='crm_test')
    op.drop_table('rank_outputs', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_records_playerId'), table_name='player_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_records_organization_id'), table_name='player_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_records_last_updated'), table_name='player_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_records_is_sensitive'), table_name='player_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_records_created_by'), table_name='player_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_records_created_at'), table_name='player_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_records_assigned_to_id'), table_name='player_records', schema='crm_test')
    op.drop_table('player_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_team_requests_teamId'), table_name='team_requests', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_team_requests_organization_id'), table_name='team_requests', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_team_requests_last_updated'), table_name='team_requests', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_team_requests_is_sensitive'), table_name='team_requests', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_team_requests_created_by'), table_name='team_requests', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_team_requests_created_at'), table_name='team_requests', schema='crm_test')
    op.drop_table('team_requests', schema='crm_test')
    op.drop_table('rank_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contacts_organization_id'), table_name='contacts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contacts_last_updated'), table_name='contacts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contacts_is_sensitive'), table_name='contacts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contacts_created_by'), table_name='contacts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_contacts_created_at'), table_name='contacts', schema='crm_test')
    op.drop_table('contacts', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_user_email'), table_name='user', schema='crm_test')
    op.drop_table('user', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_purchases_organization_id'), table_name='purchases', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_purchases_module_id'), table_name='purchases', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_purchases_created_at'), table_name='purchases', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_purchases_active_until'), table_name='purchases', schema='crm_test')
    op.drop_table('purchases', schema='crm_test')
    op.drop_table('user_roles', schema='crm_test')
    op.drop_table('organizations', schema='crm_test')
    op.drop_table('modules', schema='crm_test')
    # ### end Alembic commands ###