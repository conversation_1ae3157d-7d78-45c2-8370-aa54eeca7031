import uuid
from typing import Optional
from pydantic import BaseModel
from app.schemas.contact import ContactShort

class AssignedToRecordUpdate(BaseModel):
    contact_id: Optional[uuid.UUID]
    player_id: Optional[uuid.UUID]
    team_request_id: Optional[uuid.UUID]


class AssignedToRecordCreate(AssignedToRecordUpdate):
    contact_id: uuid.UUID
    player_id: Optional[uuid.UUID]
    team_request_id: Optional[uuid.UUID]


class AssignedToRecord(BaseModel):
    contact: ContactShort
    contact_id: uuid.UUID

    class Config:
        orm_mode = True