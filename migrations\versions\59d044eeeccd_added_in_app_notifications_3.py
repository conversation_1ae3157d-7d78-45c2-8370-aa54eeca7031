"""Added in app notifications -3

Revision ID: 59d044eeeccd
Revises: 16c5300ac8be
Create Date: 2024-08-26 16:38:04.448682

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '59d044eeeccd'
down_revision = '16c5300ac8be'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('platform_notifications', 'active',
               existing_type=sa.VARCHAR(),
               type_=sa.<PERSON>an(),
               existing_nullable=True,
               schema='crm_test',
               postgresql_using="active::boolean")
    op.create_index(op.f('ix_crm_test_platform_notifications_active'), 'platform_notifications', ['active'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_platform_notifications_created_for'), 'platform_notifications', ['created_for'], unique=False, schema='crm_test')
    op.create_foreign_key(None, 'platform_notifications', 'user', ['created_for'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'platform_notifications', schema='crm_test', type_='foreignkey')
    op.drop_index(op.f('ix_crm_test_platform_notifications_created_for'), table_name='platform_notifications', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_platform_notifications_active'), table_name='platform_notifications', schema='crm_test')
    op.alter_column('platform_notifications', 'active',
               existing_type=sa.Boolean(),
               type_=sa.VARCHAR(),
               existing_nullable=True,
               schema='crm_test')
    # ### end Alembic commands ###