import uuid
from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)
from typing import Optional

from app.schemas.enums import (
    ActivityStage,
)

from app.schemas.player_record import PlayerRec<PERSON>, PlayerRecordShort
from app.schemas.team_request import TeamRequest, TeamRequestShort


class ProposalUpdate(ExtendedUpdateBase):
    player_id: Optional[uuid.UUID]
    request_id: Optional[uuid.UUID]

    class Config:
        use_enum_values = True
        use_cache=True
        schema_extra = {
            "example": {
                "player_id": "56d9d54a-5cce-477f-9667-bf028d9c29f3",
                "request_id": "80218c15-670b-4656-922a-4a8941e46dc3",
                "created_at": "2022-08-10T08:40:41.016Z",
                "notes": "Good connection with club",
            }
        }


class ProposalCreate(ProposalUpdate, ExtendedCreateBase):
    player_id: uuid.UUID
    request_id: uuid.UUID
    stage: Optional[ActivityStage] = 'offered'


class Proposal(ProposalCreate, ExtendedBase):
    player: PlayerRecordShort
    team_request: TeamRequestShort

    class Config:
        orm_mode = True
        use_cache=True

TeamRequest.update_forward_refs(Proposal=Proposal)
PlayerRecord.update_forward_refs(Proposal=Proposal)
