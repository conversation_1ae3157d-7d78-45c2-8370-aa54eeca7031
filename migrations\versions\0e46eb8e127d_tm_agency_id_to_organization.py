"""TM Agency id to organization

Revision ID: 0e46eb8e127d
Revises: a2c02228e1c7
Create Date: 2025-04-14 16:30:18.351568

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0e46eb8e127d'
down_revision = 'a2c02228e1c7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('agency_id', sa.Integer(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organizations', 'agency_id', schema='crm_test')
    # ### end Alembic commands ###