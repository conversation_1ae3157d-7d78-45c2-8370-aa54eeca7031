from typing import Any, List, Optional, Dict
from fastapi import APIRouter, Depends, BackgroundTasks, Query, Body
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from app.utils import mail
from app.schemas.activity import (
    Activity,
    ActivityDeal,
    ActivityTask,
    ActivityCreate,
    ActivityUpdate,
    QuickCreateActivityPlayer,
    QuickCreateOpActivity,
    QuickCreateStaffActivity,
)
from app import crud, models
from app.api import deps, utils
from app.utils import snake_to_title
from datetime import datetime, date
from app.api.endpoints.contacts import read_contact
from app.api.endpoints.message_subscription import read_subscriptions_email
from app.api.endpoints.platform_notifications import create_in_app_notification
import uuid
import pandas as pd
from io import BytesIO, StringIO
from urllib.parse import quote
from sqlalchemy import text
from app.config import settings

router = APIRouter()


@router.get("/", response_model=Dict[str, Any])
def read_all_activities(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    # Pagination parameters
    page: int = Query(0, alias="offset", ge=0),
    limit: int = Query(10, le=1000),
    # Search parameter
    search: Optional[str] = Query(None, alias="search"),
    # Filter parameters
    activity_type: Optional[str] = Query(None, alias="type"),
    stage: Optional[List[str]] = Query(None),
    assigned_to_me: Optional[bool] = Query(None),
    assigned_to_contact_id: Optional[List[str]] = Query(None),
    created_by_me: Optional[bool] = Query(None),
    created_by_contact_id: Optional[List[str]] = Query(None),
    player_id: Optional[List[int]] = Query(None, alias="playerId"),
    team_id: Optional[List[int]] = Query(None, alias="teamId"),
    team_country: Optional[List[str]] = Query(None),
    team_league: Optional[List[str]] = Query(None),
    last_updated_start: Optional[datetime] = Query(None),
    last_updated_end: Optional[datetime] = Query(None),
    next_action_start: Optional[date] = Query(None),
    next_action_end: Optional[date] = Query(None),
    due_date_start: Optional[date] = Query(None),
    due_date_end: Optional[date] = Query(None),
    # Sorting parameters
    sort_by: Optional[str] = Query(None),
    sort_order: Optional[str] = Query("desc"),
) -> Any:
    """
    Retrieve paginated activities with comprehensive filtering and sorting.

    Search functionality:
    - search: Free text search across title, player name, team name, team country, and team league

    Filters available:
    - type: Filter by activity type (task/deal)
    - stage: Filter by activity stages (to_do, in_progress, done, etc.)
    - assigned_to_me: Filter activities assigned to current user
    - assigned_to_contact_id: Filter activities assigned to specific contacts
    - created_by_me: Filter activities created by current user
    - created_by_contact_id: Filter activities created by specific contacts
    - playerId: Filter by player IDs
    - teamId: Filter by team IDs
    - team_country: Filter by team countries (area_name)
    - team_league: Filter by team leagues
    - Date ranges: last_updated_start/end, next_action_start/end, due_date_start/end

    Sorting available on: title, type, stage, last_updated, created_at, next_action,
    due_date, player_name, team_name, team_country, team_league
    """
    utils.check_get_all(Activity, current_user)
    return crud.activity.get_activities_paginated_with_filters(
        db=db,
        org_id=current_user.organization_id,
        can_access_sensitive=utils.can_access_sensitive(current_user),
        page=page,
        limit=limit,
        search=search,
        activity_type=activity_type,
        stage=stage,
        assigned_to_me=assigned_to_me,
        assigned_to_contact_id=assigned_to_contact_id,
        created_by_me=created_by_me,
        created_by_contact_id=created_by_contact_id,
        player_id=player_id,
        team_id=team_id,
        team_country=team_country,
        team_league=team_league,
        last_updated_start=last_updated_start,
        last_updated_end=last_updated_end,
        next_action_start=next_action_start,
        next_action_end=next_action_end,
        due_date_start=due_date_start,
        due_date_end=due_date_end,
        sort_by=sort_by,
        sort_order=sort_order,
        current_user_id=str(current_user.id),
    )


@router.get("/notifications", response_model=Dict[str, Any])
def read_all_notifications(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    # Pagination parameters
    page: int = Query(0, alias="offset", ge=0),
    limit: int = Query(10, le=1000),
    # Search parameter
    search: Optional[str] = Query(None, alias="search"),
    # Filter parameters
    player_id: Optional[List[int]] = Query(None, alias="playerId"),
    team_id: Optional[List[int]] = Query(None, alias="teamId"),
    team_country: Optional[List[str]] = Query(None),
    team_league: Optional[List[str]] = Query(None),
    legal_type: Optional[List[str]] = Query(None),
    assigned_to_me: Optional[bool] = Query(None),
    assigned_to_contact_id: Optional[List[str]] = Query(None),
    assigned_to_email: Optional[List[str]] = Query(None),
    due_date_start: Optional[date] = Query(None),
    due_date_end: Optional[date] = Query(None),
    # Sorting parameters
    sort_by: Optional[str] = Query(None),
    sort_order: Optional[str] = Query("desc"),
) -> Any:
    """
    Retrieve paginated birthday and legal notifications with comprehensive filtering.

    Birthday notifications: From NotificationSettings with player_notifications=true
    Legal notifications: From Contract

    Special handling for commission_agreement contracts:
    - Returns multiple notifications (one per installment due_date)
    - Date filtering uses installment due_date instead of contract end_date
    - Each installment creates a separate notification row

    Search functionality:
    - search: Free text search across player name, team name, team country, and team league

    Filters available:
    - playerId: Filter by player IDs
    - teamId: Filter by team IDs (for legal notifications)
    - team_country: Filter by team countries (area_name)
    - team_league: Filter by team leagues
    - legal_type: Filter by contract types (mandate, commission_agreement, club_contract, etc.)
    - assigned_to_me: Filter notifications assigned to current user
    - assigned_to_contact_id: Filter notifications assigned to specific contacts (for birthday notifications)
    - assigned_to_email: Filter legal notifications assigned to specific emails (for legal notifications)
    - Date ranges: due_date_start/end (birthday or installment due_date for commission_agreement, contract end_date for others)

    Sorting available on: player_name, team_name, team_country, team_league, due_date, type
    """
    utils.check_get_all(Activity, current_user)
    return crud.activity.get_notifications_paginated_with_filters(
        db=db,
        org_id=current_user.organization_id,
        can_access_sensitive=utils.can_access_sensitive(current_user),
        page=page,
        limit=limit,
        search=search,
        player_id=player_id,
        team_id=team_id,
        team_country=team_country,
        team_league=team_league,
        legal_type=legal_type,
        assigned_to_me=assigned_to_me,
        assigned_to_contact_id=assigned_to_contact_id,
        assigned_to_email=assigned_to_email,
        due_date_start=due_date_start,
        due_date_end=due_date_end,
        sort_by=sort_by,
        sort_order=sort_order,
        current_user_id=str(current_user.id),
        current_user_email=current_user.email,
    )


@router.get("/player/{playerId}", response_model=List[Activity])
def read_all_activities_for_player(
    playerId: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all activities for a player.
    """
    utils.check_get_all(Activity, current_user)
    return crud.activity.get_activities_for_wyscout_player(
        db,
        current_user.organization_id,
        utils.can_access_sensitive(current_user),
        playerId,
    )


@router.get("/team/{teamId}", response_model=List[Activity])
def read_all_activities_for_team(
    teamId: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all activities for a team.
    """
    utils.check_get_all(Activity, current_user)
    can_access_sense = utils.can_access_sensitive(current_user)
    exclude = utils.exclude_based_on_role(current_user)

    activities = crud.activity.get_activities_for_wyscout_team(
        db,
        current_user.organization_id,
        can_access_sense,
        teamId,
    )
    if exclude:
        return_activities = [
            Activity.from_orm(x) for x in activities if exclude(x, current_user)
        ]
    else:
        return_activities = [Activity.from_orm(x) for x in activities]

    return return_activities


@router.get("/staff/{staff_id}", response_model=List[Activity])
def read_all_activities_for_staff(
    staff_id: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all activities for a staff.
    """
    utils.check_get_all(Activity, current_user)
    return crud.activity.get_activities_for_wyscout_staff(
        db,
        current_user.organization_id,
        utils.can_access_sensitive(current_user),
        staff_id,
    )


@router.post("/", response_model=Activity)
def create_activity(
    *,
    db: Session = Depends(deps.get_db),
    activity_in: ActivityCreate,
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new activity.
    """
    if activity_in.notes:
        activity_in.notes = [
            {
                "comment": activity_in.notes[0]["comment"],
                "creator": current_user.email,
                "time": datetime.now(),
            }
        ]
    activity = crud.activity.create_with_user_assigne(
        db=db,
        obj_in=activity_in,
        user=current_user,
    )
    if activity_in.assigned_to_record:
        for assigned_id in activity_in.assigned_to_record:
            background_tasks.add_task(
                mail.send_mail_new_activity,
                activity,
                current_user=current_user,
                db=db,
                read_contact_func=read_contact,
                read_subscription_func=read_subscriptions_email,
                create_in_app_notification=create_in_app_notification,
                assigned_id=assigned_id,
            )

    return activity


@router.post("/quick")
def create_quick_activity(
    *,
    db: Session = Depends(deps.get_db),
    activities_in: List[QuickCreateActivityPlayer] = Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new activity.
    """
    for activity in activities_in:
        date_deal_created = None if activity.stage != "offered" else datetime.now()
        player = crud.player_record.get_with_wyscout(
            db=db, wyId=activity.playerId, org_id=current_user.organization_id
        )
        deal_activity = ActivityCreate(
            playerId=activity.playerId,
            teamId=activity.teamId,
            stage=activity.stage,
            title=f"{activity.player_name} to {activity.team_name}",
            type=activity.type,
            assigned_to_record=[i.contact_id for i in player.assigned_to_record],
            date_deal_created=date_deal_created,
        )
        crud.activity.create_with_user_assigne(
            db=db,
            obj_in=deal_activity,
            user=current_user,
        )
    return None


@router.post("/opportunity/bulk")
def create_quick_activity(
    *,
    db: Session = Depends(deps.get_db),
    activities_in: List[QuickCreateOpActivity] = Body(...),
    assigned_to_record: Optional[List[uuid.UUID]] = Body(None),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new activity from op finder - bulk.
    """
    quick_deals = []
    for activity in activities_in:
        deal = ActivityCreate(
            playerId=activity.playerId,
            teamId=activity.teamId,
            stage="to_do",
            title=f"{activity.player_name} to {activity.team_name}",
            type="deal",
            description=activity.description,
            assigned_to_record=assigned_to_record,
        )
        quick_deals.append(deal)

    activities = crud.activity.bulk_create_with_user_and_assigned_to(
        db=db, objs_in=quick_deals, user=current_user
    )
    return None


@router.post("/quick-staff")
def create_quick_staff_activity(
    *,
    db: Session = Depends(deps.get_db),
    activities_in: List[QuickCreateStaffActivity] = Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new activity for staff.
    """
    for activity in activities_in:
        staff = crud.staff_record.get_staff_with_transfermarkt(
            db=db, tmId=activity.staff_id, org_id=current_user.organization_id
        )

        # Create the activity
        staff_activity = ActivityCreate(
            staff_id=activity.staff_id,
            teamId=activity.teamId,
            stage=activity.stage,
            title=f"{activity.staff_name} to {activity.team_name}",
            type=activity.type,
            assigned_to_record=(
                [i.contact_id for i in staff.assigned_to_record]
                if staff and hasattr(staff, "assigned_to_record")
                else None
            ),
        )

        crud.activity.create_with_user_assigne(
            db=db,
            obj_in=staff_activity,
            user=current_user,
        )
    return None


@router.put("/bulk", response_model=List[Activity])
def bulk_update_activities(
    *,
    db: Session = Depends(deps.get_db),
    activities_updates: Dict[str, Dict[str, Any]] = Body(...),
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Bulk update multiple activities using a dictionary format.
    """
    updated_activities = []

    for activity_id, update_data in activities_updates.items():
        activity = crud.activity.get_by_org(
            db=db, id=activity_id, org_id=current_user.organization_id
        )
        if not activity:
            continue

        # Only include fields that are explicitly provided in the request
        # Add required fields for tracking
        update_data["last_updated"] = datetime.now()
        update_data["updated_by_id"] = current_user.id

        if "assigned_to_record" not in update_data:
            if activity.assigned_to_record:
                update_data["assigned_to_record"] = [
                    a.contact_id for a in activity.assigned_to_record
                ]
            else:
                update_data["assigned_to_record"] = None

        # Create a dict with only the provided fields
        # Don't use Pydantic model directly to avoid setting defaults for missing fields
        activity_update_dict = {
            k: v for k, v in update_data.items() if k in update_data
        }

        updated_activity = crud.activity.update_with_changelog(
            db=db,
            record_id=activity_id,
            db_obj=activity,
            obj_in=activity_update_dict,
            change_model=models.ActivityChange,
            user=current_user,
            record_type="activity",
        )

        if update_data["assigned_to_record"]:
            for assigned_id in update_data["assigned_to_record"]:
                background_tasks.add_task(
                    mail.send_mail_new_activity,
                    updated_activity,
                    current_user=current_user,
                    db=db,
                    read_contact_func=read_contact,
                    read_subscription_func=read_subscriptions_email,
                    create_in_app_notification=create_in_app_notification,
                    assigned_id=assigned_id,
                    mail_type="update",
                )

        updated_activities.append(updated_activity)

    return updated_activities


@router.put("/{id}", response_model=Activity)
def update_activity(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    activity_in: ActivityUpdate,
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update an activity.
    """
    activity = crud.activity.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )
    activity_in.last_updated = datetime.now()
    activity_in.updated_by_id = current_user.id

    activity = crud.activity.update_with_changelog(
        db=db,
        record_id=id,
        db_obj=activity,
        obj_in=activity_in,
        change_model=models.ActivityChange,
        user=current_user,
        record_type="activity",
    )
    if activity_in.assigned_to_record:
        for assigned_id in activity_in.assigned_to_record:
            background_tasks.add_task(
                mail.send_mail_new_activity,
                activity,
                current_user=current_user,
                db=db,
                read_contact_func=read_contact,
                read_subscription_func=read_subscriptions_email,
                create_in_app_notification=create_in_app_notification,
                assigned_id=assigned_id,
                mail_type="update",
            )

    return activity


@router.get("/deals/{id}", response_model=ActivityDeal)
def read_activity(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get an activity by ID.
    """
    activity = crud.activity.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )
    return activity


@router.get("/tasks/{id}", response_model=ActivityTask)
def read_activity(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get an activity by ID.
    """
    activity = crud.activity.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )
    return activity


@router.delete("/{id}", response_model=Activity)
def delete_activity(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete an activity.
    """
    activity = crud.activity.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )
    utils.check_delete(activity, current_user)
    activity_out = Activity.from_orm(activity)
    crud.activity.remove(db=db, id=id)
    return activity_out


@router.get("/export/csv")
def export_activities_csv(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Export all activities for the organization as CSV file.
    Data is sorted by last_updated DESC.
    """
    # Get all activities for export
    activities_data = crud.activity.get_all_activities_for_export(
        db=db,
        org_id=current_user.organization_id,
        can_access_sensitive=utils.can_access_sensitive(current_user),
    )

    # Convert to DataFrame
    df = pd.DataFrame(activities_data)

    # Remove id and description columns
    if "id" in df.columns:
        df = df.drop(columns=["id"])
    if "description" in df.columns:
        df = df.drop(columns=["description"])

    # Handle empty data
    if df.empty:
        df = pd.DataFrame(
            columns=[
                "title",
                "type",
                "stage",
                "created_at",
                "last_updated",
                "next_action",
                "due_date",
                "date_deal_created",
                "player_full_name",
                "team_name",
                "team_country",
                "team_league",
                "created_by_full_name",
            ]
        )

    # Format datetime columns for better readability
    datetime_columns = [
        "created_at",
        "last_updated",
        "next_action",
        "due_date",
        "date_deal_created",
    ]
    for col in datetime_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col]).dt.strftime("%Y-%m-%d %H:%M:%S")

    # Convert stage values to Title Case
    if "stage" in df.columns:
        df["stage"] = df["stage"].apply(lambda x: snake_to_title(x) if x else x)

    # Convert type values to Title Case
    if "type" in df.columns:
        df["type"] = df["type"].apply(lambda x: snake_to_title(x) if x else x)

    # Rename columns from snake_case to Title Case
    df = df.rename(columns={col: snake_to_title(col) for col in df.columns})

    # Create CSV in memory
    csv_buffer = StringIO()
    df.to_csv(csv_buffer, index=False)
    csv_content = csv_buffer.getvalue()

    # Generate filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"activities_export_{timestamp}.csv"
    quoted_filename = quote(filename)

    headers = {"Content-Disposition": f"attachment; filename*=UTF-8''{quoted_filename}"}

    return StreamingResponse(
        BytesIO(csv_content.encode("utf-8")), media_type="text/csv", headers=headers
    )


@router.get("/export/excel")
def export_activities_excel(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Export all activities for the organization as Excel file.
    Data is sorted by last_updated DESC.
    """
    # Get all activities for export
    activities_data = crud.activity.get_all_activities_for_export(
        db=db,
        org_id=current_user.organization_id,
        can_access_sensitive=utils.can_access_sensitive(current_user),
    )

    # Convert to DataFrame
    df = pd.DataFrame(activities_data)

    # Remove id and description columns
    if "id" in df.columns:
        df = df.drop(columns=["id"])
    if "description" in df.columns:
        df = df.drop(columns=["description"])

    # Handle empty data
    if df.empty:
        df = pd.DataFrame(
            columns=[
                "title",
                "type",
                "stage",
                "created_at",
                "last_updated",
                "next_action",
                "due_date",
                "date_deal_created",
                "player_full_name",
                "team_name",
                "team_country",
                "team_league",
                "created_by_full_name",
            ]
        )

    # Format datetime columns for better readability
    datetime_columns = [
        "created_at",
        "last_updated",
        "next_action",
        "due_date",
        "date_deal_created",
    ]
    for col in datetime_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors="coerce")

    # Convert stage values to Title Case
    if "stage" in df.columns:
        df["stage"] = df["stage"].apply(lambda x: snake_to_title(x) if x else x)

    # Convert type values to Title Case
    if "type" in df.columns:
        df["type"] = df["type"].apply(lambda x: snake_to_title(x) if x else x)

    # Rename columns from snake_case to Title Case
    df = df.rename(columns={col: snake_to_title(col) for col in df.columns})

    # Create Excel in memory
    excel_buffer = BytesIO()
    with pd.ExcelWriter(excel_buffer, engine="openpyxl") as writer:
        df.to_excel(writer, sheet_name="Activities", index=False)

        # Auto-adjust column widths
        worksheet = writer.sheets["Activities"]
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            worksheet.column_dimensions[column_letter].width = adjusted_width

    excel_buffer.seek(0)

    # Generate filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"activities_export_{timestamp}.xlsx"
    quoted_filename = quote(filename)

    headers = {"Content-Disposition": f"attachment; filename*=UTF-8''{quoted_filename}"}

    return StreamingResponse(
        excel_buffer,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers=headers,
    )


@router.get("/filter_options", response_model=Dict[str, Any])
def get_activity_filter_options(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all available filter options for activities.

    Returns:
    - assigned_to: List of users who activities can be assigned to (id and name)
    - created_by: List of users who have created activities (id and email)
    - players: List of players associated with activities (id and name)
    - teams: List of teams associated with activities (id and name)
    - team_countries: List of unique team countries
    - team_leagues: List of unique team leagues
    """
    org_id = current_user.organization_id

    # Query for assigned users
    assigned_to_query = text(
        f"""
        SELECT DISTINCT c.id, CONCAT(c.first_name, ' ', c.last_name) as name
        FROM {settings.PG_SCHEMA}.assigned_to_record atr
        JOIN {settings.PG_SCHEMA}.contacts c ON c.id = atr.contact_id
        JOIN {settings.PG_SCHEMA}.activity a ON a.id = atr.activity_id
        WHERE a.organization_id = :org_id
        ORDER BY name
        """
    )

    # Query for activity creators
    created_by_query = text(
        f"""
        SELECT DISTINCT u.id, u.email
        FROM {settings.PG_SCHEMA}.activity a
        JOIN {settings.PG_SCHEMA}.user u ON u.id = a.created_by
        WHERE a.organization_id = :org_id
        ORDER BY u.email
        """
    )

    # Query for players
    players_query = text(
        f"""
        SELECT DISTINCT a."playerId" as id, CONCAT(pi."firstName", ' ', pi."lastName") as name
        FROM {settings.PG_SCHEMA}.activity a
        JOIN wyscout.player_info2 pi ON pi."playerId" = a."playerId"
        WHERE a.organization_id = :org_id AND a."playerId" IS NOT NULL
        ORDER BY name
        """
    )

    # Query for teams
    teams_query = text(
        f"""
        SELECT DISTINCT a."teamId" as id, ti.name
        FROM {settings.PG_SCHEMA}.activity a
        JOIN wyscout.team_info2 ti ON ti."teamId" = a."teamId"
        WHERE a.organization_id = :org_id AND a."teamId" IS NOT NULL
        ORDER BY ti.name
        """
    )

    # Query for team countries
    team_countries_query = text(
        f"""
        SELECT DISTINCT ti.area_name
        FROM {settings.PG_SCHEMA}.activity a
        JOIN wyscout.team_info2 ti ON ti."teamId" = a."teamId"
        WHERE a.organization_id = :org_id AND ti.area_name IS NOT NULL
        ORDER BY ti.area_name
        """
    )

    # Query for team leagues
    team_leagues_query = text(
        f"""
        SELECT DISTINCT ti.league_name
        FROM {settings.PG_SCHEMA}.activity a
        JOIN wyscout.team_info2 ti ON ti."teamId" = a."teamId"
        WHERE a.organization_id = :org_id AND ti.league_name IS NOT NULL
        ORDER BY ti.league_name
        """
    )

    # Execute all queries
    assigned_to = db.execute(assigned_to_query, {"org_id": org_id}).all()
    created_by = db.execute(created_by_query, {"org_id": org_id}).all()
    players = db.execute(players_query, {"org_id": org_id}).all()
    teams = db.execute(teams_query, {"org_id": org_id}).all()
    team_countries = db.execute(team_countries_query, {"org_id": org_id}).all()
    team_leagues = db.execute(team_leagues_query, {"org_id": org_id}).all()

    # Format the results
    return {
        "assigned_to": [
            {"id": str(user.id), "name": user.name} for user in assigned_to
        ],
        "created_by": [
            {"id": str(user.id), "email": user.email} for user in created_by
        ],
        "players": [{"id": player.id, "name": player.name} for player in players],
        "teams": [{"id": team.id, "name": team.name} for team in teams],
        "team_countries": [country.area_name for country in team_countries],
        "team_leagues": [league.league_name for league in team_leagues],
    }
