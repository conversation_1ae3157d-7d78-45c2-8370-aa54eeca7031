from sqlalchemy import create_engine
import sys
import uuid
from sqlalchemy.sql import text
from datetime import datetime

sys.path.append("c:\\Projects\\shadow-eleven-backend")
from app.config import settings
import pandas as pd


def add_purchases_decorator(func, modules: list = None):
    def wrapper(*args, **kwargs):
        (org_uuid, schema) = func(*args, **kwargs)
        add_purchases(org_uuid, schema, modules)
    return wrapper

# Not supplying a module will get them all
def add_purchases(
    org_uuid: uuid, schema: str = settings.PG_SCHEMA, modules: list = None
):
    if modules is None:
        modules = []
    engine = create_engine(settings.PG_URL)
    if not modules:
        dd = pd.read_sql(f"SELECT id from {schema}.modules", engine)
    else:
        dd = pd.read_sql(
            f"select * from {schema}.modules where name in"
            f" ({', '.join(['%s' for _ in modules])})",
            engine,
            params=(modules),
        )

    # Inserting the actual purchases
    values = [
        f"""('{uuid.uuid4()}', '{org_uuid}', '{module_id}', '{datetime.now()}',
      '420', '2122-08-20 10:34:16.452', True)"""
        for module_id in dd["id"]
    ]
    with engine.connect() as conn:
        conn.execute(
        text(
            f"""INSERT INTO {schema}.purchases (id, organization_id, module_id, 
            created_at, price, active_until, active) VALUES {', '.join(values)}"""
        )
    )

if __name__ == "__main__":
    add_purchases("5845d361-1456-4d61-97c6-976fb8fec80f", modules=["ranks", "gbe"])
