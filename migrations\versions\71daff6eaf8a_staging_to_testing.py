"""Staging to testing

Revision ID: 71daff6eaf8a
Revises: 6a3faad29b05
Create Date: 2024-11-22 17:04:29.580529

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '71daff6eaf8a'
down_revision = '6a3faad29b05'
branch_labels = None
depends_on = None
import fastapi_users_db_sqlalchemy

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('community_deal',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('community_proposal_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('community_deal_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('feedback', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('organization_proposed_to', sa.String(), nullable=True),
    sa.Column('email_proposed_to', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['community_proposal_id'], ['crm.community_proposals.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['crm.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_community_deal_community_proposal_id'), 'community_deal', ['community_proposal_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_community_deal_created_at'), 'community_deal', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_community_deal_created_by'), 'community_deal', ['created_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_community_deal_is_sensitive'), 'community_deal', ['is_sensitive'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_community_deal_last_updated'), 'community_deal', ['last_updated'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_community_deal_organization_id'), 'community_deal', ['organization_id'], unique=False, schema='crm')
    op.add_column('community_proposals', sa.Column('video_link', sa.String(), nullable=True), schema='crm')
    op.add_column('platform_notifications', sa.Column('community_deal_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm')
    op.drop_constraint('platform_notifications_community_proposal_id_fkey', 'platform_notifications', schema='crm', type_='foreignkey')
    op.create_foreign_key(None, 'platform_notifications', 'community_deal', ['community_deal_id'], ['id'], source_schema='crm', referent_schema='crm', ondelete='CASCADE')
    op.drop_column('platform_notifications', 'community_proposal_id', schema='crm')
    op.create_foreign_key(None, 'player_features', 'player_records', ['player_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.add_column('team_requests', sa.Column('club_contact', sa.String(), nullable=True), schema='crm')
    op.add_column('team_requests', sa.Column('eu_passport', sa.Boolean(), nullable=True), schema='crm')
    op.drop_column('team_requests', 'reason_for_outcome', schema='crm')
    op.drop_column('team_requests', 'stage', schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('team_requests', sa.Column('stage', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm')
    op.add_column('team_requests', sa.Column('reason_for_outcome', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm')
    op.drop_column('team_requests', 'eu_passport', schema='crm')
    op.drop_column('team_requests', 'club_contact', schema='crm')
    op.drop_constraint(None, 'player_features', schema='crm', type_='foreignkey')
    op.add_column('platform_notifications', sa.Column('community_proposal_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm')
    op.drop_constraint(None, 'platform_notifications', schema='crm', type_='foreignkey')
    op.create_foreign_key('platform_notifications_community_proposal_id_fkey', 'platform_notifications', 'community_proposals', ['community_proposal_id'], ['id'], source_schema='crm', referent_schema='crm', ondelete='CASCADE')
    op.drop_column('platform_notifications', 'community_deal_id', schema='crm')
    op.drop_column('community_proposals', 'video_link', schema='crm')
    op.drop_index(op.f('ix_crm_community_deal_organization_id'), table_name='community_deal', schema='crm')
    op.drop_index(op.f('ix_crm_community_deal_last_updated'), table_name='community_deal', schema='crm')
    op.drop_index(op.f('ix_crm_community_deal_is_sensitive'), table_name='community_deal', schema='crm')
    op.drop_index(op.f('ix_crm_community_deal_created_by'), table_name='community_deal', schema='crm')
    op.drop_index(op.f('ix_crm_community_deal_created_at'), table_name='community_deal', schema='crm')
    op.drop_index(op.f('ix_crm_community_deal_community_proposal_id'), table_name='community_deal', schema='crm')
    op.drop_table('community_deal', schema='crm')
    # ### end Alembic commands ###