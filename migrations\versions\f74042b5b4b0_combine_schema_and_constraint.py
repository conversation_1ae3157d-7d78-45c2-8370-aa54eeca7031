"""combine schema and constraint

Revision ID: f74042b5b4b0
Revises: 17a2c09e7478
Create Date: 2023-07-07 14:10:04.932536

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f74042b5b4b0'
down_revision = '17a2c09e7478'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('notifications_settings',
    sa.<PERSON>umn('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('contract_notifications', sa.<PERSON>(), nullable=True),
    sa.Column('player_notifications', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['player_id'], ['crm_dev.player_records.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['crm_dev.user.id'], ),
    sa.PrimaryKeyConstraint('id', 'user_id', 'player_id'),
    sa.UniqueConstraint('user_id', 'player_id', name='_user_player_uc_new'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_notifications_settings_player_id'), 'notifications_settings', ['player_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_notifications_settings_user_id'), 'notifications_settings', ['user_id'], unique=False, schema='crm_dev')
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_notifications_settings_user_id'), table_name='notifications_settings', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_notifications_settings_player_id'), table_name='notifications_settings', schema='crm_dev')
    op.drop_table('notifications_settings', schema='crm_dev')
    # ### end Alembic commands ###