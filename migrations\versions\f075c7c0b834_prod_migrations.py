"""Prod migrations

Revision ID: f075c7c0b834
Revises: 755462ceb7e5
Create Date: 2022-11-09 11:53:11.880070

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f075c7c0b834'
down_revision = '755462ceb7e5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('source_to_record',
    sa.Column('source_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('team_request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['player_id'], ['crm.player_records.id'], ),
    sa.ForeignKeyConstraint(['source_id'], ['crm.contacts.id'], ),
    sa.ForeignKeyConstraint(['team_request_id'], ['crm.team_requests.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_source_to_record_player_id'), 'source_to_record', ['player_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_source_to_record_source_id'), 'source_to_record', ['source_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_source_to_record_team_request_id'), 'source_to_record', ['team_request_id'], unique=False, schema='crm')
    op.drop_index('ix_crm_player_records_source_id', table_name='player_records', schema='crm')
    op.drop_constraint('player_records_source_id_fkey', 'player_records', schema='crm', type_='foreignkey')
    op.drop_column('player_records', 'source_id', schema='crm')
    op.drop_constraint('team_requests_source_id_fkey', 'team_requests', schema='crm', type_='foreignkey')
    op.drop_column('team_requests', 'source_id', schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('team_requests', sa.Column('source_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm')
    op.create_foreign_key('team_requests_source_id_fkey', 'team_requests', 'contacts', ['source_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.add_column('player_records', sa.Column('source_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm')
    op.create_foreign_key('player_records_source_id_fkey', 'player_records', 'contacts', ['source_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_index('ix_crm_player_records_source_id', 'player_records', ['source_id'], unique=False, schema='crm')
    op.drop_index(op.f('ix_crm_source_to_record_team_request_id'), table_name='source_to_record', schema='crm')
    op.drop_index(op.f('ix_crm_source_to_record_source_id'), table_name='source_to_record', schema='crm')
    op.drop_index(op.f('ix_crm_source_to_record_player_id'), table_name='source_to_record', schema='crm')
    op.drop_table('source_to_record', schema='crm')
    # ### end Alembic commands ###