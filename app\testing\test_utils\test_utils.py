from app.utils.base import create_user, create_org, delete_org, delete_user, update_modules_of_orgs
import uuid
import pytest
from sqlalchemy import create_engine
from app.config import settings
import pandas as pd

org_name = "testOrg"
email = "<EMAIL>"
pwd="testPass"
is_active = True
is_superuser = False
is_verified= True
is_enabled= True
role = 'user_full'
user_id = uuid.uuid4()
engine = create_engine(settings.PG_URL)

def test_create_org():
    org_id_original = uuid.uuid4()
    mdls = ['ranks']
    delete_org(name=org_name)
    org_id = create_org(org_name, "Temporary organization to perform tests", modules = mdls, org_uuid=org_id_original)

    assert org_id == str(org_id_original)
    
    df = pd.read_sql(f'''select * from {settings.PG_SCHEMA}.organizations where "id" = '{str(org_id)}' ''', engine)
    purchases_df = pd.read_sql(f'''select * from {settings.PG_SCHEMA}.purchases where "organization_id" = '{str(org_id)}' ''', engine)

    delete_org(org_id)
    delete_org(name=org_name)

    assert df.shape[0] == 1
    assert purchases_df.shape[0] == len(mdls)


def test_create_user_noorg():
    non_existent_org = "noOrg"
    with pytest.raises(Exception) as e_info:
        delete_user(email=email)
        _ = create_user(email, pwd, is_active, is_superuser, is_verified, is_enabled, non_existent_org, role, settings.PG_SCHEMA, user_id)

def test_create_user_with_org():
    org_id_original = uuid.uuid4()
    delete_org(name=org_name)

    org_id = create_org(org_name, "Temporary organization to perform tests", modules = None, org_uuid=org_id_original)
    delete_user(email=email)
    _ = create_user(email, pwd, is_active, is_superuser, is_verified, is_enabled, org_id, role, settings.PG_SCHEMA, user_id)

    df = pd.read_sql(f'''select * from {settings.PG_SCHEMA}.user where "id" = '{str(user_id)}' ''', engine)
    delete_org(org_id)
    delete_org(name=org_name)

    assert df.shape[0] == 1
    assert str(df.organization_id.values[0]) == str(org_id_original)

    delete_user(user_id)

def test_update_all_modules():
    org_id_original = uuid.uuid4()
    new_modules = ['ranks', 'legal']

    delete_org(name=org_name)
    org_id = create_org(org_name, "Temporary organization to perform tests", modules = None, org_uuid=org_id_original)
    update_modules_of_orgs(modules=new_modules, org_id=str(org_id_original))

    purchases_df = pd.read_sql(f'''select "name" from {settings.PG_SCHEMA}.modules where "id" in (select "module_id" from {settings.PG_SCHEMA}.purchases where "organization_id" = '{str(org_id_original)}')''', engine)
    delete_org(org_id)
    delete_org(name=org_name)

    assert len(set(list(purchases_df.name)).difference(set(new_modules))) == 0

def test_add_new_modules():
    org_id_original = uuid.uuid4()
    delete_org(name=org_name)
    org_id = create_org(org_name, "Temporary organization to perform tests", modules = ['ranks'], org_uuid=org_id_original)
    
    old_modules = list(pd.read_sql(f'''select "name" from {settings.PG_SCHEMA}.modules where "id" in (select "module_id" from {settings.PG_SCHEMA}.purchases where "organization_id" = '{str(org_id)}') ''', engine).name)

    additional_modules = ['gbe']

    update_modules_of_orgs(modules=additional_modules, org_id=str(org_id_original), append=True)

    all_modules = [*old_modules, *additional_modules]

    purchases_df = pd.read_sql(f'''select "name" from {settings.PG_SCHEMA}.modules where "id" in (select "module_id" from {settings.PG_SCHEMA}.purchases where "organization_id" = '{str(org_id_original)}')''', engine)
    delete_org(org_id)
    delete_org(name=org_name)

    assert len(set(list(purchases_df.name)).difference(set(all_modules))) == 0


def test_delete_org_by_name():
    org_name = 'test_org_to_delete'
    org_id_original = uuid.uuid4()
    delete_org(name=org_name)
    org_id = create_org(org_name, "Temporary organization to perform tests", modules = ['ranks'], org_uuid=org_id_original)
    delete_org(name=org_name)

    df = pd.read_sql(f'''select * from {settings.PG_SCHEMA}.organizations where "name" = '{str(org_name)}' ''', engine)

    assert df.shape[0] == 0

def test_delete_org_by_id():
    org_name = 'test_org_to_delete'
    delete_org(name=org_name)
    org_id_original = uuid.uuid4()
    org_id = create_org(org_name, "Temporary organization to perform tests", modules = ['ranks'], org_uuid=org_id_original)
    delete_org(org_id=org_id)

    df = pd.read_sql(f'''select * from {settings.PG_SCHEMA}.organizations where "name" = '{str(org_name)}' ''', engine)

    assert df.shape[0] == 0

