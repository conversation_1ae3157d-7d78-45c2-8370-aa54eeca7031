"""Activity due_date and next_action - only a date

Revision ID: b829f4870c19
Revises: d15c48f2e747
Create Date: 2025-07-07 13:38:05.617700

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b829f4870c19'
down_revision = 'd15c48f2e747'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('activity', 'next_action',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.Date(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('activity', 'due_date',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.Date(),
               existing_nullable=True,
               schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    op.alter_column('activity', 'due_date',
               existing_type=sa.Date(),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('activity', 'next_action',
               existing_type=sa.Date(),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               schema='crm_test')
    # ### end Alembic commands ###