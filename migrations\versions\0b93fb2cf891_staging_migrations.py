"""Staging migrations

Revision ID: 0b93fb2cf891
Revises: 669fa4a79966
Create Date: 2025-04-04 20:18:02.333824

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0b93fb2cf891'
down_revision = '669fa4a79966'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('share_tokens',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('resource_type', sa.String(length=50), nullable=False),
    sa.Column('resource_id', sa.UUID(), nullable=False),
    sa.Column('token', sa.UUID(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('is_disabled', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('token'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_share_tokens_id'), 'share_tokens', ['id'], unique=False, schema='crm_dev')
    op.drop_index('idx_team_activities', table_name='activity', schema='crm_dev')
    op.add_column('field_players', sa.Column('video_link', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('organizations', sa.Column('type', sa.String(), nullable=True), schema='crm_dev')
    op.drop_index('idx_player_records', table_name='player_records', schema='crm_dev')
    op.drop_index('idx_team_requests', table_name='team_requests', schema='crm_dev')
    op.drop_index('idx_team_requests_position', table_name='team_requests', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_team_requests_position', 'team_requests', ['position'], unique=False, schema='crm_dev')
    op.create_index('idx_team_requests', 'team_requests', ['status'], unique=False, schema='crm_dev')
    op.create_index('idx_player_records', 'player_records', ['control_stage'], unique=False, schema='crm_dev')
    op.drop_column('organizations', 'type', schema='crm_dev')
    op.drop_column('field_players', 'video_link', schema='crm_dev')
    op.drop_constraint(None, 'activity', schema='crm_dev', type_='foreignkey')
    op.create_index('idx_team_activities', 'activity', ['stage'], unique=False, schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_share_tokens_id'), table_name='share_tokens', schema='crm_dev')
    op.drop_table('share_tokens', schema='crm_dev')
    # ### end Alembic commands ###