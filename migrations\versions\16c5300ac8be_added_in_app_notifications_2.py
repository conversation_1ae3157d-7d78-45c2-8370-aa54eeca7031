"""Added in app notifications -2

Revision ID: 16c5300ac8be
Revises: b19144235cc5
Create Date: 2024-08-26 14:10:30.532130

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy
# revision identifiers, used by Alembic.
revision = '16c5300ac8be'
down_revision = 'b19144235cc5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('platform_notifications',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.<PERSON>(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('active', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('foreign_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_for', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_platform_notifications_created_at'), 'platform_notifications', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_platform_notifications_created_by'), 'platform_notifications', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_platform_notifications_is_sensitive'), 'platform_notifications', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_platform_notifications_last_updated'), 'platform_notifications', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_platform_notifications_organization_id'), 'platform_notifications', ['organization_id'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_test_platform_notifications_organization_id'), table_name='platform_notifications', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_platform_notifications_last_updated'), table_name='platform_notifications', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_platform_notifications_is_sensitive'), table_name='platform_notifications', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_platform_notifications_created_by'), table_name='platform_notifications', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_platform_notifications_created_at'), table_name='platform_notifications', schema='crm_test')
    op.drop_table('platform_notifications', schema='crm_test')
    # ### end Alembic commands ###