from typing import List
from datetime import date
from sqlalchemy.orm import Session

import pandas as pd
from app.db.session import engine

def fetch_club_finder_results(
    db: Session,
    playerId: str,
    league: bool,
    culture: bool,
    position: bool,
    division_level: List[str],
) -> List[dict]:
    """
    Encapsulates the 'club_finder' logic:
      - get culture, region, role_name for the player
      - run the big query that finds staff members
      - for each staff, retrieve the current club info
      - build club_finder_results
    Returns a list of dicts.
    """

    # 1) Gather the relevant data from Wyscout
    player_to_transfer = pd.read_sql(
        f"""
        SELECT c.culture,
               c1.region as league_region,
               pi2.role_name,
               pi2.primary_ws_position,
               pi2.team_area_name
        FROM wyscout.player_info2 pi2
        JOIN public.countries c
          ON c.alpha3code = pi2."birthArea_alpha3code"
        LEFT JOIN public.countries c1
          ON c1.country_name = pi2.team_area_name
        WHERE "playerId" = {playerId}
        """,
        engine,
    )

    if player_to_transfer.empty:
        # Return empty list or raise an exception?
        return []

    culture_q = player_to_transfer.iloc[0]["culture"]
    left_region_q = player_to_transfer.iloc[0]["league_region"] or "none"
    role_name = player_to_transfer.iloc[0]["role_name"]
    team_area_name = player_to_transfer.iloc[0]["team_area_name"]

    # If team_area_name is empty, fallback approach
    if not team_area_name:
        last_team_player = pd.read_sql(
            f"""
            SELECT ti.area_name, c.region as league_region
            FROM wyscout.player_match_info sp
            JOIN wyscout.team_info2 ti
              ON ti."teamId" = sp."teamId"
            JOIN public.countries c
              ON c.country_name = ti.area_name
            WHERE "playerId" = '{playerId}'
            ORDER BY date DESC
            LIMIT 1
            """,
            engine,
        )
        if not last_team_player.empty:
            left_region_q = last_team_player.iloc[0]["league_region"] or "none"
            team_area_name = last_team_player.iloc[0]["area_name"]

    # 2) Build the main staff query
    query = f"""
    WITH main_tbl AS (
        SELECT *
        FROM (
            SELECT sd.staff_id,
                   sd.current_team_id,
                   tt.joined_id as buying_id,
                   tt.joined_name as buying_team,
                   td."shortName" as player_name,
                   tt."date" as transfer_date,
                   tt.tm_player_id,
                   tt.left_id as selling_id,
                   tt.left_name as selling_team,
                   sd.role as staff_role_at_transfer,
                   sd.name as staff_name
            FROM transfermarkt.staff_data sd
            JOIN transfermarkt.tm_transfers tt
              ON tt.joined_id = sd.current_team_id
            JOIN transfermarkt.tm_to_ws_ids ttwi
              ON tt.tm_player_id = ttwi.tm_player_id
            JOIN wyscout.player_info2 td
              ON td."playerId" = ttwi."playerId"
            LEFT JOIN transfermarkt.tm_teams tt2
              ON tt2.team_id = tt.left_id
            LEFT JOIN transfermarkt.tm_teams tt3
              ON tt3.team_id = tt.joined_id
            WHERE sd.role IN (
                'Manager','Assistant Manager','Caretaker Manager','Sporting Director',
                'Scout','Director of Football','Technical Director','Chief Scout',
                'Chief Analyst','Head of Scouting','Head of Football Operations',
                'Managing Director Sport','Managing Director Professional Football',
                'Director of Professional Football and Scout','Global Sports Director',
                'Sporting CEO, Director of Sport','President','Owner','Chief Executive Officer'
            )
              AND sd.status = 'alive'
              AND tt.joined_name IS NOT NULL
              AND appointed::date < tt."date"
              AND coalesce(in_charge_until, CURRENT_DATE::text)::date >= tt."date"
              AND tt.new_type != 'back from loan'
              AND tt.joined_id NOT IN ('515','75')
    """

    # 3) Add optional filters (league, culture, division_level, position)
    if league:
        if left_region_q.lower() == "macedonia fyr":
            query += f"""
            AND tt2.league_country IN (
                SELECT 'North Macedonia'
                FROM public.countries
                WHERE region = '{left_region_q}'
            )
            AND tt3.league_country NOT IN (
                SELECT 'North Macedonia'
                FROM public.countries
                WHERE region = '{left_region_q}'
            )
            """
        elif left_region_q.lower() == "turkey":
            query += f"""
            AND tt2.league_country IN (
                SELECT 'Türkiye'
                FROM public.countries
                WHERE region = '{left_region_q}'
            )
            AND tt3.league_country NOT IN (
                SELECT 'Türkiye'
                FROM public.countries
                WHERE region = '{left_region_q}'
            )
            """
        else:
            query += f"""
            AND coalesce(tt2.league_country, 'none') IN (
                SELECT country_name
                FROM public.countries
                WHERE region = '{left_region_q}'
            )
            AND coalesce(tt3.league_country, 'none') NOT IN (
                SELECT country_name
                FROM public.countries
                WHERE region = '{left_region_q}'
            )
            """

    if culture:
        query += f"""
        AND td."birthArea_name" IN (
            SELECT country_name
            FROM public.countries
            WHERE culture = '{culture_q}'
        )
        """

    if division_level and len(division_level) > 0:
        unknown_division = any(dl.lower() == "unknown division" for dl in division_level)
        cleaned_div_levels = [
            f"'{dl}'" for dl in division_level if dl.lower() != "unknown division"
        ]
        if unknown_division:
            if cleaned_div_levels:
                query += f"""
                AND (
                    tt2.league_tier IN ({','.join(cleaned_div_levels)})
                    OR tt2.league_country IS NULL
                )
                """
            else:
                query += "AND tt2.league_country IS NULL "
        else:
            query += f"AND tt2.league_tier IN ({','.join(cleaned_div_levels)}) "

    if position:
        # Filter on the player's role
        query += f"AND td.role_name = '{role_name}' "

    query += f"""
        ) AS s
    ),
    main_tbl_w_min_date AS (
        SELECT *,
               MIN("transfer_date"::date) OVER (
                   PARTITION BY "tm_player_id","buying_team"
               ) AS player_first_join
        FROM main_tbl
    )
    SELECT DISTINCT ON ("tm_player_id","staff_id","buying_team") *
    FROM main_tbl_w_min_date
    WHERE "transfer_date" <= player_first_join::date + interval '18 months'
    """

    relevant_staff = pd.read_sql(query, engine).fillna("")
    if relevant_staff.empty:
        return []

    # Group staff members
    dd = {}
    for _, row in relevant_staff.iterrows():
        dd.setdefault(row["staff_id"], []).append(row.to_dict())

    sorted_staff_ids = sorted(dd.keys(), key=lambda s: len(dd[s]), reverse=True)

    # 4) For each staff, figure out their current club
    def get_club_for_staff(staff_ids_list):
        if not staff_ids_list:
            return pd.DataFrame()

        staff_ids_str = ", ".join(f"'{sid}'" for sid in staff_ids_list)
        query_staff = f"""
            SELECT sd.staff_id,
                   sd.role as current_role,
                   tt.team_id AS transfermarkt_team_id,
                   tt.team_name,
                   tt.league_name,
                   tt.league_country
            FROM transfermarkt.staff_data sd
            JOIN transfermarkt.tm_teams tt
               ON tt.team_id = sd.current_team_id
            JOIN transfermarkt.transfermarkt_data td
               ON tt.team_id = td.current_club_id
            WHERE staff_id IN ({staff_ids_str})
              AND (in_charge_until IS NULL OR in_charge_until > '{date.today()}')
              AND tt.league_country != '{team_area_name}'
            GROUP BY sd.staff_id ,sd.role, tt.team_id, tt.team_name, tt.league_name, tt.league_country
        """
        return pd.read_sql(query_staff, engine)

    clubs_df = get_club_for_staff(sorted_staff_ids)
    if clubs_df.empty:
        return []

    # Build the final list
    used_staff_ids = set()
    rows_dict = {}
    for _, r in clubs_df.iterrows():
        sid = r["staff_id"]
        tname = r["team_name"]
        tm_tid = r["transfermarkt_team_id"]
        live_role = r["current_role"]

        if sid not in used_staff_ids:
            for tr in dd[sid]:
                tr["current_role"] = live_role
            if tname not in rows_dict:
                rows_dict[tname] = {
                    "current_team": tname,
                    "transfermarkt_team_id": tm_tid,
                    "current_league": r["league_name"],
                    "transfer_count": len(dd[sid]),
                    "staff_id": sid,
                    "league_country": r["league_country"],
                    "transfers": dd[sid],  # the list of staff rows
                }
            else:
                # If we already had tname, just update transfer_count if bigger
                if len(dd[sid]) > rows_dict[tname]["transfer_count"]:
                    rows_dict[tname]["transfer_count"] = len(dd[sid])
                rows_dict[tname]["transfers"].extend(dd[sid])
                rows_dict[tname]["current_role"] = r["current_role"]
            used_staff_ids.add(sid)

    # Turn the rows_dict into a list
    club_finder_results = list(rows_dict.values())

    return club_finder_results
