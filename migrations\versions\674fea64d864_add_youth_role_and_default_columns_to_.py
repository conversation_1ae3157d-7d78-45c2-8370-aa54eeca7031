"""Add youth, role and default columns  to rank records table structure

Revision ID: 674fea64d864
Revises: 1eaa643ca97d
Create Date: 2022-08-24 16:26:03.344386

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '674fea64d864'
down_revision = '1eaa643ca97d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rank_records', sa.Column('youth', sa.<PERSON>(), nullable=True), schema='crm_dev')
    op.add_column('rank_records', sa.Column('target_position', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('rank_records', sa.Column('default_quality', sa.Bo<PERSON>(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rank_records', 'default_quality', schema='crm_dev')
    op.drop_column('rank_records', 'target_position', schema='crm_dev')
    op.drop_column('rank_records', 'youth', schema='crm_dev')
    # ### end Alembic commands ###