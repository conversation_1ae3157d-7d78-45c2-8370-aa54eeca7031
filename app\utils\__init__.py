from typing import List
from fastapi.encoders import jsonable_encoder
from app.schemas.change import ChangeCreate
import random
import string
import httpx
from pyfcm import FCMNotification

from app.config import settings
push_service = FCMNotification(service_account_file=settings.FIREBASE_CREDS, project_id=settings.PROJECT_ID)

def send_token_push(title, body, tokens, path):
    for token in tokens:
        push_service.notify(fcm_token=token, notification_title=title, notification_body=body,
                             notification_image="https://ensk.ai/static/favicon-fee4a6ac1846d5063a1bb2c0a982c7d8.ico",
                             data_payload={"path": path})


async def get_async_response(url, auth, params = None):
    async with httpx.AsyncClient() as client:
        # Make the asynchronous request to the external API
        response = await client.get(url,
            auth=auth,
            params=params, timeout=None)
        return response
    
async def get_async_post_response(url, auth, json = None):
    async with httpx.AsyncClient() as client:
        # Make the asynchronous request to the external API
        response = await client.post(url, 
            json=json,
            auth=auth,
            timeout=None)
        return response

def compare_version_of_objects(old_object, new_object: dict, editor_id) -> List[dict]:
    """
    Compare two objects and return a Change object.
    """
    return [
        jsonable_encoder(
            ChangeCreate(
                edit_by=editor_id,
                field=key,
                updated=value,
                previous=getattr(old_object, key),
            )
        )
        for key, value in new_object.items()
        if key != "last_updated" and value not in [getattr(old_object, key) ]
    ]


def snake_to_title(x: str) -> str:
    """
    Convert snake_case to Title Case.
    """
    return " ".join(word.capitalize() for word in str(x).split("_"))



"""
Convert suitability score to text -> 0/5 -> Unsuitable.
"""
suitability_score_to_text = {
    '5': "Top Match",
    '4.5': "Excellent Match",
    '4': "Good Match",
    '3.5': "Possible Match",
    '3': "Long shot",
    '2.5': "Unlikely Match",
    '2': "Poor Match",
    '1.5': "Unsuitable",
    '1': "Unsuitable",
    '0': "Unsuitable",
}
def generate_password(length=12):
    # Define the possible characters to include in the password
    characters = string.ascii_letters + string.digits + string.punctuation
    # Use random.choices() to select a random character from the characters string for the specified length
    password = ''.join(random.choices(characters, k=length))
    return password

def get_url_frontend():
    url = settings.FRONTEND_URL_TESTING
    if settings.STAGING:
        url = settings.FRONTEND_URL_STAGING
    elif settings.PROD:
        url = settings.FRONTEND_URL_PROD
    return url

contries_dict = {
  'Afghanistan': "af",
  'Alabama': "us-al",
  'Alaska': "us-ak",
  'Albania': "al",
  'Algeria': "dz",
  'American Samoa': "as",
  'Andorra': "ad",
  'Angola': "ao",
  'Anguilla': "ai",
  'Antigua and Barbuda': "ag",
  'Argentina': "ar",
  'Arizona': "us-az",
  'Arkansas': "us-ar",
  'Armenia': "am",
  'Aruba': "aw",
  'Australia': "au",
  'Austria': "at",
  'Azerbaijan': "az",
  'Bahamas': "bs",
  'Bahrain': "bh",
  'Burkina Faso': 'bf',
  'Bangladesh': "bd",
  'Barbados': "bb",
  'Belarus': "by",
  'Belgium': "be",
  'Belize': "bz",
  'Benin': "bj",
  'Bermuda': "bm",
  'Bhutan': "bt",
  'Bolivia': "bo",
  'Bosnia-Herzegovina': "ba",
  'Botswana': "bw",
  'Brazil': "br",
  'Brunei': "bn",
  'Bulgaria': "bg",
  'Burundi': "bi",
  'California': "us-ca",
  'Cambodia': "kh",
  'Cameroon': "cm",
  'Canada': "ca",
  'Cape Verde': "cv",
  'Caribbean Netherlands': "bq",
  'Cayman Islands': "ky",
  'Central African Republic': "cf",
  'Cape Verde Islands': 'cv',
  'Chad': "td",
  'Chile': "cl",
  'China PR': "cn",
  'Christmas Island': "cx",
  'Cocos Islands': "cc",
  'Colombia': "co",
  'Colorado': "us-co",
  'Comoros': "km",
  'Côte d\'Ivoire': 'ci',
  'Connecticut': "us-ct",
  'Congo DR': 'cd',
  'Congo': 'cd',
  'Cook Islands': "ck",
  'Costa Rica': "cr",
  'Croatia': "hr",
  'Cuba': "cu",
  'Curaçao': "cw",
  'Cyprus': "cy",
  'Czech Republic': "cz",
  'Delaware': "us-de",
  'Denmark': "dk",
  'Djibouti': "dj",
  'Dominica': "dm",
  'Dominican Republic': "do",
  'DR Congo': "cd",
  'Ecuador': "ec",
  'Egypt': "eg",
  'El Salvador': "sv",
  'England': "gb-eng",
  'Equatorial Guinea': "gq",
  'Eritrea': "er",
  'Estonia': "ee",
  'Eswatini': "sz",
  'Ethiopia': "et",
  'Faroe Islands': "fo",
  'Fiji': "fj",
  'Finland': "fi",
  'Florida': "us-fl",
  'France': "fr",
  'French Guiana': 'gf',
  'Gabon': "ga",
  'Gambia': "gm",
  'Georgia': "ge",
  'Germany': "de",
  'Ghana': "gh",
  'Guadeloupe': 'gp',
  'Gibraltar': "gi",
  'Greece': "gr",
  'Guinea': "gn",
  'Guinea-Bissau': "gw",
  'Honduras': "hn",
  'Hong Kong': "hk",
  'Hungary': "hu",
  'Iceland': "is",
  'India': "in",
  'Indonesia': "id",
  'Iowa': "us-ia",
  'Iran': "ir",
  'Iraq': "iq",
  'Ireland': "ie",
  'Ireland Republic': 'ie',
  'Isle of Man': "im",
  'Israel': "il",
  'Italy': "it",
  'Jamaica': "jm",
  'Japan': "jp",
  'Jersey': "je",
  'Jordan': "jo",
  'Kansas': "us-ks",
  'Kazakhstan': "kz",
  'Kentucky': "us-ky",
  'Kenya': "ke",
  'Kiribati': "ki",
  'Kosovo': "xk",
  'Kuwait': "kw",
  'Kyrgyzstan': "kg",
  'Laos': "la",
  'Latvia': "lv",
  'Lebanon': "lb",
  'Lesotho': "ls",
  'Liberia': "lr",
  'Libya': "ly",
  'Liechtenstein': "li",
  'Lithuania': "lt",
  'Louisiana': "us-la",
  'Luxembourg': "lu",
  'Macau': "mo",
  'Madagascar': "mg",
  'Maine': "us-me",
  'Malawi': "mw",
  'Malaysia': "my",
  'Maldives': "mv",
  'Mali': "ml",
  'Malta': "mt",
  'Marshall Islands': "mh",
  'Martinique': "mq",
  'Maryland': "us-md",
  'Massachusetts': "us-ma",
  'Mauritania': "mr",
  'Mauritius': "mu",
  'Mayotte': "yt",
  'Mexico': "mx",
  'Michigan': "us-mi",
  'Micronesia': "fm",
  'Minnesota': "us-mn",
  'Mississippi': "us-ms",
  'Missouri': "us-mo",
  'Moldova': "md",
  'Monaco': "mc",
  'Mongolia': "mn",
  'Montana': "us-mt",
  'Montenegro': "me",
  'Montserrat': "ms",
  'Morocco': "ma",
  'Mozambique': "mz",
  'Myanmar': "mm",
  'Namibia': "na",
  'Nauru': "nr",
  'Nebraska': "us-ne",
  'Nepal': "np",
  'Netherlands': "nl",
  'Nevada': "us-nv",
  'New Caledonia': "nc",
  'New Hampshire': "us-nh",
  'New Jersey': "us-nj",
  'New Mexico': "us-nm",
  'New York': "us-ny",
  'New Zealand': "nz",
  'Nicaragua': "ni",
  'Niger': "ne",
  'Nigeria': "ng",
  'Niue': "nu",
  'Norfolk Island': "nf",
  'North Carolina': "us-nc",
  'North Dakota': "us-nd",
  'North Korea': "kp",
  'North Macedonia': "mk",
  'Macedonia FYR': "mk",
  'Northern Ireland': "gb-nir",
  'Norway': "no",
  'Ohio': "us-oh",
  'Oklahoma': "us-ok",
  'Oman': "om",
  'Oregon': "us-or",
  'Pakistan': "pk",
  'Palau': "pw",
  'Palestine': "ps",
  'Panama': "pa",
  'Papua New Guinea': "pg",
  'Paraguay': "py",
  'Pennsylvania': "us-pa",
  'Peru': "pe",
  'Philippines': "ph",
  'Pitcairn Islands': "pn",
  'Poland': "pl",
  'Portugal': "pt",
  'Puerto Rico': "pr",
  'Qatar': "qa",
  'Republic of the Congo': "cg",
  'Romania': "ro",
  'Russia': "ru",
  'Samoa': "ws",
  'San Marino': "sm",
  'Saudi Arabia': "sa",
  'Scotland': "gb-sct",
  'Senegal': "sn",
  'Serbia': "rs",
  'Seychelles': "sc",
  'Sierra Leone': "sl",
  'Singapore': "sg",
  'Sint Maarten': "sx",
  'Slovakia': "sk",
  'Slovenia': "si",
  'Solomon Islands': "sb",
  'Somalia': "so",
  'South Africa': "za",
  'South Carolina': "us-sc",
  'South Dakota': "us-sd",
  'South Georgia': "gs",
  'South Korea': "kr",
  'Korea Republic': 'kr',
  'South Sudan': "ss",
  'Spain': "es",
  'Sri Lanka': "lk",
  'Sudan': "sd",
  'Suriname': "sr",
  'Svalbard and Jan Mayen': "sj",
  'Sweden': "se",
  'Switzerland': "ch",
  'Syria': "sy",
  'São Tomé and Príncipe': "st",
  'Taiwan': "tw",
  'Tajikistan': "tj",
  'Tanzania': "tz",
  'Tennessee': "us-tn",
  'Texas': "us-tx",
  'Thailand': "th",
  'Timor-Leste': "tl",
  'Togo': "tg",
  'Tokelau': "tk",
  'Tonga': "to",
  'Trinidad and Tobago': "tt",
  'Tunisia': "tn",
  'Turkey': "tr",
  'Turkmenistan': "tm",
  'Turks and Caicos Islands': "tc",
  'Tuvalu': "tv",
  'Uganda': "ug",
  'Ukraine': "ua",
  'United Arab Emirates': "ae",
  'United Kingdom': "gb",
  'United Nations': "un",
  'United States': "us",
  'United States Minor Outlying Islands': "um",
  'United States Virgin Islands': "vi",
  'Uruguay': "uy",
  'Utah': "us-ut",
  'Uzbekistan': "uz",
  'Vanuatu': "vu",
  'Vatican City': "va",
  'Venezuela': "ve",
  'Vermont': "us-vt",
  'Vietnam': "vn",
  'Virginia': "us-va",
  'Wales': "gb-wls",
  'Wallis and Futuna': "wf",
  'Washington': "us-wa",
  'West Virginia': "us-wv",
  'Western Sahara': "eh",
  'Wisconsin': "us-wi",
  'Wyoming': "us-wy",
  'Yemen': "ye",
  'Zambia': 'zm',
  'Zimbabwe': 'zw',
  'Korea Republic': 'kr',
  'Macedonia FYR': "mk",
  'Ireland Republic': 'ie',
  'Guadeloupe': 'gp',
  'French Guiana': 'gf',
  'Côte d\'Ivoire': 'ci',
  'Cape Verde Islands': 'cv',
  'Burkina Faso': 'bf',
  'None': 'none',
}

pdf_logos = {
    '0844656c-924e-4506-b26f-f9561184ac56' : 'https://media.licdn.com/dms/image/C560BAQGOp6oAYPRaJA/company-logo_200_200/0/1647003575903/sports_entertainment_group_international_logo?e=1729123200&amp;v=beta&amp;t=vEiheoqtAqULLTZ6KGejd2tNDYXla0_uEXID1IURhWg',
    '4c109c61-c185-4135-a9d7-d8ff050513a4' : 'https://26951654.fs1.hubspotusercontent-eu1.net/hubfs/26951654/<EMAIL>'
}

basic_new = (
        "PlayerRecord",
        "TeamRequest",
        "Contact",
        "CommunityProposal",
        "Report",
        "ActivityTracker",
        "Porposals",
        "Notifications",
        "CommentCreate",
        "Activity",
        "ActivityCreate",
        "ActivityUpdate",
        "Comment",
        "Comments",
        "StaffRecord",
        "StaffRecordCreate",
        "StaffRecordUpdate",
    )
pro_new = (
        "PlayerRecord",
        "TeamRequest",
        "Contact",
        "CommunityProposal",
        "Report",
        "ActivityTracker",
        "Porposals",
        "Contract",
        "Notifications",
        "Commercial",
        "Mandate",
        "RepresentationAgreement",
        "ClubContract",
        "CommissionAgreement",
        "ContractUpload",
        "CommentCreate",
        "Activity",
        "ActivityCreate",
        "ActivityUpdate",
        "Comment",
        "Comments",
        "StaffRecord",
        "StaffRecordCreate",
        "StaffRecordUpdate",
    )

community_tokens = {
    'propose': 5,
    'upload': 2,
    'request_whatsapp': 2,
    'player_whatsapp': 2,
}

control_stages = {"watchlist","target","in_talks",
                  "signed","on_hold","mandate","mandate_on_demand"}

status_requests = {"open", "maybe"}

positionMappingWS = {
    "gk": "gk",
    "amf": "cam",
    "cb": "cb",
    "cf": "st",
    "dmf": "dmc",
    "lamf": "lw",
    "lb": "lb",
    "lb5": "lb",
    "lcb": "lcb",
    "lcb3": "lcb",
    "lcmf": "cmf",
    "lcmf3": "cmf",
    "ldmf": "dmc",
    "lw": "lw",
    "lwb": "lb",
    "lwf": "lw",
    "ramf": "rw",
    "rb": "rb",
    "rb5": "rb",
    "rcb": "rcb",
    "rcb3": "rcb",
    "rcmf": "cmf",
    "rcmf3": "cmf",
    "rdmf": "dmc",
    "rw": "rw",
    "rwb": "rb",
    "rwf": "rw",
    "ss": "ss",
}
