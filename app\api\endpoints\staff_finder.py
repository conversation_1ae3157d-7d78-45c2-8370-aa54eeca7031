from typing import Any
from app import models
from fastapi import APIRouter, Depends
from app.api import deps
import sqlalchemy
import pandas as pd
from app.db.session import engine
from datetime import datetime

router = APIRouter()


@router.get("/{tm_player_id}")
def get_clubs(
    *,
    tm_player_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    query = sqlalchemy.text(f"""with parent_teams_children as (
    select distinct on (st2.team_id) st1.team_name parent_team_name, st1.league_tier parent_league_tier, st2.*, st1.team_id as parent_team_id, levenshtein(st2.team_name, st1.team_name) str_dist
    from transfermarkt.tm_teams st1 full join transfermarkt.tm_teams st2
    on st1.league_country = st2.league_country and st1.league_name != st2.league_name
    where st2.team_name like '%%' || st1.team_name || '%%'
    and st1.league_tier <= st2.league_tier
    order by st2.team_id, st1.league_tier
    )
    , parent_teams_main as (
    select distinct on (st2.team_id) st1.team_name parent_team_name, st1.league_tier parent_league_tier, st2.*, st1.team_id as parent_team_id, levenshtein(st2.team_name, st1.team_name) str_dist
    from transfermarkt.tm_teams st1 full join transfermarkt.tm_teams st2
    on st1.league_country = st2.league_country and st1.league_name = st2.league_name
    where st2.team_name like '%%' || st1.team_name || '%%'
    and st1.league_tier = st2.league_tier
    order by st2.team_id, st1.league_tier
    )
    , parent_teams_all as (
    select * from parent_teams_children
    union
    select * from parent_teams_main
    )
    , parent_teams as (
    select distinct on (team_id) *
    from parent_teams_all
    order by team_id, parent_league_tier
    )
    , staff_teams as
    (select tt.team_name, tt.parent_team_name, tt.league_name, tt.league_country, tt.league_tier, tt.parent_team_id, sd.*
    from transfermarkt.staff_data sd, parent_teams tt
    where current_team_id = tt.team_id
    )
    , transfers_expanded AS (
    SELECT
        tt1.*,
        LAG(left_id)  OVER (PARTITION BY tm_player_id ORDER BY date) AS prev_left_id,
        LAG(date)     OVER (PARTITION BY tm_player_id ORDER BY date) AS joined_date,
        LAG(joined_id)OVER (PARTITION BY tm_player_id ORDER BY date) AS prev_joined_id
    FROM transfermarkt.tm_transfers tt1
    WHERE tt1.tm_player_id = :tm_player_id
    ORDER BY tt1.date
    )
    , all_common_tenure as
    (select *, te."joined_date" as date_of_transfer
    from staff_teams sd, transfers_expanded te
    where (sd.current_team_id = te.left_id or sd.parent_team_id = te.left_id)
    and ((te.joined_date::text between appointed and in_charge_until) or (appointed between te.joined_date::text and te.date::text) or (in_charge_until between te.joined_date::text and te.date::text))
    union
    select *, te."date" as date_of_transfer
    from staff_teams sd, (select distinct on (tm_player_id) * from transfers_expanded order by tm_player_id, date desc) te
    where (sd.current_team_id = te.joined_id or sd.parent_team_id = te.joined_id)
    and te.date::text < in_charge_until
    )
    select distinct on (ac.staff_id, tt.team_id) ac.team_name as prev_team, ac.current_team_id
    ,ac.league_country as prev_team_country, ac.role as prev_role, ac.appointed, ac.in_charge_until, ac.name, ac.date_of_transfer,
    concat('https://www.transfermarkt.com/staff/profil/trainer/', ac.staff_id) as staff_url, tt.team_name as curr_team,tt.league_country as curr_team_country ,sd.role as new_role
    from all_common_tenure ac
    join transfermarkt.staff_data sd on ac.staff_id = sd.staff_id and sd.in_charge_until is null
    join transfermarkt.tm_teams tt on tt.team_id = sd.current_team_id
    join transfermarkt.transfermarkt_data td on td.tm_player_id = ac.tm_player_id
    and td.tm_player_id = :tm_player_id
    where tt.team_id != td.current_club_id
    order by ac.staff_id, tt.team_id, date_of_transfer desc
    """)

    try:
        staffs = pd.read_sql(query, engine, params={"tm_player_id": tm_player_id})
        with engine.connect() as conn:
            conn.execute(sqlalchemy.text("INSERT INTO public.staff_finder_logs values (:email, :org_id, :tm_id, :timestamp)")
                       , {"email": current_user.email, "org_id": current_user.organization_id, "tm_id": tm_player_id, "timestamp": datetime.now()})
        return staffs.to_dict(orient='records')
    except Exception as e:
        print(f"Error executing query: {e}")