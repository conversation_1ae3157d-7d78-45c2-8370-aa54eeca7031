"""Date deal created

Revision ID: 92a0e392431d
Revises: 75aeace4f477
Create Date: 2024-11-29 14:53:21.211477

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '92a0e392431d'
down_revision = '75aeace4f477'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('activity', sa.Column('date_deal_created', sa.DateTime(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('activity', 'date_deal_created', schema='crm_test')
    # ### end Alembic commands ###