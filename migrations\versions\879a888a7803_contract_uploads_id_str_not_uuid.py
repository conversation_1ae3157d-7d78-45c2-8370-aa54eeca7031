"""contract uploads id str not uuid

Revision ID: 879a888a7803
Revises: 9c738ef0fe97
Create Date: 2022-09-27 14:58:44.275413

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '879a888a7803'
down_revision = '9c738ef0fe97'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('contract_uploads', 'id',
               existing_type=postgresql.UUID(),
               type_=sa.String(),
               existing_nullable=False,
               schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('contract_uploads', 'id',
               existing_type=sa.String(),
               type_=postgresql.UUID(),
               existing_nullable=False,
               schema='crm_dev')
    # ### end Alembic commands ###