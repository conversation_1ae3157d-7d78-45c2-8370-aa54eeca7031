import uuid
from typing import Optional

from fastapi import Depends, Request
from fastapi_users import BaseUserManager, UUIDIDMixin

from app.utils.base import add_player_request_new_org
from .fastapi_users import FastAPIUsersUpdated
from fastapi_users.authentication import (
    BearerTransport,
)
from .backend_with_remember import AuthenticationBackendRemember
from .jwt_updated import JWTUpStrategy
from fastapi_users_db_sqlalchemy import SQLAlchemyUserDatabase
from app.utils.mail import send_reset_password_link, send_verification_link
from app.api.endpoints.users.db import User, get_user_db
from app.config import settings
from app import crud
from app.db.session import session_maker
from firebase_admin import auth
from fastapi import HTTPException
from fastapi_users.jwt import generate_jwt
from datetime import datetime, timedelta
from app.utils.mail import send_welcome_email
from app.utils import get_url_frontend

async def get_user_manager(user_db: SQLAlchemyUserDatabase = Depends(get_user_db)):
    yield UserManager(user_db)


def get_jwt_strategy() -> JWTUpStrategy:
    return JWTUpStrategy(secret=settings.JWT_SECRET_KEY, lifetime_seconds=86400, remember_me_lifetime_seconds=86400*30)


auth_backend = AuthenticationBackendRemember(
    name="jwt",
    transport=BearerTransport(tokenUrl="users/auth/jwt/login"),
    get_strategy=get_jwt_strategy,
)

fastapi_users = FastAPIUsersUpdated[User, uuid.UUID](get_user_manager, [auth_backend])
current_active_user = fastapi_users.current_user(active=True)

class UserManager(UUIDIDMixin, BaseUserManager[User, uuid.UUID]):
    reset_password_token_secret = settings.JWT_SECRET_KEY
    verification_token_secret = settings.JWT_SECRET_KEY

    async def on_after_register(self, user: User, user_db: SQLAlchemyUserDatabase = Depends(get_user_db), request: Optional[Request] = None):
        print(f"User {user.id} has registered.")
        # Add user to contacts with the same id
        org = crud.organization.get(db = session_maker(), id=user.organization_id)       

        contact = crud.contact.create_with_user(
        db=session_maker(),
        obj_in={
            'id': user.id,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'contact_type': 'internal',
            'email': user.email,
            # 'organization_id': user.organization_id,
            'contact_organization': org.name
        },
        user=user
        )

    async def on_after_forgot_password(
        self, user: User, token: str, request: Optional[Request] = None
    ):
        await send_reset_password_link(user, token)
        print(f"User {user.id} has forgot their password. Reset token: {token}")

    async def on_after_request_verify(
        self, user: User, token: str, request: Optional[Request] = None
    ):
        print(f"Verification requested for user {user.id}. Verification token: {token}")
        await send_verification_link(user, token)

    async def on_after_verify(self, user: User, request: Optional[Request]) -> None:
        user = auth.get_user_by_email(user.email)
        current_custom_claims = user.custom_claims
        current_custom_claims['is_verified'] = True
        auth.set_custom_user_claims(user.uid, current_custom_claims)
        user.id = user._data['localId']
        user.organization_id = user.custom_claims.get('organization_id')
        await add_player_request_new_org(user)
    
    async def generate_reset_token(self, user: User) -> str:
        """Manually generate a password reset token (without sending an email)."""
        token_data = {
            "sub": str(user.id),
            "password_fgpt": self.password_helper.hash(user.hashed_password),
            "aud": self.reset_password_token_audience,
            "exp": datetime.utcnow() + timedelta(hours=24),
        }

        token = generate_jwt(
            token_data,
            self.reset_password_token_secret,
            self.reset_password_token_lifetime_seconds,
        )
        return token
    
    async def send_reset_link_to_email(self, email: str):
        """Finds the user by email, generates a reset token, and sends it via email."""
        user = await self.user_db.get_by_email(email)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Generate the reset token
        token = await self.generate_reset_token(user)

        # Create the reset link
        url = get_url_frontend()
        reset_link = f"{url}/reset_password/{token}"

        try:
            await send_welcome_email(reset_link, email)
        except Exception as e:
            print(e)
            raise HTTPException(status_code=500, detail="Failed to send email")
        # Send the email
        return {"message": "Reset link sent successfully"}

