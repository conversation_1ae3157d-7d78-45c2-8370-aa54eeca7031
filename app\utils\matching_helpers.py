from dataclasses import dataclass
from datetime import datetime

@dataclass
class SegmentRange:
    lower_bound: int
    upper_bound: int

def player_to_team_segment_map(player_segment: int) -> SegmentRange:
    if player_segment == -1:
        # players not evaluated yet should not match to any requests:
        return SegmentRange(upper_bound=-1, lower_bound=-1)
    if 0 <= player_segment <= 6:
        return SegmentRange(upper_bound=4, lower_bound=4)
    elif player_segment == 7:
        return SegmentRange(upper_bound=3, lower_bound=3)
    elif 8 <= player_segment <= 9:
        return SegmentRange(upper_bound=2, lower_bound=3)
    elif 10 <= player_segment <= 11:
        return SegmentRange(upper_bound=1, lower_bound=2)
    # no need to elif since the enum object already takes care of validation:
    elif player_segment == 12:
        return SegmentRange(upper_bound=1, lower_bound=1)
    raise ValueError(
        "Invalid team segment range, values should be no higher than 12"
    )

def get_window_from_date(td: datetime = datetime.now()) -> str:
    year, month = td.year, td.month
    if month >= 3 and month <= 8:
        return f'summer_{year}'
    elif month == 1 or month == 2:
        return f'winter_{year}'
    elif month == 12  or month >= 9 and month <= 12:
        return f'winter_{year+1}'
    return f'summer_{year + 1}'

def team_to_player_segment_map(team_segment: int) -> SegmentRange:
    if team_segment == 1:
        return SegmentRange(upper_bound=12, lower_bound=10)
    elif team_segment == 2:
        return SegmentRange(upper_bound=11, lower_bound=8)
    elif team_segment == 3:
        return SegmentRange(upper_bound=9, lower_bound=7)
    elif team_segment == 4:
        return SegmentRange(upper_bound=6, lower_bound=1)
    raise ValueError(
        "Invalid team segment range, values should be between 1 and 4"
    )