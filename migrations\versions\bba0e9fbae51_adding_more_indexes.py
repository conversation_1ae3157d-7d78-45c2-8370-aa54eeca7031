"""adding more indexes

Revision ID: bba0e9fbae51
Revises: 103090fbb859
Create Date: 2022-08-10 13:05:41.822360

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'bba0e9fbae51'
down_revision = '103090fbb859'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_crm_dev_contacts_created_by'), 'contacts', ['created_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_player_record_changes_edit_by'), 'player_record_changes', ['edit_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_player_records_created_by'), 'player_records', ['created_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_proposals_created_by'), 'proposals', ['created_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_proposals_organization_id'), 'proposals', ['organization_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_purchases_module_id'), 'purchases', ['module_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_purchases_organization_id'), 'purchases', ['organization_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_reports_created_by'), 'reports', ['created_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_reports_organization_id'), 'reports', ['organization_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_team_request_changes_edit_by'), 'team_request_changes', ['edit_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_team_requests_created_by'), 'team_requests', ['created_by'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_dev_team_requests_created_by'), table_name='team_requests', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_team_request_changes_edit_by'), table_name='team_request_changes', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_reports_organization_id'), table_name='reports', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_reports_created_by'), table_name='reports', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_purchases_organization_id'), table_name='purchases', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_purchases_module_id'), table_name='purchases', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_proposals_organization_id'), table_name='proposals', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_proposals_created_by'), table_name='proposals', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_player_records_created_by'), table_name='player_records', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_player_record_changes_edit_by'), table_name='player_record_changes', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contacts_created_by'), table_name='contacts', schema='crm_dev')
    # ### end Alembic commands ###