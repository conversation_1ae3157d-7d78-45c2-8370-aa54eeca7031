"""Changelog added to activity

Revision ID: 40836527a65e
Revises: 29d5d5ea9521
Create Date: 2024-06-27 11:51:05.951253

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '40836527a65e'
down_revision = '29d5d5ea9521'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('activity_change',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('edit_at', sa.DateTime(), nullable=True),
    sa.Column('field', sa.String(), nullable=True),
    sa.Column('previous', sa.String(), nullable=True),
    sa.Column('updated', sa.String(), nullable=True),
    sa.Column('activity_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('edit_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.ForeignKeyConstraint(['activity_id'], ['crm_test.activity.id'], ),
    sa.ForeignKeyConstraint(['edit_by'], ['crm_test.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_activity_change_activity_id'), 'activity_change', ['activity_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_activity_change_edit_at'), 'activity_change', ['edit_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_activity_change_edit_by'), 'activity_change', ['edit_by'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_test_activity_change_edit_by'), table_name='activity_change', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_activity_change_edit_at'), table_name='activity_change', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_activity_change_activity_id'), table_name='activity_change', schema='crm_test')
    op.drop_table('activity_change', schema='crm_test')
    # ### end Alembic commands ###