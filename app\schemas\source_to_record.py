import uuid
from typing import Optional
from pydantic import BaseModel
from app.schemas.contact import ContactShort

class SourceToRecordUpdate(BaseModel):
    source_id: Optional[uuid.UUID]
    player_id: Optional[uuid.UUID]
    team_request_id: Optional[uuid.UUID]


class SourceToRecordCreate(SourceToRecordUpdate):
    source_id: uuid.UUID
    player_id: Optional[uuid.UUID]
    team_request_id: Optional[uuid.UUID]


class SourceToRecord(BaseModel):
    source: ContactShort
    source_id: uuid.UUID

    class Config:
        orm_mode = True