from datetime import datetime
import uuid
from sqlalchemy import Column, ForeignKey, Float, DateTime, Boolean
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.config import settings
from sqlalchemy.dialects.postgresql import UUID


class Purchase(Base):
    __tablename__ = "purchases"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.organizations.id"),
        index=True,
    )
    module_id = Column(
        UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.modules.id"), index=True
    )

    created_at = Column(DateTime, index=True, default=datetime.now)
    price = Column(Float)
    active_until = Column(DateTime, index=True)
    active = Column(Boolean, default=True)
    module = relationship("Module", back_populates="organizations", lazy="joined")
    organization = relationship("Organization", back_populates="modules")
