"""Add name column to rank records table structure

Revision ID: 1eaa643ca97d
Revises: 83055aa28b70
Create Date: 2022-08-24 16:25:13.091065

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1eaa643ca97d'
down_revision = '83055aa28b70'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###