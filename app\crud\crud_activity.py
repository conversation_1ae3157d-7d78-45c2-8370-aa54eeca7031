from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.activity import ActivityCreate, ActivityUpdate
from sqlalchemy.orm import Session, lazyload, Load, selectinload
from typing import List, TypeVar, Union, Type, Any, Dict, Optional
from app.db.base_class import Base
from fastapi.encoders import jsonable_encoder
from app.models import User
from app.utils import compare_version_of_objects
from pydantic import BaseModel
from app.models import AssignedToRecord
import json
import uuid
from sqlalchemy import and_, exists, text, desc, asc
from datetime import datetime, date
from app.config import settings

ModelType = TypeVar("ModelType", bound=Base)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)
ChangeType = TypeVar("ChangeType", bound=Base)


class CRUDActivity(CRUDBase[models.Activity, ActivityCreate, ActivityUpdate]):

    def get_all_with_filters(
        self, db: Session, org_id: str, can_access_sensitive: bool, filters: dict
    ) -> List[ModelType]:
        # Base query with organization ID filter
        query = (
            db.query(self.model)
            .options(
                selectinload(self.model.assigned_to_record),
                selectinload(self.model.player_info),
                selectinload(self.model.staff_info),
                selectinload(self.model.team_info),
                selectinload(self.model.comments),
            )
            .filter(self.model.organization_id == org_id)
        )

        # Handle sensitivity filter
        if not can_access_sensitive:
            query = query.filter(self.model.is_sensitive != True)

        # Apply dynamic filters
        if "assigned_to_me" in filters and filters["assigned_to_me"] is not None:
            if filters["assigned_to_me"]:
                query = query.join(
                    AssignedToRecord, AssignedToRecord.activity_id == self.model.id
                ).filter(AssignedToRecord.contact_id == filters["current_user_id"])

        if "open_closed_all" in filters and filters["open_closed_all"]:
            if filters["open_closed_all"] == "open":
                query = query.filter(
                    self.model.stage.in_(
                        ["to_do", "in_progress", "interest", "negotiations"]
                    )
                )
            elif filters["open_closed_all"] == "closed":
                query = query.filter(self.model.stage.in_(["no_interest", "done"]))

        # Order by last updated
        query = query.order_by(self.model.last_updated.desc())

        # Return filtered results
        return query.all()

    def get_activities_paginated_with_filters(
        self,
        db: Session,
        org_id: str,
        can_access_sensitive: bool,
        page: int = 0,
        limit: int = 10,
        # Search parameter
        search: Optional[str] = None,
        # Filter parameters
        activity_type: Optional[str] = None,
        stage: Optional[List[str]] = None,
        assigned_to_me: Optional[bool] = None,
        assigned_to_contact_id: Optional[List[str]] = None,
        created_by_me: Optional[bool] = None,
        created_by_contact_id: Optional[List[str]] = None,
        player_id: Optional[List[int]] = None,
        team_id: Optional[List[int]] = None,
        team_country: Optional[List[str]] = None,
        team_league: Optional[List[str]] = None,
        last_updated_start: Optional[datetime] = None,
        last_updated_end: Optional[datetime] = None,
        next_action_start: Optional[date] = None,
        next_action_end: Optional[date] = None,
        due_date_start: Optional[date] = None,
        due_date_end: Optional[date] = None,
        # Sorting parameters
        sort_by: Optional[str] = None,
        sort_order: Optional[str] = "desc",
        current_user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Get paginated activities with comprehensive filtering and sorting.
        Similar to team_page.py pattern.
        """
        skip = page * limit

        # Build base WHERE clause
        where_clauses = [f"a.organization_id = :org_id"]
        params = {"limit": limit, "skip": skip, "org_id": org_id}

        # Handle sensitivity filter
        if not can_access_sensitive:
            where_clauses.append("(a.is_sensitive IS NULL OR a.is_sensitive != true)")

        # Search filter (text search across multiple fields)
        if search:
            params["search"] = search
            tsq = "plainto_tsquery('simple_unaccent', :search)"

            where_clauses.append(
                text(
                    f"""
            (
            to_tsvector('simple_unaccent', a.title) @@ {tsq}
            OR to_tsvector('simple_unaccent', pi."firstName" || ' ' || pi."lastName") @@ {tsq}
            OR to_tsvector('simple_unaccent', ti.name) @@ {tsq}
            OR to_tsvector('simple_unaccent', ti.area_name) @@ {tsq}
            OR to_tsvector('simple_unaccent', ti.league_name) @@ {tsq}
            OR to_tsvector('simple_unaccent', u.first_name || ' ' || u.last_name) @@ {tsq}
            OR EXISTS (
                SELECT 1
                FROM {settings.PG_SCHEMA}.assigned_to_record atr
                JOIN {settings.PG_SCHEMA}.contacts c
                ON c.id = atr.contact_id
                WHERE atr.activity_id = a.id
                AND to_tsvector(
                        'simple_unaccent',
                        c.first_name || ' ' ||
                        c.last_name  || ' ' ||
                        c.email
                    ) @@ {tsq}
            )
            )
            """
                )
            )

        # Type filter (single value)
        if activity_type:
            where_clauses.append("a.type = :activity_type")
            params["activity_type"] = activity_type

        # Stage filter (array)
        if stage and len(stage) > 0:
            placeholders = []
            for i, stage_val in enumerate(stage):
                param_name = f"stage_{i}"
                placeholders.append(f":{ param_name}")
                params[param_name] = stage_val
            where_clauses.append(f"a.stage IN ({', '.join(placeholders)})")

        # Assigned to filters
        if assigned_to_me and current_user_id:
            where_clauses.append(
                """
                EXISTS (
                    SELECT 1 FROM {schema}.assigned_to_record atr
                    WHERE atr.activity_id = a.id AND atr.contact_id = :current_user_id
                )
            """.format(
                    schema=settings.PG_SCHEMA
                )
            )
            params["current_user_id"] = current_user_id
        elif assigned_to_contact_id and len(assigned_to_contact_id) > 0:
            contact_ids_clause = """
                EXISTS (
                    SELECT 1 FROM {schema}.assigned_to_record atr
                    WHERE atr.activity_id = a.id AND atr.contact_id IN ({placeholders})
                )
            """.format(
                schema=settings.PG_SCHEMA,
                placeholders=", ".join(
                    [
                        f":assigned_contact_{i}"
                        for i in range(len(assigned_to_contact_id))
                    ]
                ),
            )
            where_clauses.append(contact_ids_clause)
            for i, contact_id in enumerate(assigned_to_contact_id):
                params[f"assigned_contact_{i}"] = contact_id

        # Created by filters
        if created_by_me and current_user_id:
            where_clauses.append("a.created_by = :created_by_user_id")
            params["created_by_user_id"] = current_user_id
        elif created_by_contact_id and len(created_by_contact_id) > 0:
            placeholders = []
            for i, creator_id in enumerate(created_by_contact_id):
                param_name = f"creator_{i}"
                placeholders.append(f":{ param_name}")
                params[param_name] = creator_id
            where_clauses.append(f"a.created_by IN ({', '.join(placeholders)})")

        # Player filter (array)
        if player_id and len(player_id) > 0:
            placeholders = []
            for i, pid in enumerate(player_id):
                param_name = f"player_id_{i}"
                placeholders.append(f":{ param_name}")
                params[param_name] = pid
            where_clauses.append(f'a."playerId" IN ({", ".join(placeholders)})')

        # Team filter (array)
        if team_id and len(team_id) > 0:
            placeholders = []
            for i, tid in enumerate(team_id):
                param_name = f"team_id_{i}"
                placeholders.append(f":{ param_name}")
                params[param_name] = tid
            where_clauses.append(f'a."teamId" IN ({", ".join(placeholders)})')

        # Team country filter (array)
        if team_country and len(team_country) > 0:
            placeholders = []
            for i, country in enumerate(team_country):
                param_name = f"team_country_{i}"
                placeholders.append(f":{ param_name}")
                params[param_name] = country
            where_clauses.append(f"ti.area_name IN ({', '.join(placeholders)})")

        # Team league filter (array)
        if team_league and len(team_league) > 0:
            placeholders = []
            for i, league in enumerate(team_league):
                param_name = f"team_league_{i}"
                placeholders.append(f":{ param_name}")
                params[param_name] = league
            where_clauses.append(f"ti.league_name IN ({', '.join(placeholders)})")

        # Date range filters
        if last_updated_start:
            where_clauses.append("a.last_updated >= :last_updated_start")
            params["last_updated_start"] = last_updated_start
        if last_updated_end:
            where_clauses.append("a.last_updated <= :last_updated_end")
            params["last_updated_end"] = last_updated_end

        # Handle next_action and due_date filters with OR logic when both are provided
        next_action_conditions = []
        due_date_conditions = []

        if next_action_start:
            next_action_conditions.append("a.next_action >= :next_action_start")
            params["next_action_start"] = next_action_start
        if next_action_end:
            next_action_conditions.append("a.next_action <= :next_action_end")
            params["next_action_end"] = next_action_end

        if due_date_start:
            due_date_conditions.append("a.due_date >= :due_date_start")
            params["due_date_start"] = due_date_start
        if due_date_end:
            due_date_conditions.append("a.due_date <= :due_date_end")
            params["due_date_end"] = due_date_end

        # Apply OR logic if both next_action and due_date filters are provided
        if next_action_conditions and due_date_conditions:
            next_action_clause = " AND ".join(next_action_conditions)
            due_date_clause = " AND ".join(due_date_conditions)
            where_clauses.append(f"(({next_action_clause}) OR ({due_date_clause}))")
        elif next_action_conditions:
            where_clauses.extend(next_action_conditions)
        elif due_date_conditions:
            where_clauses.extend(due_date_conditions)

        where_clause = "WHERE " + " AND ".join(str(clause) for clause in where_clauses)

        # Build ORDER BY clause
        order_clause = "ORDER BY a.last_updated DESC"  # Default sorting
        if sort_by:
            # Map sort fields to actual column names
            sort_field_map = {
                "title": "a.title",
                "type": "a.type",
                "stage": "a.stage",
                "last_updated": "a.last_updated",
                "created_at": "a.created_at",
                "next_action": "a.next_action",
                "due_date": "a.due_date",
                "player_name": 'CONCAT(pi."firstName", \' \', pi."lastName")',
                "team_name": "ti.name",
                "team_country": "ti.area_name",
                "team_league": "ti.league_name",
            }

            if sort_by in sort_field_map:
                sort_direction = "DESC" if sort_order.lower() == "desc" else "ASC"
                order_clause = f"ORDER BY {sort_field_map[sort_by]} {sort_direction}"

        # Count query
        count_query = text(
            f"""
            SELECT COUNT(*)
            FROM {settings.PG_SCHEMA}.activity a
            LEFT JOIN wyscout.player_info2 pi ON pi."playerId" = a."playerId"
            LEFT JOIN wyscout.team_info2 ti ON ti."teamId" = a."teamId"
            LEFT JOIN transfermarkt.staff_info si ON si.staff_id = a.staff_id
            LEFT JOIN {settings.PG_SCHEMA}."user" u ON u.id = a.created_by
            {where_clause}
        """
        )
        total_count = db.execute(count_query, params).scalar()

        # Main query with pagination
        query = text(
            f"""
            SELECT
                a.id,
                a.title,
                a.type,
                a.stage,
                a.description,
                a.created_at,
                a.last_updated,
                a.next_action,
                a.due_date,
                a.date_deal_created,
                a.is_sensitive,
                a.notes,
                a.created_by,
                a.updated_by_id,
                a."playerId",
                p.id as player_id,
                a."teamId",
                a.staff_id,
                a.organization_id,
                -- Creator info as a JSON object
                json_build_object(
                    'id', u.id,
                    'email', u.email
                ) as creator,
                -- Organization info as a JSON object
                json_build_object(
                    'id', o.id,
                    'name', o.name,
                    'password', ''
                ) as organization,
                -- Player info as a JSON object
                CASE WHEN a."playerId" IS NOT NULL THEN
                    json_build_object(
                        'playerId', pi."playerId",
                        'firstName', pi."firstName",
                        'lastName', pi."lastName",
                        'shortName', pi."shortName",
                        'birthDate', pi."birthDate",
                        'team_name', pi.team_name,
                        'currentTeamId', pi."currentTeamId",
                        'passport', pi.passport,
                        'tm_link', pi.tm_link
                    )
                ELSE NULL END as player_info,
                -- Team info as a JSON object
                CASE WHEN a."teamId" IS NOT NULL THEN
                    json_build_object(
                        'teamId', ti."teamId",
                        'name', ti.name,
                        'area_name', ti.area_name,
                        'divisionLevel', ti."divisionLevel",
                        'league_name', ti.league_name,
                        'segment', ti.segment,
                        'imageDataURL', ti."imageDataURL"
                    )
                ELSE NULL END as team_info,
                -- Staff info as a JSON object
                CASE WHEN a.staff_id IS NOT NULL THEN
                    json_build_object(
                        'staff_id', si.staff_id,
                        'name', si.name,
                        'role', si.role,
                        'passport', si.passport,
                        'birth_date', si.birth_date,
                        'team_name', si.team_name,
                        'league_country', si.league_country,
                        'league_name', si.league_name
                    )
                ELSE NULL END as staff_info,
                -- Get assigned users as a JSON array
                (
                    SELECT COALESCE(json_agg(
                        json_build_object(
                            'contact_id', atr.contact_id,
                            'contact', json_build_object(
                                'id', c.id,
                                'email', c.email,
                                'first_name', c.first_name,
                                'last_name', c.last_name
                            )
                        )
                    ), '[]'::json)
                    FROM {settings.PG_SCHEMA}.assigned_to_record atr
                    LEFT JOIN {settings.PG_SCHEMA}.contacts c ON c.id = atr.contact_id
                    WHERE atr.activity_id = a.id
                ) as assigned_to_record,
                -- Get comments as a JSON array
                (
                    SELECT COALESCE(json_agg(
                        json_build_object(
                            'id', cm.id,
                            'time', cm.time,
                            'comment', cm.comment,
                            'creator', cm.creator,
                            'activity_id', cm.activity_id
                        ) ORDER BY cm.time DESC
                    ), '[]'::json)
                    FROM {settings.PG_SCHEMA}.comments_activity cm
                    WHERE cm.activity_id = a.id
                ) as comments
            FROM {settings.PG_SCHEMA}.activity a
            LEFT JOIN wyscout.player_info2 pi ON pi."playerId" = a."playerId"
            LEFT JOIN wyscout.team_info2 ti ON ti."teamId" = a."teamId"
            LEFT JOIN transfermarkt.staff_info si ON si.staff_id = a.staff_id
            LEFT JOIN {settings.PG_SCHEMA}.user u ON u.id = a.created_by
            LEFT JOIN {settings.PG_SCHEMA}.player_records p ON a."playerId" = p."playerId" and :org_id = p.organization_id
            LEFT JOIN {settings.PG_SCHEMA}.organizations o ON o.id = a.organization_id
            {where_clause}
            {order_clause}
            LIMIT :limit OFFSET :skip
        """
        )

        activities = db.execute(query, params).mappings().all()

        return {
            "total_count": total_count,
            "total_pages": (total_count + limit - 1) // limit,
            "limit": limit,
            "offset": page,
            "data": activities,
        }

    def get_all_activities_for_export(
        self, db: Session, org_id: str, can_access_sensitive: bool
    ) -> List[Dict[str, Any]]:
        """
        Get all activities for the organization for export purposes.
        Returns flattened data suitable for CSV/Excel export.
        Sorted by last_updated DESC.
        """
        # Build base WHERE clause
        where_clauses = [f"a.organization_id = :org_id"]
        params = {"org_id": org_id}

        # Handle sensitivity filter
        if not can_access_sensitive:
            where_clauses.append("(a.is_sensitive IS NULL OR a.is_sensitive != true)")

        where_clause = "WHERE " + " AND ".join(where_clauses)

        # Export query with all relevant fields flattened
        query = text(
            f"""
            SELECT
                a.title,
                a.type,
                a.stage,
                a.description,
                a.created_at,
                a.last_updated,
                a.next_action,
                a.due_date,
                a.date_deal_created,
                -- Player information
                CONCAT(pi."firstName", ' ', pi."lastName") as player_full_name,
                pi.team_name as player_current_team,
                pi.passport as player_passport,
                pi.tm_link as player_tm_link,
                -- Team information
                ti.name as team_name,
                ti.area_name as team_country,
                ti."divisionLevel" as team_division_level,
                ti.league_name as team_league,
                -- Staff information
                si.name as staff_name,
                si.role as staff_role,
                si.passport as staff_passport,
                si.birth_date as staff_birth_date,
                si.team_name as staff_team_name,
                si.league_country as staff_league_country,
                si.league_name as staff_league_name,
                -- Creator information
                u.email as created_by_email,
                CONCAT(u.first_name, ' ', u.last_name) as created_by_full_name,
                -- Updated by information
                uc.email as updated_by_email,
                CONCAT(uc.first_name, ' ', uc.last_name) as updated_by_full_name
            FROM {settings.PG_SCHEMA}.activity a
            LEFT JOIN wyscout.player_info2 pi ON pi."playerId" = a."playerId"
            LEFT JOIN wyscout.team_info2 ti ON ti."teamId" = a."teamId"
            LEFT JOIN transfermarkt.staff_info si ON si.staff_id = a.staff_id
            LEFT JOIN {settings.PG_SCHEMA}.user u ON u.id = a.created_by
            LEFT JOIN {settings.PG_SCHEMA}.contacts uc ON uc.id = a.updated_by_id
            {where_clause}
            ORDER BY a.last_updated DESC
        """
        )

        activities = db.execute(query, params).mappings().all()
        return [dict(activity) for activity in activities]

    def get_tasks_with_org(
        self, db: Session, org_id: str, can_access_sensitive: bool
    ) -> List[ModelType]:
        filter_cond = (
            self.model.is_sensitive != True if not can_access_sensitive else True
        )
        return (
            db.query(self.model)
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
            .filter(
                self.model.organization_id == org_id,
                filter_cond,
                self.model.type == "task",
            )
            .order_by(self.model.last_updated.desc())
            .all()
        )

    def get_deals_with_org(
        self, db: Session, org_id: str, can_access_sensitive: bool
    ) -> List[ModelType]:
        filter_cond = (
            self.model.is_sensitive != True if not can_access_sensitive else True
        )
        return (
            db.query(self.model)
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
            .filter(
                self.model.organization_id == org_id,
                filter_cond,
                self.model.type == "deal",
            )
            .order_by(self.model.last_updated.desc())
            .all()
        )

    def get_activities_for_wyscout_player(
        self, db: Session, org_id: str, can_access_sensitive: bool, playerId
    ) -> List[ModelType]:
        filter_cond = (
            self.model.is_sensitive != True if not can_access_sensitive else True
        )
        return (
            db.query(self.model)
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
            .filter(
                self.model.organization_id == org_id,
                filter_cond,
                self.model.playerId == playerId,
            )
            .order_by(self.model.last_updated.desc())
            .all()
        )

    def get_activities_for_wyscout_team(
        self, db: Session, org_id: str, can_access_sensitive: bool, teamId
    ) -> List[ModelType]:
        filter_cond = (
            self.model.is_sensitive != True if not can_access_sensitive else True
        )
        return (
            db.query(self.model)
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
            .filter(
                self.model.organization_id == org_id,
                filter_cond,
                self.model.teamId == teamId,
            )
            .order_by(self.model.last_updated.desc())
            .all()
        )

    def get_activities_for_wyscout_staff(
        self, db: Session, org_id: str, can_access_sensitive: bool, staff_id
    ) -> List[ModelType]:
        filter_cond = (
            self.model.is_sensitive != True if not can_access_sensitive else True
        )
        return (
            db.query(self.model)
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
            .filter(
                self.model.organization_id == org_id,
                filter_cond,
                self.model.staff_id == staff_id,
            )
            .order_by(self.model.last_updated.desc())
            .all()
        )

    def update_with_changelog(
        self,
        db: Session,
        *,
        record_id: str,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
        change_model: Type[ChangeType],
        record_type: str,
        user: User,
    ) -> ModelType:
        assigned_in_data = False
        # we pop out player_features and compute changes separately:
        obj_in_data = jsonable_encoder(obj_in)
        db_obj_data = jsonable_encoder(db_obj)
        if (
            obj_in_data
            and db_obj_data
            and str(obj_in_data["assigned_to_record"])
            == str(db_obj_data["assigned_to_record"])
        ):
            obj_in_data.pop("assigned_to_record")
        else:
            assigned_in_data = obj_in_data.pop("assigned_to_record")

        # print(db_obj.source_to_record[0].source_id)

        changes = compare_version_of_objects(
            db_obj,
            obj_in_data,
            user.id,
        )
        ll_assigned = []
        if assigned_in_data:
            ll_assigned = [
                AssignedToRecord(contact_id=con, id=uuid.uuid4())
                for con in assigned_in_data
            ]

        db_obj.assigned_to_record = ll_assigned
        for field, value in obj_in_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        for c in changes:
            c[f"{record_type}_id"] = record_id
            # print(c)
            if c["field"] == "notes":
                # print(c)
                try:
                    c["previous"] = json.dumps(c["previous"])
                except:
                    c["previous"] = ""

                try:
                    c["updated"] = json.dumps(c["updated"])
                except:
                    c["updated"] = ""

            db.add(change_model(**c))
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_notifications_paginated_with_filters(
        self,
        db: Session,
        org_id: str,
        can_access_sensitive: bool,
        page: int = 0,
        limit: int = 10,
        # Search parameter
        search: Optional[str] = None,
        # Filter parameters
        player_id: Optional[List[int]] = None,
        team_id: Optional[List[int]] = None,
        team_country: Optional[List[str]] = None,
        team_league: Optional[List[str]] = None,
        legal_type: Optional[List[str]] = None,
        assigned_to_me: Optional[bool] = None,
        assigned_to_contact_id: Optional[List[str]] = None,
        assigned_to_email: Optional[List[str]] = None,
        due_date_start: Optional[date] = None,
        due_date_end: Optional[date] = None,
        # Sorting parameters
        sort_by: Optional[str] = None,
        sort_order: Optional[str] = "desc",
        current_user_id: Optional[str] = None,
        current_user_email: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Get paginated birthday and legal notifications with comprehensive filtering.

        Birthday notifications: From NotificationSettings with player_notifications=true
        Legal notifications: From Contract

        Filters:
        - player_id: Filter by player IDs
        - team_id: Filter by team IDs (for legal notifications)
        - team_country: Filter by team countries
        - team_league: Filter by team leagues
        - search: Search across player name, team name, team country, team league
        - assigned_to_me: Filter notifications assigned to current user
        - assigned_to_contact_id: Filter notifications assigned to specific contacts
        - due_date_start/end: Filter by birthday or contract end_date
        """
        skip = page * limit

        # Build base WHERE clauses for birthday notifications
        birthday_where_clauses = []

        # Build base WHERE clauses for legal notifications
        legal_where_clauses = []

        # Single parameter dictionary for all queries
        params = {"limit": limit, "skip": skip, "org_id": org_id}

        # Organization filter for both
        birthday_where_clauses.append("pr.organization_id = :org_id")
        legal_where_clauses.append("c.organization_id = :org_id")

        # Player notifications must be enabled for birthday notifications
        birthday_where_clauses.append("ns.player_notifications = true")

        # Handle sensitivity filter for both
        if not can_access_sensitive:
            birthday_where_clauses.append(
                "(pr.is_sensitive IS NULL OR pr.is_sensitive != true)"
            )

        # Player ID filter
        if player_id and len(player_id) > 0:
            placeholders = []
            for i, pid in enumerate(player_id):
                param_name = f"player_{i}"
                placeholders.append(f":{param_name}")
                params[param_name] = pid
            birthday_where_clauses.append(
                f'pr."playerId" IN ({", ".join(placeholders)})'
            )
            legal_where_clauses.append(f'pr."playerId" IN ({", ".join(placeholders)})')

        # Team ID filter (only applies to legal notifications)
        if team_id and len(team_id) > 0:
            placeholders = []
            for i, tid in enumerate(team_id):
                param_name = f"team_{i}"
                placeholders.append(f":{param_name}")
                params[param_name] = tid
            legal_where_clauses.append(f'c."teamId" IN ({", ".join(placeholders)})')

        # Team country filter
        if team_country and len(team_country) > 0:
            placeholders = []
            for i, country in enumerate(team_country):
                param_name = f"country_{i}"
                placeholders.append(f":{param_name}")
                params[param_name] = country
            # For birthday notifications, we need to join with team_info2 to get area_name
            birthday_where_clauses.append(
                f'pti.area_name IN ({", ".join(placeholders)})'
            )
            legal_where_clauses.append(f'ti.area_name IN ({", ".join(placeholders)})')

        # Team league filter
        if team_league and len(team_league) > 0:
            placeholders = []
            for i, league in enumerate(team_league):
                param_name = f"league_{i}"
                placeholders.append(f":{param_name}")
                params[param_name] = league
            # For birthday notifications, we need to join with team_info2 to get league_name
            birthday_where_clauses.append(
                f'pti.league_name IN ({", ".join(placeholders)})'
            )
            legal_where_clauses.append(f'ti.league_name IN ({", ".join(placeholders)})')

        # Search filter
        if search:
            search_term = f"%{search}%"
            birthday_search_clause = """(
                CONCAT(pi."firstName", ' ', pi."lastName") ILIKE :search
                OR pi.team_name ILIKE :search
                OR pti.area_name ILIKE :search
                OR pti.league_name ILIKE :search
            )"""
            legal_search_clause = """(
                CONCAT(pi."firstName", ' ', pi."lastName") ILIKE :search
                OR ti.name ILIKE :search
                OR ti.area_name ILIKE :search
                OR ti.league_name ILIKE :search
            )"""
            birthday_where_clauses.append(birthday_search_clause)
            legal_where_clauses.append(legal_search_clause)
            params["search"] = search_term

        # Legal type filter (only applies to legal notifications)
        if legal_type and len(legal_type) > 0:
            placeholders = []
            for i, ltype in enumerate(legal_type):
                param_name = f"legal_type_{i}"
                placeholders.append(f":{param_name}")
                params[param_name] = ltype
            legal_where_clauses.append(
                f'c.contract_type IN ({", ".join(placeholders)})'
            )

        # Assigned to filters
        if assigned_to_me and current_user_id and current_user_email:
            birthday_where_clauses.append("ns.user_id = :current_user_id")
            legal_where_clauses.append(":current_user_email = ANY(c.notify)")
            params["current_user_id"] = current_user_id
            params["current_user_email"] = current_user_email
        elif assigned_to_contact_id and len(assigned_to_contact_id) > 0:
            placeholders = []
            for i, contact_id in enumerate(assigned_to_contact_id):
                param_name = f"assigned_contact_bd_{i}"
                placeholders.append(f":{param_name}")
                params[param_name] = contact_id
            birthday_where_clauses.append(f"ns.user_id IN ({', '.join(placeholders)})")

            # For legal notifications, check if any of the contact IDs are in the notify array
            legal_contact_conditions = []
            for i, contact_email in enumerate(assigned_to_email):
                param_name = f"assigned_contact_legal_{i}"
                legal_contact_conditions.append(f":{param_name} = ANY(c.notify)")
                params[param_name] = contact_email
            legal_where_clauses.append(f"({' OR '.join(legal_contact_conditions)})")

        # Date range filter
        if due_date_start:
            # For birthday notifications, filter by next birthday date (birthday in current or next year)
            # Handle leap year birthdays properly by using make_date function with LEAST to cap Feb 29 to Feb 28 in non-leap years
            birthday_where_clauses.append(
                """
                (
                    CASE
                        WHEN EXTRACT(MONTH FROM pi."birthDate"::date) > EXTRACT(MONTH FROM CURRENT_DATE)
                             OR (EXTRACT(MONTH FROM pi."birthDate"::date) = EXTRACT(MONTH FROM CURRENT_DATE)
                                 AND EXTRACT(DAY FROM pi."birthDate"::date) >= EXTRACT(DAY FROM CURRENT_DATE))
                        THEN make_date(
                            EXTRACT(YEAR FROM CURRENT_DATE)::int,
                            EXTRACT(MONTH FROM pi."birthDate"::date)::int,
                            LEAST(
                                EXTRACT(DAY FROM pi."birthDate"::date)::int,
                                EXTRACT(DAY FROM (make_date(EXTRACT(YEAR FROM CURRENT_DATE)::int, EXTRACT(MONTH FROM pi."birthDate"::date)::int, 1) + INTERVAL '1 month - 1 day'))::int
                            )
                        )
                        ELSE make_date(
                            (EXTRACT(YEAR FROM CURRENT_DATE) + 1)::int,
                            EXTRACT(MONTH FROM pi."birthDate"::date)::int,
                            LEAST(
                                EXTRACT(DAY FROM pi."birthDate"::date)::int,
                                EXTRACT(DAY FROM (make_date((EXTRACT(YEAR FROM CURRENT_DATE) + 1)::int, EXTRACT(MONTH FROM pi."birthDate"::date)::int, 1) + INTERVAL '1 month - 1 day'))::int
                            )
                        )
                    END
                ) >= :due_date_start
            """
            )
            params["due_date_start"] = due_date_start

            # For legal notifications, filter by end_date
            legal_where_clauses.append("c.end_date >= :due_date_start")

        if due_date_end:
            # For birthday notifications, filter by next birthday date
            # Handle leap year birthdays properly by using make_date function with LEAST to cap Feb 29 to Feb 28 in non-leap years
            birthday_where_clauses.append(
                """
                (
                    CASE
                        WHEN EXTRACT(MONTH FROM pi."birthDate"::date) > EXTRACT(MONTH FROM CURRENT_DATE)
                             OR (EXTRACT(MONTH FROM pi."birthDate"::date) = EXTRACT(MONTH FROM CURRENT_DATE)
                                 AND EXTRACT(DAY FROM pi."birthDate"::date) >= EXTRACT(DAY FROM CURRENT_DATE))
                        THEN make_date(
                            EXTRACT(YEAR FROM CURRENT_DATE)::int,
                            EXTRACT(MONTH FROM pi."birthDate"::date)::int,
                            LEAST(
                                EXTRACT(DAY FROM pi."birthDate"::date)::int,
                                EXTRACT(DAY FROM (make_date(EXTRACT(YEAR FROM CURRENT_DATE)::int, EXTRACT(MONTH FROM pi."birthDate"::date)::int, 1) + INTERVAL '1 month - 1 day'))::int
                            )
                        )
                        ELSE make_date(
                            (EXTRACT(YEAR FROM CURRENT_DATE) + 1)::int,
                            EXTRACT(MONTH FROM pi."birthDate"::date)::int,
                            LEAST(
                                EXTRACT(DAY FROM pi."birthDate"::date)::int,
                                EXTRACT(DAY FROM (make_date((EXTRACT(YEAR FROM CURRENT_DATE) + 1)::int, EXTRACT(MONTH FROM pi."birthDate"::date)::int, 1) + INTERVAL '1 month - 1 day'))::int
                            )
                        )
                    END
                ) <= :due_date_end
            """
            )
            params["due_date_end"] = due_date_end

            # For legal notifications, filter by end_date
            legal_where_clauses.append("c.end_date <= :due_date_end")

        # Build WHERE clauses
        birthday_where_clause = "WHERE " + " AND ".join(birthday_where_clauses)
        legal_where_clause = "WHERE " + " AND ".join(legal_where_clauses)

        # Build ORDER BY clause
        order_clause = "ORDER BY date DESC"  # Default sorting
        if sort_by:
            sort_field_map = {
                "player_name": "player_name",
                "team_name": "team_name",
                "team_country": "team_country",
                "team_league": "team_league",
                "due_date": "date",
                "date": "date",
                "type": "notification_type",
            }

            if sort_by in sort_field_map:
                sort_direction = "DESC" if sort_order.lower() == "desc" else "ASC"
                order_clause = f"ORDER BY {sort_field_map[sort_by]} {sort_direction}"

        # Count query for birthday notifications
        birthday_count_query = text(
            f"""
            SELECT COUNT(*) as count
            FROM {settings.PG_SCHEMA}.notifications_settings ns
            JOIN {settings.PG_SCHEMA}.player_records pr ON pr.id = ns.player_id
            JOIN wyscout.player_info2 pi ON pi."playerId" = pr."playerId"
            LEFT JOIN wyscout.team_info2 pti ON pti."teamId" = pi."currentTeamId"
            {birthday_where_clause}
        """
        )

        # Count query for legal notifications
        legal_count_query = text(
            f"""
            SELECT COUNT(*) as count
            FROM {settings.PG_SCHEMA}.contracts c
            LEFT JOIN {settings.PG_SCHEMA}.player_records pr ON pr.id = c.player_id
            LEFT JOIN wyscout.player_info2 pi ON pi."playerId" = pr."playerId"
            LEFT JOIN wyscout.team_info2 ti ON ti."teamId" = c."teamId"
            {legal_where_clause}
        """
        )

        birthday_count = db.execute(birthday_count_query, params).scalar()
        legal_count = db.execute(legal_count_query, params).scalar()
        total_count = birthday_count + legal_count

        # Main query combining both birthday and legal notifications
        combined_query = text(
            f"""
            (
                SELECT
                    'birthday' as notification_type,
                    pr.id as player_record_id,
                    pr."playerId",
                    CONCAT(pi."firstName", ' ', pi."lastName") as player_name,
                    pi.team_name,
                    NULL as contract_id,
                    pi."currentTeamId" as "teamId",
                    NULL as legal_type,
                    (
                        CASE
                            WHEN EXTRACT(MONTH FROM pi."birthDate"::date) > EXTRACT(MONTH FROM CURRENT_DATE)
                                 OR (EXTRACT(MONTH FROM pi."birthDate"::date) = EXTRACT(MONTH FROM CURRENT_DATE)
                                     AND EXTRACT(DAY FROM pi."birthDate"::date) >= EXTRACT(DAY FROM CURRENT_DATE))
                            THEN make_date(
                                EXTRACT(YEAR FROM CURRENT_DATE)::int,
                                EXTRACT(MONTH FROM pi."birthDate"::date)::int,
                                LEAST(
                                    EXTRACT(DAY FROM pi."birthDate"::date)::int,
                                    EXTRACT(DAY FROM (make_date(EXTRACT(YEAR FROM CURRENT_DATE)::int, EXTRACT(MONTH FROM pi."birthDate"::date)::int, 1) + INTERVAL '1 month - 1 day'))::int
                                )
                            )
                            ELSE make_date(
                                (EXTRACT(YEAR FROM CURRENT_DATE) + 1)::int,
                                EXTRACT(MONTH FROM pi."birthDate"::date)::int,
                                LEAST(
                                    EXTRACT(DAY FROM pi."birthDate"::date)::int,
                                    EXTRACT(DAY FROM (make_date((EXTRACT(YEAR FROM CURRENT_DATE) + 1)::int, EXTRACT(MONTH FROM pi."birthDate"::date)::int, 1) + INTERVAL '1 month - 1 day'))::int
                                )
                            )
                        END
                    ) as date
                FROM {settings.PG_SCHEMA}.notifications_settings ns
                JOIN {settings.PG_SCHEMA}.player_records pr ON pr.id = ns.player_id
                JOIN wyscout.player_info2 pi ON pi."playerId" = pr."playerId"
                LEFT JOIN wyscout.team_info2 pti ON pti."teamId" = pi."currentTeamId"
                {birthday_where_clause}
            )
            UNION ALL
            (
                SELECT
                    'legal' as notification_type,
                    pr.id as player_record_id,
                    pr."playerId",
                    CONCAT(pi."firstName", ' ', pi."lastName") as player_name,
                    COALESCE(ti.name, pi.team_name) as team_name,
                    c.id as contract_id,
                    c."teamId",
                    c.contract_type as legal_type,
                    c.end_date as date
                FROM {settings.PG_SCHEMA}.contracts c
                LEFT JOIN {settings.PG_SCHEMA}.player_records pr ON pr.id = c.player_id
                LEFT JOIN wyscout.player_info2 pi ON pi."playerId" = pr."playerId"
                LEFT JOIN wyscout.team_info2 ti ON ti."teamId" = c."teamId"
                {legal_where_clause}
            )
            {order_clause}
            LIMIT :limit OFFSET :skip
        """
        )

        notifications = db.execute(combined_query, params).mappings().all()

        return {
            "total_count": total_count,
            "total_pages": (total_count + limit - 1) // limit,
            "limit": limit,
            "offset": page,
            "data": [dict(notification) for notification in notifications],
        }


activity = CRUDActivity(models.Activity)
