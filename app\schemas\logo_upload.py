import uuid
from typing import TYPE_CHECKING
from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)

if TYPE_CHECKING:
    from app.schemas.football_field import FootballField


class LogoUploadUpdate(ExtendedUpdateBase):
    ...


class LogoUploadCreate(ExtendedCreateBase):
    football_field_id: uuid.UUID
    id: str


class LogoUpload(ExtendedBase):
    football_field_id: uuid.UUID
    football_field: "FootballField"
    id: str

    class Config:
        orm_mode = True