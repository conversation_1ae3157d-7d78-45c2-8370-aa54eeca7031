import uuid
from typing import Optional
from pydantic import BaseModel, <PERSON>
from fastapi_users import schemas
from datetime import datetime
from app.schemas.user_role import UserRole
from app.schemas.organization import OrganizationShort


class UserRead(schemas.BaseUser[uuid.UUID]):
    is_enabled: bool
    organization_id: uuid.UUID
    role_id: uuid.UUID
    # otp_enabled: Optional[bool]
    # otp_verified: Optional[bool]
    # otp_base32: str
    # otp_url: str


class UserCreate(schemas.BaseUserCreate):
    is_enabled: bool = False
    organization_id: Optional[uuid.UUID]
    organization_name: Optional[str]
    role_id: Optional[uuid.UUID]
    first_name: str = ''
    last_name: str = ''
    accept_terms: bool
    accept_marketing_emails: Optional[bool] = False
    date_created: Optional[datetime] = Field(default_factory=datetime.now)
    phone_number: Optional[str] = ''
    use_whatsapp: Optional[bool] = False


class UserUpdate(schemas.BaseUserUpdate):
    is_enabled: Optional[bool]
    organization_id: Optional[uuid.UUID]
    role_id: Optional[uuid.UUID]
    first_name: Optional[str]
    last_name: Optional[str]
    accept_terms: Optional[bool]
    accept_marketing_emails: Optional[bool]
    phone_number: Optional[str]
    use_whatsapp: Optional[bool]
    # otp_enabled: Optional[bool]
    # otp_verified: Optional[bool]
    # otp_base32: Optional[str]
    # otp_url: Optional[str]


class User(UserRead):
    user_role: UserRole
    organization: OrganizationShort
    phone_number: Optional[str]
    use_whatsapp: Optional[bool]


class UserShort(BaseModel):
    id: uuid.UUID
    email: str

    class Config:
        orm_mode=True

class UserOTPVerify(BaseModel):
    email: str
    token: str

class UserRoleDefault(BaseModel):
    user_id: uuid.UUID
    role_id: uuid.UUID

    class Config:
        orm_mode=True

class UserRoleDefaultDelete(UserRoleDefault):
    role_id: Optional[uuid.UUID]

class UserRoleDefaultInput(BaseModel):
    billing_email: str
    role_name: str
    product_name: str

    
