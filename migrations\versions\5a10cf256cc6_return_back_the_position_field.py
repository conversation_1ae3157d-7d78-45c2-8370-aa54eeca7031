"""Return back the position field

Revision ID: 5a10cf256cc6
Revises: 4a73aedce62b
Create Date: 2023-08-15 11:28:31.871850

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5a10cf256cc6'
down_revision = '4a73aedce62b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('community_proposals', sa.Column('position', postgresql.ARRAY(sa.String()), nullable=True), schema='crm_test')
    op.add_column('community_proposals', sa.Column('foot', sa.String(), nullable=True), schema='crm_test')
    op.drop_constraint('notifications_settings_user_id_fkey', 'notifications_settings', schema='crm_test', type_='foreignkey')
    op.drop_constraint('notifications_settings_player_id_fkey', 'notifications_settings', schema='crm_test', type_='foreignkey')
    op.create_foreign_key(None, 'notifications_settings', 'player_records', ['player_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    op.create_foreign_key(None, 'notifications_settings', 'user', ['user_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'notifications_settings', schema='crm_test', type_='foreignkey')
    op.drop_constraint(None, 'notifications_settings', schema='crm_test', type_='foreignkey')
    op.create_foreign_key('notifications_settings_player_id_fkey', 'notifications_settings', 'player_records', ['player_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    op.create_foreign_key('notifications_settings_user_id_fkey', 'notifications_settings', 'user', ['user_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    op.drop_column('community_proposals', 'foot', schema='crm_test')
    op.drop_column('community_proposals', 'position', schema='crm_test')
    # ### end Alembic commands ###