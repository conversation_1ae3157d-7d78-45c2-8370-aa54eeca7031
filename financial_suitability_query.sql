WITH player_qry AS (
  SELECT pr.*, pi."currentTeamId", cte.rating, ce.comp_median_rating,
         pi."fullName", pi."firstName", pi."lastName", pi."shortName",
         pym.avg_mins_in_year, pi.current_value, cte.category
    FROM :schema.player_records pr
    JOIN wyscout.player_info2 pi ON pr."playerId" = pi."playerId"
    LEFT JOIN wyscout.competition_teams cte ON pi."currentTeamId" = cte."teamId"
    LEFT JOIN wyscout.competition_ratings ce ON cte."competitionId" = ce."competitionId"
    LEFT JOIN wyscout.player_year_minutes pym ON pr."playerId" = pym."playerId"
   WHERE pr."playerId" = :player_id
),
avg_team_value_subquery AS (
  SELECT pi."currentTeamId" AS teamId, AVG(pi.current_value) AS avg_player_value
    FROM wyscout.player_info2 pi
   WHERE pi.current_value IS NOT NULL
   GROUP BY pi."currentTeamId"
),
source_qry AS (
  SELECT DISTINCT ON (tr.id)
         tr.id, str.source_id, c.email, c.contact_organization
    FROM :schema.team_requests tr
    JOIN :schema.source_to_record str ON str.team_request_id = tr.id
    JOIN :schema.contacts c ON str.source_id = c.id
   ORDER BY tr.id
),
team_qry AS (
  SELECT tr.id, sq.source_id, ti.name AS officialName, ti.area_name, ti."imageDataURL",
         cte.name AS league_name, cte.rating, cte."divisionLevel",
         tr.max_value, tr.max_net_salary, tr.position, tr.last_updated,
         tr."teamId", tr.organization_id, tr.status, tr.transfer_period,
         tr.type, cte."competitionId", tr.is_community, tr.foot,
         atvs.avg_player_value,
         CONCAT(sq.email, ', ', sq.contact_organization, ': ') AS source
    FROM :schema.team_requests tr
    LEFT JOIN wyscout.competition_teams cte ON tr."teamId" = cte."teamId"
    LEFT JOIN wyscout.team_info2 ti      ON ti."teamId" = tr."teamId"
    LEFT JOIN source_qry sq              ON sq.id = tr.id
    LEFT JOIN avg_team_value_subquery atvs ON tr."teamId" = atvs.teamId
   WHERE (tr.organization_id = :org_id OR tr.is_community = TRUE)
     AND tr.transfer_period && ARRAY['summer_2025']::varchar[]
),
comp_qry AS (
  SELECT tq.*, ce.comp_median_rating,
         CASE WHEN tq.is_community = TRUE AND tq.organization_id != :org_id THEN TRUE ELSE FALSE END AS is_community_final
    FROM team_qry tq
    LEFT JOIN wyscout.competition_ratings ce ON ce."competitionId" = tq."competitionId"
),
components AS (
  SELECT cq.source, cq.league_name, pq."fullName", pq."firstName", pq."lastName", pq."shortName",
         cq.foot, cq.officialName AS team_name, cq.area_name, cq."divisionLevel", cq.type,
         cq.max_value, cq.max_net_salary, cq.transfer_period,
         cq.rating AS request_team_rating, pq.rating AS player_team_rating,
         cq.comp_median_rating AS request_comp_rating, pq.comp_median_rating AS player_comp_rating,
         cq."teamId", pq.position AS player_pos, cq.position AS requested_pos,
         cq.is_community_final AS is_community, cq.status,
         (LEAST(70,COALESCE(pq.avg_mins_in_year,0))/70)^2                AS player_min_suitability,
         (1-LEAST(ABS(pq.comp_median_rating-cq.comp_median_rating)/cq.comp_median_rating,1))^2 AS comp_elo_suitability,
         (1-LEAST(ABS(pq.rating-cq.rating)/cq.rating,1))^2               AS team_elo_suitability,
         CASE WHEN pq.current_value IS NOT NULL AND cq.avg_player_value>0 AND pq.current_value>0
              THEN LEAST(1.0, cq.avg_player_value/pq.current_value)
              ELSE 1.0
         END AS financial_suitability,
         cq.avg_player_value                                        AS team_average_value,
         pq.current_value                                           AS player_market_value
    FROM player_qry pq
    CROSS JOIN comp_qry cq
   WHERE (cq.position = ANY(pq.position)
          OR (cq.position='rb'  AND 'rwb'=ANY(pq.position))
          OR (cq.position='rwb' AND 'rb'=ANY(pq.position))
          OR (cq.position='lb'  AND 'lwb'=ANY(pq.position))
          OR (cq.position='lwb' AND 'lb'=ANY(pq.position))
          OR (cq.position='lcb' AND 'cb'=ANY(pq.position))
          OR (cq.position='rcb' AND 'cb'=ANY(pq.position)))
     AND (pq.organization_id = :org_id OR cq.is_community_final)
     AND (cq."teamId" != pq."currentTeamId" OR pq."currentTeamId" IS NULL)
     AND NOT (
      pq.player_team_category = 'default'
      AND cq.team_category    <> 'default'
    )
),
scored AS (
  SELECT *,
        (0.65*team_elo_suitability
          + 0.25*comp_elo_suitability
          + 0.10*player_min_suitability)^2
          * (1 - 0.6 * (1 - financial_suitability))     AS suitability_score
  FROM components
),
final AS (
  SELECT *,
         CASE
           WHEN suitability_score_60    < 0.1 THEN 0
           WHEN suitability_score_60    < 0.2 THEN 1.0
           WHEN suitability_score_60    < 0.3 THEN 1.5
           WHEN suitability_score_60    < 0.4 THEN 2.0
           WHEN suitability_score_60    < 0.5 THEN 2.5
           WHEN suitability_score_60    < 0.6 THEN 3.0
           WHEN suitability_score_60    < 0.7 THEN 3.5
           WHEN suitability_score_60    < 0.8 THEN 4.0
           WHEN suitability_score_60    < 0.9 THEN 4.5
           ELSE                              5.0
         END AS platform_suitability
  FROM scored
)
SELECT * 
  FROM final
 ORDER BY platform_suitability_old DESC NULLS LAST;
