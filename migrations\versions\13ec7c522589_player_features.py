""" player features

Revision ID: 13ec7c522589
Revises: 4ca05d9bde17
Create Date: 2022-09-01 13:36:49.901209

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '13ec7c522589'
down_revision = '4ca05d9bde17'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('player_features',
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('speed', sa.Integer(), nullable=True),
    sa.Column('explosive', sa.Integer(), nullable=True),
    sa.Column('dynamic', sa.Integer(), nullable=True),
    sa.Column('build', sa.Integer(), nullable=True),
    sa.Column('intelligence', sa.Integer(), nullable=True),
    sa.<PERSON>umn('scanning', sa.Integer(), nullable=True),
    sa.<PERSON>umn('pressing', sa.Integer(), nullable=True),
    sa.<PERSON>umn('aggresive', sa.Integer(), nullable=True),
    sa.Column('leader', sa.Integer(), nullable=True),
    sa.Column('teamwork', sa.Integer(), nullable=True),
    sa.Column('education', sa.Integer(), nullable=True),
    sa.Column('entourage', sa.Integer(), nullable=True),
    sa.Column('first_touch', sa.Integer(), nullable=True),
    sa.Column('dribble', sa.Integer(), nullable=True),
    sa.Column('progress_with_the_ball', sa.Integer(), nullable=True),
    sa.Column('play_with_the_back', sa.Integer(), nullable=True),
    sa.Column('diagonal_passing', sa.Integer(), nullable=True),
    sa.Column('through_ball_passing', sa.Integer(), nullable=True),
    sa.Column('passes_to_final_third', sa.Integer(), nullable=True),
    sa.Column('pushes_inbetween_lines', sa.Integer(), nullable=True),
    sa.Column('defensive_positioning', sa.Integer(), nullable=True),
    sa.Column('attacking_positioning', sa.Integer(), nullable=True),
    sa.Column('interceptions', sa.Integer(), nullable=True),
    sa.Column('aerial_duels', sa.Integer(), nullable=True),
    sa.Column('finishing', sa.Integer(), nullable=True),
    sa.Column('heading', sa.Integer(), nullable=True),
    sa.Column('cut_inside', sa.Integer(), nullable=True),
    sa.Column('gk_1_vs_one_duels', sa.Integer(), nullable=True),
    sa.Column('defensive_1_vs_one_duels', sa.Integer(), nullable=True),
    sa.Column('closing_space', sa.Integer(), nullable=True),
    sa.Column('ball_control', sa.Integer(), nullable=True),
    sa.Column('reflexes', sa.Integer(), nullable=True),
    sa.Column('set_pieces', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['player_id'], ['crm_dev.player_records.id'], ),
    sa.PrimaryKeyConstraint('player_id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_player_features_player_id'), 'player_features', ['player_id'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_dev_player_features_player_id'), table_name='player_features', schema='crm_dev')
    op.drop_table('player_features', schema='crm_dev')
    # ### end Alembic commands ###