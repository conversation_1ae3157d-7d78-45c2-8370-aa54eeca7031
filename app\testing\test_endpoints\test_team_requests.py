from fastapi.testclient import Test<PERSON>lient
import json
from app.main import app
from app.testing.fixtures import (switch_between_users,
 client_chestnite,
  client_enskai,
  get_current_user_enskai_owner,
  get_current_user_chesnite_limited)
import pytest

client = TestClient(app)

team_requests_dict = {'request_id_enskai': None, 'created_request_obj_enskai': None,
'request_id_chestnite': None, 'created_request_obj_chestnite': None, 'teamId': '480084'}

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_read_team_requests(switch_between_users: TestClient):
    client, org = switch_between_users
    response = client.get("team_requests/")
    data = response.json()
    assert data is not None, "Response returned null data"

    if org == 'enskai':
        first_data = data[0]
        assert "transfer_period" in first_data, "transfer_period is not in the returned contacts"
        assert "position" in first_data, "position is not in the returned contacts"
        assert "created_by" in first_data, "created_by is not in the returned contacts"
        assert "created_at" in first_data, "created_at is not in the returned contacts"
        assert "team_info" in first_data,"team_info is not in the returned contacts"
    else:
        assert len(data) == 0 # Chestnite have one transfer --

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_create_team_request(switch_between_users: TestClient):
    client, org = switch_between_users

    data = json.dumps({
    "name": "FH",
    "teamId": "7731",
    "area_name": "Iceland",
    "division_level": "1",
    "max_age": "",
    "max_value": "",
    "max_net_salary": "",
    "segment": 4,
    "position": "cb",
    "transfer_period": [
        "winter_2023"
    ],
    "foot": "no_preference",
    "type": [
        "loan_with_option"
    ],
    "stage": "request_intake",
    "source_to_record": [],
    "reason_for_outcome": ""
})

    response = client.post('team_requests/?send_mail=false', data=data, headers={"Content-Type": "application/json"})
    assert response.status_code == 200, f"Request failed, status code is {response.status_code}"
    reponse_obj = response.json()

    team_requests_dict[f"created_request_obj_{org}"] = reponse_obj
    team_requests_dict[f"request_id_{org}"] = reponse_obj['id']
    assert team_requests_dict[f"request_id_{org}"] is not None, "Request id is None"

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_get_specific_team_request(switch_between_users: TestClient):
    client, org = switch_between_users

    response = client.get(f"""team_requests/{team_requests_dict[f"request_id_{org}"]}""")
    assert response.status_code == 200, f"Request failed, status code is {response.status_code}"
    returned_obj = response.json()
    assert returned_obj == team_requests_dict[f"created_request_obj_{org}"], "The returned object should be equal to the just created one"
    
    non_existent_id_response = client.get("team_requests/9f6b7234-6a6d-11ed-a1eb-0242ac120002")
    assert non_existent_id_response.status_code == 404, "Maybe we have this id in the DB -_-"

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_edit_specific_team_request(switch_between_users: TestClient):
    client, org = switch_between_users
    data = json.dumps({
    "name": "FH",
    "teamId": "7731",
    "area_name": "Iceland",
    "division_level": "1",
    "max_age": "",
    "max_value": "",
    "max_net_salary": "6969",
    "segment": 4,
    "position": "cb",
    "transfer_period": [
        "summer_2023"
    ],
    "foot": "left",
    "type": [
        "loan_with_option"
    ],
    "stage": "request_intake",
    "source_to_record": [],
    "reason_for_outcome": ""
})
    response = client.put(f"""team_requests/{team_requests_dict[f"request_id_{org}"]}""", data=data, headers={"Content-Type": "application/json"})
    assert response.status_code == 200,  f"Request failed, status code is {response.status_code}"
    response_obj = response.json()
    assert response_obj != team_requests_dict[f"created_request_obj_{org}"], "Put request should have made updates"
    assert response_obj['max_net_salary'] == 6969, "Max net salary is not changed to '6969'"
    assert response_obj['transfer_period'][0] == 'summer_2023', "transfer_period is not changed, but it should be"
    assert response_obj['foot'] == 'left', 'foot should have 2 items'

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_delete_specific_contact(switch_between_users: TestClient):
    client, org = switch_between_users

    response = client.delete(f"""team_requests/{team_requests_dict[f"request_id_{org}"]}""")
    assert response.status_code == 200,  f"Request failed, status code is {response.status_code}"
    response = client.get(f"""team_requests/{team_requests_dict[f"request_id_{org}"]}""")
    assert response.status_code == 404, "Id should be deleted, but apperantly it isn't"