import uuid
from typing import Optional, List, TYPE_CHECKING

from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)
from app.schemas.enums import ConstrolStage, TransferStrategyStaff, StaffRoles
from pydantic import BaseModel
from app.schemas.staff_info import StaffInfo
from app.schemas.staff_upload import StaffUpload
from app.schemas.assigned_to_record import AssignedToRecord
from app.schemas.comment import Comment

if TYPE_CHECKING:
    from app.schemas.contract import ContractShort, Contract


class StaffRecordUpdate(ExtendedUpdateBase):
    assigned_to_record: Optional[List[uuid.UUID]]
    staff_id: int
    early_termination_fee: Optional[float]
    description: Optional[str]
    licenses: Optional[List[str]]
    languages: Optional[List[str]]
    current_salary: Optional[float]
    expected_salary: Optional[float]
    control_stage: Optional[ConstrolStage]
    transfer_strategy: Optional[TransferStrategyStaff]
    description: Optional[str]
    roles: Optional[List[StaffRoles]]
    regions_of_interest: Optional[List[str]]
    roles_of_interest: Optional[List[str]]

    class Config:
        use_enum_values = True
        use_cache = True
        # allow_population_by_field_name = True


class StaffRecordCreate(StaffRecordUpdate, ExtendedCreateBase):
    staff_id: int

class StaffRecord(StaffRecordCreate, ExtendedBase):
    staff_info: StaffInfo
    assigned_to_record: Optional[List[AssignedToRecord]]
    uploads: Optional[List[StaffUpload]]
    comments: List[Comment]

StaffUpload.update_forward_refs(StaffRecord=StaffRecord)


class StaffRecordShort(BaseModel):
    id: uuid.UUID
    staff_info: StaffInfo

    class Config:
        orm_mode = True
        use_cache = True
