"""Phone number in community deal

Revision ID: a2c02228e1c7
Revises: 0b93fb2cf891
Create Date: 2025-04-14 13:31:47.202398

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a2c02228e1c7'
down_revision = '0b93fb2cf891'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('community_deal', sa.Column('phone_number', sa.String(), nullable=True), schema='crm_test')


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('community_deal', 'phone_number', schema='crm_test')
    # ### end Alembic commands ###