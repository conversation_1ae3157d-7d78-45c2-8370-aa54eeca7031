"""Added staff backend. Initial layout

Revision ID: 2eccd975e145
Revises: 2a3e88e56ff7
Create Date: 2024-12-10 16:07:00.797792

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy
# revision identifiers, used by Alembic.
revision = '2eccd975e145'
down_revision = '2a3e88e56ff7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('staff_records',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('staff_id', sa.Integer(), nullable=True),
    sa.Column('current_salary', sa.Float(), nullable=True),
    sa.Column('expected_salary', sa.Float(), nullable=True),
    sa.Column('early_termination_fee', sa.Float(), nullable=True),
    sa.Column('licenses', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('languages', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('control_stage', sa.String(), nullable=True),
    sa.Column('transfer_strategy', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('roles', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('regions_of_interest', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('roles_of_interest', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('linked_tm_members', postgresql.ARRAY(sa.Integer()), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    #sa.ForeignKeyConstraint(['staff_id'], ['transfermarkt.staff_info.staff_id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_staff_records_created_at'), 'staff_records', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_staff_records_created_by'), 'staff_records', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_staff_records_is_sensitive'), 'staff_records', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_staff_records_last_updated'), 'staff_records', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_staff_records_organization_id'), 'staff_records', ['organization_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_staff_records_staff_id'), 'staff_records', ['staff_id'], unique=False, schema='crm_test')
    op.create_table('staff_uploads',
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('staff_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.ForeignKeyConstraint(['staff_id'], ['crm_test.staff_records.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_staff_uploads_created_at'), 'staff_uploads', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_staff_uploads_created_by'), 'staff_uploads', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_staff_uploads_is_sensitive'), 'staff_uploads', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_staff_uploads_last_updated'), 'staff_uploads', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_staff_uploads_organization_id'), 'staff_uploads', ['organization_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_staff_uploads_staff_id'), 'staff_uploads', ['staff_id'], unique=False, schema='crm_test')
    op.add_column('assigned_to_record', sa.Column('staff_record_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_test')
    op.create_index(op.f('ix_crm_test_assigned_to_record_staff_record_id'), 'assigned_to_record', ['staff_record_id'], unique=False, schema='crm_test')
    op.create_foreign_key(None, 'assigned_to_record', 'staff_records', ['staff_record_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    op.add_column('comments_activity', sa.Column('staff_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_test')
    op.create_index(op.f('ix_crm_test_comments_activity_staff_id'), 'comments_activity', ['staff_id'], unique=False, schema='crm_test')
    op.create_foreign_key(None, 'comments_activity', 'staff_records', ['staff_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'comments_activity', schema='crm_test', type_='foreignkey')
    op.drop_index(op.f('ix_crm_test_comments_activity_staff_id'), table_name='comments_activity', schema='crm_test')
    op.drop_column('comments_activity', 'staff_id', schema='crm_test')
    op.drop_constraint(None, 'assigned_to_record', schema='crm_test', type_='foreignkey')
    op.drop_index(op.f('ix_crm_test_assigned_to_record_staff_record_id'), table_name='assigned_to_record', schema='crm_test')
    op.drop_column('assigned_to_record', 'staff_record_id', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_staff_uploads_staff_id'), table_name='staff_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_staff_uploads_organization_id'), table_name='staff_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_staff_uploads_last_updated'), table_name='staff_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_staff_uploads_is_sensitive'), table_name='staff_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_staff_uploads_created_by'), table_name='staff_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_staff_uploads_created_at'), table_name='staff_uploads', schema='crm_test')
    op.drop_table('staff_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_staff_records_staff_id'), table_name='staff_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_staff_records_organization_id'), table_name='staff_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_staff_records_last_updated'), table_name='staff_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_staff_records_is_sensitive'), table_name='staff_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_staff_records_created_by'), table_name='staff_records', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_staff_records_created_at'), table_name='staff_records', schema='crm_test')
    op.drop_table('staff_records', schema='crm_test')
    # ### end Alembic commands ###