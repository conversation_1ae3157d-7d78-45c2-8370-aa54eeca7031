"""Added tracked transfers to the backend

Revision ID: 1405358fd81f
Revises: beaec03d6d01
Create Date: 2024-03-18 13:30:56.338953

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '1405358fd81f'
down_revision = 'beaec03d6d01'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tracked_transfers',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('position', sa.String(), nullable=True),
    sa.Column('player_name', sa.String(), nullable=True),
    sa.Column('left_name', sa.String(), nullable=True),
    sa.Column('fee', sa.String(), nullable=True),
    sa.Column('date', sa.DateTime(), nullable=True),
    sa.Column('player_url', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['request_id'], ['crm_test.team_requests.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_tracked_transfers_request_id'), 'tracked_transfers', ['request_id'], unique=False, schema='crm_test')
    op.add_column('team_requests', sa.Column('status', sa.String(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('team_requests', 'status', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_tracked_transfers_request_id'), table_name='tracked_transfers', schema='crm_test')
    op.drop_table('tracked_transfers', schema='crm_test')
    # ### end Alembic commands ###