"""Migrate testing to staging

Revision ID: 5f5d3c3db0c1
Revises: 88cc2913eb9b
Create Date: 2023-09-18 15:35:35.618864

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5f5d3c3db0c1'
down_revision = '88cc2913eb9b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_crm_dev_contacts_id'), 'contacts', ['id'], unique=False, schema='crm_dev')
    op.drop_constraint('contracts_agent_id_fkey', 'contracts', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key(None, 'contracts', 'contacts', ['agent_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', ondelete='SET NULL')
    op.drop_constraint('notifications_settings_player_id_fkey', 'notifications_settings', schema='crm_dev', type_='foreignkey')
    op.drop_constraint('notifications_settings_user_id_fkey', 'notifications_settings', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key(None, 'notifications_settings', 'player_records', ['player_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', ondelete='cascade')
    op.create_foreign_key(None, 'notifications_settings', 'contacts', ['user_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', ondelete='cascade')
    op.drop_constraint('player_records_assigned_to_id_fkey', 'player_records', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key(None, 'player_records', 'contacts', ['assigned_to_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.drop_constraint('source_to_record_source_id_fkey', 'source_to_record', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key(None, 'source_to_record', 'contacts', ['source_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', ondelete='CASCADE')
    op.add_column('user', sa.Column('first_name', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('user', sa.Column('last_name', sa.String(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'last_name', schema='crm_dev')
    op.drop_column('user', 'first_name', schema='crm_dev')
    op.drop_constraint(None, 'source_to_record', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key('source_to_record_source_id_fkey', 'source_to_record', 'contacts', ['source_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_constraint(None, 'player_records', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key('player_records_assigned_to_id_fkey', 'player_records', 'contacts', ['assigned_to_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_constraint(None, 'notifications_settings', schema='crm_dev', type_='foreignkey')
    op.drop_constraint(None, 'notifications_settings', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key('notifications_settings_user_id_fkey', 'notifications_settings', 'contacts', ['user_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', onupdate='CASCADE', ondelete='CASCADE')
    op.create_foreign_key('notifications_settings_player_id_fkey', 'notifications_settings', 'player_records', ['player_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.drop_constraint(None, 'contracts', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key('contracts_agent_id_fkey', 'contracts', 'contacts', ['agent_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_index(op.f('ix_crm_dev_contacts_id'), table_name='contacts', schema='crm_dev')
    # ### end Alembic commands ###