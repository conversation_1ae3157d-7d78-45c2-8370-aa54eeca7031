from typing import Any, List
from fastapi import APIRout<PERSON>, Depends, HTTPException, Body, BackgroundTasks
from sqlalchemy.orm import Session, Load, lazyload
from sqlalchemy import desc
from app.models.rank_output import RankOutput
from app.models.rank_record import RankRecord as RRModel

from app.schemas.ranking_comparable_positions import ComparablePositions
from app.schemas.ranking_cutoff_filters import CutoffFilters
from app.schemas.ranking_position_weights import DefaultWeights
from app.schemas.ranking_variable_definitions import VariableDefinition
from app.schemas.rank_record import (
    RankRecordCreate,
    RankRecordUpdate,
    RankRecord,
    RankRecordShort,
)
from app.schemas.rank_output import RankOutputCreate, RankOutput as RankOutputSchema
from app.schemas.enums import RankSelectFilters

from app import crud, models
from app.api import deps, utils
try:
    from ...config import settings
except:
    from app.config import settings

import requests
import json
from fastapi.responses import JSONResponse

# import pandas as pd
from fastapi.encoders import jsonable_encoder

import httpx

async def get_rank_outputs(params):
    async with httpx.AsyncClient() as client:
        # Make the asynchronous request to the external API
        prms = {key: value for key, value in params["rank_params"].items() if value is not None}

        response = await client.post(settings.RANK_API_URL,
                params=prms,
                auth=(settings.RANKS_API_USR, settings.RANKS_API_PASS), timeout=None)
        return response

router = APIRouter()

def fix_var_names(a):
    a = a.replace('::float', '')
    a = a.replace('sum("minutesTagged" * ', '')
    a = a.replace(' / sum("minutesTagged")', '')
    a = a.replace('avg(', 'avg_')
    
    if '/' in a:
        a = a.replace('/', '__') + '_ratio'
    
    a = a.replace('(', '').replace(')', '').replace('"', '')
    return a


def get_chart_factory_qry(params):
    variables = []
    avg_variables = []
    perc_variables = []
    all_raw_vars = []
    var_no = 0
    for idx, it in enumerate(params['variables']):
        
        var_no +=1
        if (it['numerator'] is not None) & (('denominator' not in it.keys())):
            all_raw_vars.append(f'''"{it['numerator'].replace('"', '')}"''')
            it['name'] = f'''"{it['numerator']}"'''
            params['variables'][idx]['name'] = it['name']
            variables.append(it['name'])
        elif (it['numerator'] is not None) & (it['denominator'] is not None):
            all_raw_vars.append(f'''"{it['numerator'].replace('"', '')}"''')
            all_raw_vars.append(f'''"{it['denominator'].replace('"', '')}"''')
            it['name'] = f'''"{it['numerator']}"::float/"{it['denominator']}"::float'''
            var_statement = f'''case when "{it['denominator']}" != 0 then {it['name']} else null end as "{it['name'].replace('"', '').replace('::float', '')}" ''' #TODO null are 0 or no
            it['name'] = it['name'].replace('::float', '')
            params['variables'][idx]['name'] = it['name']
            variables.append(var_statement)
        else:
            Exception('Wrong input')
    
    variables = list(set(variables))
    all_raw_vars = list(set(all_raw_vars))
    # params['variables'] = variables
    # print(params['variables'])

    for it in params['variables']:
        # vn = f'''sum("minutesTagged" * "{it['name'].replace('"', '')}") / sum("minutesTagged")'''
        if "/" not in it['name']:
            vn = f'''sum("minutesTagged"::float * "{it['name'].replace('"', '')}"::float) / sum("minutesTagged"::float)'''
        else:
            vn = f'''avg("{it['name'].replace('"', '')}")'''
        avg_variables.append(vn)

    for it in avg_variables:
        perc_variables.append(f''' 100*PERCENT_RANK() over (order by coalesce("{fix_var_names(it)}", 0)) as "{fix_var_names(it)}_percentile"''')

    # TODO EVENTUALLY switch to sqlalchemy
    if len(params['competitions']) > 0:
        if len(params['comparable_positions']) > 0:
            qry = f"""
                with 

                player_positions as (
                    select 
					"matchId", "playerId"
					from wyscout.player_match_positions pmp 
					where 1=1
                    and "matchId" in (
                        select "matchId" 
                        from wyscout.matches m 
                        where "date" not like '-00%' and "date"::date between {params['window_start']} and {params['window_end']}
                        and "competitionId" in {tuple(params['competitions'])}
                    )
                    and "position" in {tuple(params['comparable_positions'])}
                    group by "matchId", "playerId"
					having sum("percent") > 50 
                )
                
                , stats_df as
                (select distinct "playerId", "competitionId", "matchId", greatest("minutesTagged", 1) as "minutesTagged", {','.join(variables)}
                from wyscout.player_match_info
                where "playerId" in {tuple(params['players'])}
                and "date"::date between {params['window_start']} and {params['window_end']}

                union

                select distinct "playerId", "competitionId", "matchId", greatest("minutesTagged", 1) as "minutesTagged", {','.join(variables)}
                from wyscout.player_match_info
                join player_positions pp using ("matchId", "playerId")
                )
            """
        else:
            #TODO this or show all competitions?
            qry = f"""
                with 
                
                stats_df as
                (select distinct "playerId", "competitionId", "matchId", greatest("minutesTagged", 1) as "minutesTagged", {','.join(variables)}
                from wyscout.player_match_info
                where ("playerId" in {tuple(params['players'])}
                or "competitionId" in {tuple(params['competitions'])})
                and "date"::date between {params['window_start']} and {params['window_end']}
                )
            """
        qry2 = qry + f"""
        select * from (select "playerId", "matchId" from stats_df where "competitionId" in (select "competitionId" from wyscout.competitions where format in {tuple(params['comps_to_analyze'])})) q1
        join (select pi."playerId", "matchId", "shortName", "date", "label", c."format", {','.join(all_raw_vars)} 
            from wyscout.player_match_info pmi, wyscout.player_info2 pi, wyscout.competitions  c
            where pmi."playerId" = pi."playerId"
            and c."competitionId" = pmi."competitionId"
            and pi."playerId" in {tuple(params['players'])}) q2
        using ("playerId", "matchId") """

        qry += f"""
        , player_avgs as 
                (
                    select "playerId" as "id", {','.join([a+ f''' as "{fix_var_names(a)}" ''' for a in avg_variables])}, 'player' as "type"
                    from stats_df
                    where "competitionId" in (select "competitionId" from wyscout.competitions where format in {tuple(params['comps_to_analyze'])})
                    group by "playerId"
                )

                , competition_avgs as 
                (
                    select "competitionId" as "id", {','.join([a + f''' as "{fix_var_names(a)}" ''' for a in avg_variables])}, 'competition' as "type"
                    from stats_df
                    group by "competitionId"
                )

                , combined_avgs as 
                (  
                    select * 
                    from competition_avgs
                    union
                    select * 
                    from player_avgs
                )

                select * from (select "id", ca."type", {','.join([f'''"{fix_var_names(a)}"''' for a in avg_variables])} 
                , {','.join(perc_variables)}
                , case when ca."type" = 'competition' then concat(replace(c."name", 'unknown', 'Global'), ' Average') else p."shortName" end as "name"
                from combined_avgs ca
                left join
                (select *, 'competition' as "type_new" from wyscout.competitions) c 
                on ca.id = c."competitionId" 
                and ca."type" = c."type_new"
                left join
                (select *, 'player' as "type_new" from wyscout.players) p 
                on ca.id = p."playerId"
                and ca."type" = p."type_new") iq
                where ("id" in {tuple(params['players'])} and "type" = 'player') or ("id" in {tuple(params['competitions'])} and "type" = 'competition')
        """
    
    else:
        if len(params['comparable_positions']) > 0:
            qry = f"""
                with 

                player_positions as (
                    select 
					"matchId", "playerId"
					from wyscout.player_match_positions pmp 
					where 1=1
                    and "matchId" in (
                        select "matchId" 
                        from wyscout.matches m 
                        where "date" not like '-00%' and "date"::date between {params['window_start']} and {params['window_end']}
                    )
                    and "position" in {tuple(params['comparable_positions'])}
                    group by "matchId", "playerId"
					having sum("percent") > 50 
                )
                
                , stats_df as
                (select distinct "playerId", "competitionId", "matchId", greatest("minutesTagged", 1) as "minutesTagged", {','.join(variables)}
                from wyscout.player_match_info
                where "playerId" in {tuple(params['players'])}
                and "date"::date between {params['window_start']} and {params['window_end']}

                union

                select distinct "playerId", "competitionId", "matchId", greatest("minutesTagged", 1) as "minutesTagged", {','.join(variables)}
                from wyscout.player_match_info
                join player_positions pp using ("matchId", "playerId")
                )
            """
        else:
            #TODO this or show all competitions?
            qry = f"""
                with 
                
                stats_df as
                (select distinct "playerId", "competitionId", "matchId", greatest("minutesTagged", 1) as "minutesTagged", {','.join(variables)}
                from wyscout.player_match_info
                where "playerId" in {tuple(params['players'])}
                and "date"::date between {params['window_start']} and {params['window_end']}
                )
            """
        qry2 = qry + f"""
        select * from (select "playerId", "matchId" from stats_df
        where "competitionId" in (select "competitionId" from wyscout.competitions where format in {tuple(params['comps_to_analyze'])})) q1
        join (select pi."playerId", "matchId", "shortName", "date", "label", c."format", {','.join(all_raw_vars)} 
            from wyscout.player_match_info pmi, wyscout.player_info2 pi, wyscout.competitions c
            where pmi."playerId" = pi."playerId"
            and pmi."competitionId" = c."competitionId"
            and pi."playerId" in {tuple(params['players'])}) q2
        using ("playerId", "matchId") """
        qry += f"""
        , player_avgs as 
                (
                    select "playerId" as "id", {','.join([a+ f''' as "{fix_var_names(a)}" ''' for a in avg_variables])}, 'player' as "type"
                    from stats_df
                    where "competitionId" in (select "competitionId" from wyscout.competitions where format in {tuple(params['comps_to_analyze'])})
                    group by "playerId"
                )

                , competition_avgs as 
                (
                    select -1 as "id", {','.join([a + f''' as "{fix_var_names(a)}" ''' for a in avg_variables])}, 'competition' as "type"
                    from stats_df
                )

                , combined_avgs as 
                (  
                    select * 
                    from competition_avgs
                    union
                    select * 
                    from player_avgs
                )

                select * from (select "id", ca."type", {','.join([f'''"{fix_var_names(a)}"''' for a in avg_variables])} 
                , {','.join(perc_variables)}
                , case when ca."type" = 'competition' then concat(replace(c."name", 'unknown', 'Global'), ' Average') else p."shortName" end as "name"
                from combined_avgs ca
                left join
                (select *, 'competition' as "type_new" from wyscout.competitions) c 
                on ca.id = c."competitionId" 
                and ca."type" = c."type_new"
                left join
                (select *, 'player' as "type_new" from wyscout.players) p 
                on ca.id = p."playerId"
                and ca."type" = p."type_new") iq
                where ("id" in {tuple(params['players'])} and "type" = 'player') or ("id" in {tuple(params['competitions'])} and "type" = 'competition')
        """
    


    qry = qry.replace(',)', ')').replace('''"playerId" in ()''', '''1=1''').replace('''"competitionId" in ()''', '''1=1''').replace('''"id" in ()''', '''1=1''')
    qry2 = qry2.replace(',)', ')').replace('''"playerId" in ()''', '''1=1''').replace('''"competitionId" in ()''', '''1=1''').replace('''"id" in ()''', '''1=1''')
    # print(qry)
    
    return qry, qry2


@router.get("/comparable_positions", response_model=List[ComparablePositions])
def read_comparable_positions(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve Default Comparable Positions.
    """
    # utils.check_get_all(ComparablePositions, current_user)
    return crud.ranking_comparable_positions.get_all(db)


@router.get("/cutoff_filters", response_model=CutoffFilters)
def read_default_cutoffs(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve Default Cutoffs.
    """
    # utils.check_get_all(CutoffFilters, current_user)
    return crud.ranking_cutoffs.get_all(db)[0]


@router.get("/default_weights", response_model=List[DefaultWeights])
def read_default_weights(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve Default Variable Weights per Role.
    """
    # utils.check_get_all(DefaultWeights, current_user)
    return crud.variable_weights.get_all(db)


@router.get("/variable_definitions", response_model=List[VariableDefinition])
def read_variable_definitions(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve Variable Definitions for Quality.
    """
    utils.check_get_all(VariableDefinition, current_user)
    return crud.variable_definition.get_all(db)


@router.get("/filter_options/{filter_type}")
def read_select_filter_options(
    filter_type: RankSelectFilters,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve options for filters on rank outputs of quality.
    """
    if filter_type.value == "agency":
        out = db.execute(
            "select distinct agency from wyscout.player_info2 where agency is not null"
        ).all()
    if filter_type.value == "roles":
        out = db.execute(
            "select distinct primary_position as position from derived_tables.player_roles pr where primary_position is not null"
        ).all()
    if filter_type.value == "areas":
        out = db.execute(
            'select distinct "name" area_name from wyscout.areas where "name" is not null'
        ).all()
    # utils.check_get_all(VariableDefinition, current_user)
    return out


@router.get("/chart_factory_vars")
def get_chart_factory_vars(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    qry = """
        SELECT *
        FROM information_schema.columns
        WHERE table_schema = 'wyscout'
        AND table_name   = 'advanced_stats';
    """
    out = db.execute(qry).all()
    out = sorted(
        [
            c["column_name"]
            for c in out
            if c["column_name"] not in ["playerId", "matchId"]
            and "location" not in c["column_name"]
            and "centrality" not in c["column_name"]
        ]
    )
    # utils.check_get_all(VariableDefinition, current_user)
    return out


@router.post("/chart_factory_data")
def get_chart_factory_data(
    params=Body(...),
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve the stats for chart factory
    """
    
    params = json.loads(params)

    # Ensure only unique set of numerator / denominator pairs are in the defs

    params['variables'] = list(map(dict, set(tuple(sorted(d.items())) for d in params['variables'])))
    qry, qry2 = get_chart_factory_qry(params)
    out = db.execute(qry).all()
    if params['get_match_data']:
        out2 = db.execute(qry2).all()
    else:
        out2 = None

    out = {'cf_data':out, 'match_stats':out2}
    # out = 'Endpoint live'
    # utils.check_get_all(VariableDefinition, current_user)
    return out


@router.put("/ranks")
async def query_rank_api(
    params=Body(...),
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Forwards the post request to the rank api
    """

    try:
        params = json.loads(params)
    except:
        pass

    

    RRC = RankRecordCreate(
        config=json.dumps(params["rank_params"]),
        name=params["rank_record"]["name"],
        description=params["rank_record"]["description"],
        purpose=params["rank_record"]["purpose"],
        for_who=params["rank_record"]["for_who"],
        youth=params["rank_record"]["youth"],
        target_position=params["rank_record"]["target_position"],
        default_quality=params["rank_record"]["default_quality"],
    )

    created_record = crud.rank_record.create_with_user(
        db=db, obj_in=RRC, user=current_user
    )
    
    params["rank_params"]["db_row_id"] = str(created_record.id)


    if created_record.id:
        try:
            # resp = requests.post(
            #     settings.RANK_API_URL,
            #     params=params["rank_params"],
            #     auth=(settings.RANKS_API_USR, settings.RANKS_API_PASS),
            # )

            resp = await get_rank_outputs(params)
        except Exception as e:
            raise Exception(e)

    else:
        HTTPException(
            400, "Sattellite Rank API returned an error. Check logs on Cloud Run."
        )

    return JSONResponse(
        status_code=resp.status_code,
        content=json.dumps({"rank_record_id": str(created_record.id)}),
    )


@router.get("/all_rank_queries", response_model=List[RankRecordShort])
async def get_rank_query_records(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve All rank queries, with or without results
    """
    # out =  crud.rank_record.get_all_ordered(db)
    # utils.check_get_all(VariableDefinition, current_user)
    out = (
        db.query(RRModel)
        .with_entities(
            RRModel.id,
            RRModel.name,
            RRModel.target_position,
            RRModel.purpose,
            RRModel.for_who,
            RRModel.config,
            RRModel.youth,
            RRModel.created_at,
        )
        .options(
            lazyload("*"),
        )
        .filter(RRModel.organization_id == current_user.organization_id)
        .order_by(desc(RRModel.created_at))
        .all()
    )

    return out


@router.get("/rank_query_ranks/{id}", response_model=List[RankOutputSchema])
async def get_rank_query_ranks(
    id: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get ranks and ratings for a single run
    """
    # utils.check_get_all(RankOutputSchema, current_user)
    return crud.rank_output.get_all_filtered(db, id=[id])

@router.get("/average_quality")
def get_average_quality(
    *,
    db: Session = Depends(deps.get_db),
    playerId: str,
    player_role:str,
    last_n_averages:int = 1,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Return overall quality estimation.
    """
    qry = f"""
        SELECT *
        FROM player_quality.average_quality
        WHERE "playerId" = {playerId} and api_target_role = '{player_role}'
        ORDER BY "avg_computation_date"::date desc 
        limit {last_n_averages}

    """
    out = db.execute(qry).all()

    return out
