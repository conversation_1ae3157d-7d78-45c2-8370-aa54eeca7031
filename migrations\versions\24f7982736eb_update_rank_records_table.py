"""update rank records table

Revision ID: 24f7982736eb
Revises: dc0c12b18414
Create Date: 2022-11-15 04:51:55.336395

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '24f7982736eb'
down_revision = 'dc0c12b18414'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rank_records', 'results', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rank_records', sa.Column('results', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_dev')
    # ### end Alembic commands ###