"""Added billing email column to orgs

Revision ID: c8217411115d
Revises: 7797ddeb123e
Create Date: 2024-03-08 11:26:31.190100

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c8217411115d'
down_revision = '7797ddeb123e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('billing_email', sa.String(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organizations', 'billing_email', schema='crm_test')
    # ### end Alembic commands ###