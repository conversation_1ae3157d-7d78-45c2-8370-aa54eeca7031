from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.organization import OrganizationCreate, OrganizationUpdate
from sqlalchemy.orm import Session

class CRUDOrganization(CRUDBase[models.Organization, OrganizationCreate, OrganizationCreate]):
     def get_logo_by_id(self, db: Session, organization_id: int):
        return db.query(
            models.Organization.agency_logo_url
        ).filter(
            models.Organization.id == organization_id
        ).scalar()


organization = CRUDOrganization(models.Organization)