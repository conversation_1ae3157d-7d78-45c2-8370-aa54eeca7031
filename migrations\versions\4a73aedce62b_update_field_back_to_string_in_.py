"""Update field back to string in community table

Revision ID: 4a73aedce62b
Revises: 2e6ac73bbaec
Create Date: 2023-08-14 15:53:59.714450

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4a73aedce62b'
down_revision = '2e6ac73bbaec'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('community_proposals', sa.Column('position', postgresql.ARRAY(sa.String()), nullable=True), schema='crm_dev')
    op.add_column('community_proposals', sa.Column('foot', sa.String(), nullable=True), schema='crm_dev')
    op.alter_column('community_proposals', 'are_you_the_agent',
               existing_type=sa.BOOLEAN(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.drop_constraint('notifications_settings_user_id_fkey', 'notifications_settings', schema='crm_dev', type_='foreignkey')
    op.drop_constraint('notifications_settings_player_id_fkey', 'notifications_settings', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key(None, 'notifications_settings', 'player_records', ['player_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.create_foreign_key(None, 'notifications_settings', 'user', ['user_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'notifications_settings', schema='crm_dev', type_='foreignkey')
    op.drop_constraint(None, 'notifications_settings', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key('notifications_settings_player_id_fkey', 'notifications_settings', 'player_records', ['player_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', ondelete='CASCADE')
    op.create_foreign_key('notifications_settings_user_id_fkey', 'notifications_settings', 'user', ['user_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', ondelete='CASCADE')
    op.alter_column('community_proposals', 'are_you_the_agent',
               existing_type=sa.String(),
               type_=sa.BOOLEAN(),
               existing_nullable=True,
               schema='crm_dev')
    op.drop_column('community_proposals', 'foot', schema='crm_dev')
    op.drop_column('community_proposals', 'position', schema='crm_dev')
    # ### end Alembic commands ###