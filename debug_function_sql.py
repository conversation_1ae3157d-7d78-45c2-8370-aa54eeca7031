#!/usr/bin/env python3
"""
Debug the exact SQL that the function would generate.
"""

from datetime import date

def simulate_function_sql():
    """Simulate the exact SQL the function should generate."""
    
    # Parameters from your request
    org_id = "ebfdb98e-9af4-4ba0-a437-4ce1997132e1"
    due_date_start = date(2025, 2, 23)
    due_date_end = date(2025, 4, 23)
    
    print("Function SQL Simulation")
    print("=" * 50)
    print(f"org_id: {org_id}")
    print(f"due_date_start: {due_date_start}")
    print(f"due_date_end: {due_date_end}")
    print(f"legal_type: None (no filter)")
    print()
    
    # Build WHERE clauses exactly like the function
    legal_where_clauses = []
    legal_where_clauses.append("c.organization_id = :org_id")
    
    # Create separate WHERE clauses
    non_commission_where_clauses = legal_where_clauses.copy()
    commission_where_clauses = legal_where_clauses.copy()
    
    # Add date filters
    non_commission_where_clauses.append("c.end_date >= :due_date_start")
    non_commission_where_clauses.append("c.end_date <= :due_date_end")
    
    commission_where_clauses.append("(installment->>'dueDate')::date >= :due_date_start")
    commission_where_clauses.append("(installment->>'dueDate')::date <= :due_date_end")
    
    # Add commission validation
    commission_where_clauses.append("c.installments IS NOT NULL")
    commission_where_clauses.append("c.installments::text != 'null'")
    commission_where_clauses.append("jsonb_typeof(c.installments::jsonb) = 'array'")
    commission_where_clauses.append("jsonb_array_length(c.installments::jsonb) > 0")
    commission_where_clauses.append("installment->>'dueDate' IS NOT NULL")
    commission_where_clauses.append("installment->>'dueDate' != ''")
    
    print("WHERE clauses:")
    print("Non-commission:")
    for i, clause in enumerate(non_commission_where_clauses, 1):
        print(f"  {i}. {clause}")
    
    print("\nCommission:")
    for i, clause in enumerate(commission_where_clauses, 1):
        print(f"  {i}. {clause}")
    
    # Generate the main query
    main_query = f"""
            (
                SELECT
                    'legal' as notification_type,
                    pr.id as player_record_id,
                    pr."playerId",
                    CONCAT(pi."firstName", ' ', pi."lastName") as player_name,
                    COALESCE(ti.name, pi.team_name) as team_name,
                    c.id as contract_id,
                    c."teamId",
                    c.contract_type as legal_type,
                    c.end_date as date
                FROM shadow_eleven.contracts c
                LEFT JOIN shadow_eleven.player_records pr ON pr.id = c.player_id
                LEFT JOIN wyscout.player_info2 pi ON pi."playerId" = pr."playerId"
                LEFT JOIN wyscout.team_info2 ti ON ti."teamId" = c."teamId"
                WHERE {" AND ".join(non_commission_where_clauses)} AND c.contract_type != 'commission_agreement'
            )
            UNION ALL
            (
                SELECT
                    'legal' as notification_type,
                    pr.id as player_record_id,
                    pr."playerId",
                    CONCAT(pi."firstName", ' ', pi."lastName") as player_name,
                    COALESCE(ti.name, pi.team_name) as team_name,
                    c.id as contract_id,
                    c."teamId",
                    c.contract_type as legal_type,
                    CASE 
                        WHEN installment->>'dueDate' IS NOT NULL AND installment->>'dueDate' != '' 
                        THEN (installment->>'dueDate')::date 
                        ELSE NULL 
                    END as date
                FROM shadow_eleven.contracts c
                LEFT JOIN shadow_eleven.player_records pr ON pr.id = c.player_id
                LEFT JOIN wyscout.player_info2 pi ON pi."playerId" = pr."playerId"
                LEFT JOIN wyscout.team_info2 ti ON ti."teamId" = c."teamId"
                CROSS JOIN jsonb_array_elements(c.installments::jsonb) AS installment
                WHERE {" AND ".join(commission_where_clauses)} AND c.contract_type = 'commission_agreement'
            )
            ORDER BY date DESC
            LIMIT 99 OFFSET 0
        """
    
    print(f"\nGenerated Query:")
    print("=" * 50)
    print(main_query)
    
    # Test just the commission part
    commission_only = f"""
                SELECT
                    'legal' as notification_type,
                    pr.id as player_record_id,
                    pr."playerId",
                    CONCAT(pi."firstName", ' ', pi."lastName") as player_name,
                    COALESCE(ti.name, pi.team_name) as team_name,
                    c.id as contract_id,
                    c."teamId",
                    c.contract_type as legal_type,
                    CASE 
                        WHEN installment->>'dueDate' IS NOT NULL AND installment->>'dueDate' != '' 
                        THEN (installment->>'dueDate')::date 
                        ELSE NULL 
                    END as date
                FROM shadow_eleven.contracts c
                LEFT JOIN shadow_eleven.player_records pr ON pr.id = c.player_id
                LEFT JOIN wyscout.player_info2 pi ON pi."playerId" = pr."playerId"
                LEFT JOIN wyscout.team_info2 ti ON ti."teamId" = c."teamId"
                CROSS JOIN jsonb_array_elements(c.installments::jsonb) AS installment
                WHERE {" AND ".join(commission_where_clauses)} AND c.contract_type = 'commission_agreement'
                ORDER BY date DESC;
    """
    
    print(f"\nCommission-only Query for Testing:")
    print("=" * 50)
    print(commission_only)

if __name__ == "__main__":
    simulate_function_sql()
