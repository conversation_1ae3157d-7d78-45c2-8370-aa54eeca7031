"""Add changelog to admin module

Revision ID: 5340594caad1
Revises: 130dc0ba1314
Create Date: 2025-05-22 15:00:05.070388

"""
from alembic import op
import sqlalchemy as sa
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '5340594caad1'
down_revision = '130dc0ba1314'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('admin_changes',
    sa.Column('action_type', sa.String(), nullable=False),
    sa.Column('target_type', sa.String(), nullable=False),
    sa.Column('target_id', sa.UUID(), nullable=False),
    sa.Column('details', sa.String(), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('edit_at', sa.DateTime(), nullable=True),
    sa.Column('field', sa.String(), nullable=True),
    sa.Column('previous', sa.String(), nullable=True),
    sa.Column('updated', sa.String(), nullable=True),
    sa.Column('edit_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.ForeignKeyConstraint(['edit_by'], ['crm_test.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_admin_changes_edit_at'), 'admin_changes', ['edit_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_admin_changes_edit_by'), 'admin_changes', ['edit_by'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('share_tokens_resource_type_idx', 'share_tokens', ['resource_type'], unique=False, schema='crm_test')
    op.create_index('share_tokens_resource_id_idx', 'share_tokens', ['resource_id'], unique=False, schema='crm_test')
    op.drop_table('admin_changes', schema='crm_test')
    # ### end Alembic commands ###