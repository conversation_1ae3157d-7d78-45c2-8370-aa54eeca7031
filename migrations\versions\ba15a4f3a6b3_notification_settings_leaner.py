"""Notification settings leaner

Revision ID: ba15a4f3a6b3
Revises: a5a1400d8f7f
Create Date: 2023-07-07 13:45:56.478395

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ba15a4f3a6b3'
down_revision = 'a5a1400d8f7f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('notification_settings', 'user_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    op.alter_column('notification_settings', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    op.drop_index('ix_crm_dev_notification_settings_organization_id', table_name='notification_settings', schema='crm_dev')
    op.drop_constraint('notification_settings_organization_id_fkey', 'notification_settings', schema='crm_dev', type_='foreignkey')
    op.drop_column('notification_settings', 'organization_id', schema='crm_dev')
    op.drop_column('notification_settings', 'id', schema='crm_dev')
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    op.add_column('notification_settings', sa.Column('id', postgresql.UUID(), autoincrement=False, nullable=False), schema='crm_dev')
    op.add_column('notification_settings', sa.Column('organization_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm_dev')
    op.create_foreign_key('notification_settings_organization_id_fkey', 'notification_settings', 'organizations', ['organization_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.create_index('ix_crm_dev_notification_settings_organization_id', 'notification_settings', ['organization_id'], unique=False, schema='crm_dev')
    op.alter_column('notification_settings', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=True,
               schema='crm_dev')
    op.alter_column('notification_settings', 'user_id',
               existing_type=postgresql.UUID(),
               nullable=True,
               schema='crm_dev')
    # ### end Alembic commands ###