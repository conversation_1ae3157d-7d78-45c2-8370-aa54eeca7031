"""Change int resource_id to uuid

Revision ID: d04f3a39e241
Revises: 46b7a4f41851
Create Date: 2025-03-24 17:19:49.399219

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd04f3a39e241'
down_revision = '46b7a4f41851'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('share_tokens', 'resource_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False,
               schema='crm_test',
               postgresql_using="resource_id::text::uuid"
               )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'staff_records', schema='crm_test', type_='foreignkey')
    op.alter_column('share_tokens', 'resource_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               schema='crm_test',
               )
    op.drop_constraint(None, 'activity', schema='crm_test', type_='foreignkey')
    # ### end Alembic commands ###