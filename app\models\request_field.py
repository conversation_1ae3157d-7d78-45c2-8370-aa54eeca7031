import uuid
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, ForeignKey, Float, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
from app.config import settings
from app.db.base_class import Base
from app.models.extended_base_mixin import ExtendedBaseMixin


class RequestField(Base, ExtendedBaseMixin):
    __tablename__ = "request_fields"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    name = Column(String, index=True)
    organization_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.organizations.id"),
        index=True,
    )
    requests = relationship(
        "FieldRequests",
        primaryjoin="RequestField.id==FieldRequests.field_id",
        back_populates="request_field",
    )
    for_date = Column(DateTime, default=datetime.now)
    formation = Column(String)
    contact_name = Column(String)
    contact_email = Column(String)
    contact_phone = Column(String)


class FieldRequests(Base):
    __tablename__ = "field_requests"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    field_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.request_fields.id"),
        index=True,
    )
    request_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.team_requests.id", ondelete="CASCADE"),
        index=True,
    )

    request = relationship(
        "TeamRequest",
        viewonly=True,
        primaryjoin="TeamRequest.id==FieldRequests.request_id",
    )
    index_position = Column(Integer)
    request_field = relationship(
        "RequestField", back_populates="requests", lazy="joined"
    )
    transfer_fee = Column(Float)
    salary = Column(Float)
    x_cordinate = Column(Float)
    y_cordinate = Column(Float)
    is_team_hidden = Column(Boolean, default=False)