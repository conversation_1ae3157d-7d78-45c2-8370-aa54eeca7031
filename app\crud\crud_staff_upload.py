from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.staff_upload import StaffUploadCreate, StaffUploadUpdate
from sqlalchemy.orm import Session
from typing import List, Optional, TypeVar
from app.db.base_class import Base
from sqlalchemy.orm import Session, lazyload, Load

ModelType = TypeVar("ModelType", bound=Base)
class CRUDStaffUpload(CRUDBase[models.StaffUpload, StaffUploadCreate, StaffUploadUpdate]):
    def get_files_for_staff(self, db: Session, staff_id: str, org_id: str) -> Optional[List[ModelType]]:
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).filter(self.model.staff_id == staff_id, self.model.organization_id == org_id).all()


staff_upload = CRUDStaffUpload(models.StaffUpload)