"""init

Revision ID: 444e068efa98
Revises: 
Create Date: 2022-08-02 14:09:06.089200

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from fastapi_users_db_sqlalchemy.generics import GUID

# revision identifiers, used by Alembic.
revision = "444e068efa98"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "contacts",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("last_updated", sa.DateTime(), nullable=True),
        sa.Column("created_by", sa.String(), nullable=True),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("contact_type", sa.String(), nullable=True),
        sa.Column("title", sa.String(), nullable=True),
        sa.Column("email", sa.String(), nullable=True),
        sa.Column("organization", sa.String(), nullable=True),
        sa.Column("phone_number", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_contacts_created_at"),
        "contacts",
        ["created_at"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_contacts_created_by"),
        "contacts",
        ["created_by"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_contacts_last_updated"),
        "contacts",
        ["last_updated"],
        unique=False,
        schema="crm_dev",
    )
    op.create_table(
        "modules",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("name", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="crm_dev",
    )
    op.create_table(
        "organizations",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("last_updated", sa.DateTime(), nullable=True),
        sa.Column("created_by", sa.String(), nullable=True),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("name", sa.String(), nullable=True),
        sa.Column("description", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_organizations_created_at"),
        "organizations",
        ["created_at"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_organizations_created_by"),
        "organizations",
        ["created_by"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_organizations_last_updated"),
        "organizations",
        ["last_updated"],
        unique=False,
        schema="crm_dev",
    )
    op.create_table(
        "player_records",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("last_updated", sa.DateTime(), nullable=True),
        sa.Column("created_by", sa.String(), nullable=True),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("playerId", sa.Integer(), nullable=True),
        sa.Column("control_stage", sa.String(), nullable=True),
        sa.Column("position", sa.String(), nullable=True),
        sa.Column("quality", sa.Integer(), nullable=True),
        sa.Column("potential", sa.Integer(), nullable=True),
        sa.Column("birth_date", sa.DateTime(), nullable=True),
        sa.Column("club_asking_price", sa.Float(), nullable=True),
        sa.Column("transfer_period", sa.String(), nullable=True),
        sa.Column("agency", sa.String(), nullable=True),
        sa.Column("phone_number", sa.String(), nullable=True),
        sa.Column("description", sa.String(), nullable=True),
        sa.Column("current_gross_salary", sa.Float(), nullable=True),
        sa.Column("priority_player", sa.Boolean(), nullable=True),
        sa.Column("proactively_scouted", sa.Boolean(), nullable=True),
        sa.Column("xtransfer_low", sa.Float(), nullable=True),
        sa.Column("xtransfer_high", sa.Float(), nullable=True),
        sa.Column("xgross_salary_low", sa.Float(), nullable=True),
        sa.Column("xgross_salary_high", sa.Float(), nullable=True),
        sa.Column("xtransfer_next_high", sa.Float(), nullable=True),
        sa.Column("xtransfer_next_low", sa.Float(), nullable=True),
        sa.Column("xgross_salary_next_high", sa.Float(), nullable=True),
        sa.Column("xgross_salary_next_low", sa.Float(), nullable=True),
        sa.Column("rec_max_investment", sa.Float(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_player_records_created_at"),
        "player_records",
        ["created_at"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_player_records_created_by"),
        "player_records",
        ["created_by"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_player_records_last_updated"),
        "player_records",
        ["last_updated"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_player_records_playerId"),
        "player_records",
        ["playerId"],
        unique=False,
        schema="crm_dev",
    )
    op.create_table(
        "user_roles",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("name", sa.String(), nullable=True),
        sa.Column("description", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="crm_dev",
    )
    op.create_table(
        "purchases",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("organization_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("module_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("price", sa.Float(), nullable=True),
        sa.Column("active_until", sa.DateTime(), nullable=True),
        sa.Column("active", sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(
            ["module_id"],
            ["crm_dev.modules.id"],
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["crm_dev.organizations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_purchases_active_until"),
        "purchases",
        ["active_until"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_purchases_created_at"),
        "purchases",
        ["created_at"],
        unique=False,
        schema="crm_dev",
    )
    op.create_table(
        "reports",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("last_updated", sa.DateTime(), nullable=True),
        sa.Column("created_by", sa.String(), nullable=True),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("player_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("strengths", sa.String(), nullable=True),
        sa.Column("weaknesses", sa.String(), nullable=True),
        sa.Column("model_ranks", sa.String(), nullable=True),
        sa.Column("current_ability", sa.Integer(), nullable=True),
        sa.Column("lookalike", sa.String(), nullable=True),
        sa.Column("conclusion", sa.String(), nullable=True),
        sa.Column("report_type", sa.ARRAY(sa.String()), nullable=True),
        sa.ForeignKeyConstraint(
            ["player_id"],
            ["crm_dev.player_records.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_reports_created_at"),
        "reports",
        ["created_at"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_reports_created_by"),
        "reports",
        ["created_by"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_reports_last_updated"),
        "reports",
        ["last_updated"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_reports_player_id"),
        "reports",
        ["player_id"],
        unique=False,
        schema="crm_dev",
    )
    op.create_table(
        "team_requests",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("last_updated", sa.DateTime(), nullable=True),
        sa.Column("created_by", sa.String(), nullable=True),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("teamId", sa.Integer(), nullable=True),
        sa.Column("position", sa.String(), nullable=True),
        sa.Column("segment", sa.Integer(), nullable=True),
        sa.Column("foot", sa.String(), nullable=True),
        sa.Column("stage", sa.String(), nullable=True),
        sa.Column("max_age", sa.Integer(), nullable=True),
        sa.Column("max_value", sa.Integer(), nullable=True),
        sa.Column("max_net_salary", sa.Integer(), nullable=True),
        sa.Column("transfer_period", sa.String(), nullable=True),
        sa.Column("source_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("reason_for_outcome", sa.String(), nullable=True),
        sa.Column("description", sa.String(), nullable=True),
        sa.Column("type", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["source_id"],
            ["crm_dev.contacts.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_team_requests_created_at"),
        "team_requests",
        ["created_at"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_team_requests_created_by"),
        "team_requests",
        ["created_by"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_team_requests_last_updated"),
        "team_requests",
        ["last_updated"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_team_requests_teamId"),
        "team_requests",
        ["teamId"],
        unique=False,
        schema="crm_dev",
    )
    op.create_table(
        "user",
        sa.Column("email", sa.String(length=320), nullable=False),
        sa.Column("hashed_password", sa.String(length=1024), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("is_superuser", sa.Boolean(), nullable=False),
        sa.Column("is_verified", sa.Boolean(), nullable=False),
        sa.Column("id", GUID(), nullable=False),
        sa.Column("is_enabled", sa.Boolean(), nullable=True),
        sa.Column("organization_id", postgresql.UUID(), nullable=True),
        sa.Column("role_id", postgresql.UUID(), nullable=True),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["crm_dev.organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["crm_dev.user_roles.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_user_email"), "user", ["email"], unique=True, schema="crm_dev"
    )
    op.create_table(
        "proposals",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("last_updated", sa.DateTime(), nullable=True),
        sa.Column("created_by", sa.String(), nullable=True),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("player_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("request_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["player_id"],
            ["crm_dev.player_records.id"],
        ),
        sa.ForeignKeyConstraint(
            ["request_id"],
            ["crm_dev.team_requests.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_proposals_created_at"),
        "proposals",
        ["created_at"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_proposals_created_by"),
        "proposals",
        ["created_by"],
        unique=False,
        schema="crm_dev",
    )
    op.create_index(
        op.f("ix_crm_dev_proposals_last_updated"),
        "proposals",
        ["last_updated"],
        unique=False,
        schema="crm_dev",
    )
    op.drop_table("player_report_inputs")
    op.drop_table("uefa_club_ratings")
    op.drop_table("bundesliga_speeds")
    op.drop_table("migration_status")
    op.drop_table("uefa_speed_measurements")
    op.drop_index("player_model_speeds_player", table_name="player_model_speeds")
    op.drop_table("player_model_speeds")
    op.drop_table("player_speed_rating")
    op.drop_table("ws_tm_unmatchable_players")
    op.drop_table("sorare_tweet_thresholds")
    op.drop_table("unmatched_tm_players")
    op.drop_table("manually_matched_teams")
    op.drop_table("player_role_train_set")
    op.drop_table("manually_matched_players")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "manually_matched_players",
        sa.Column("playerId", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column("tm_player_id", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column("tm_player_url", sa.VARCHAR(), autoincrement=False, nullable=True),
    )
    op.create_table(
        "player_role_train_set",
        *[
            sa.Column("playerId", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("matchId", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("teamId", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("minutesTagged", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column(
                "shots_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_outside_penalty",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_inside_penalty",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "left_foot_shots",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "right_foot_shots",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_from_the_wing",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_cross",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_set_piece",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_inplay_cross",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_through_pass",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shot_assists_penalty_area",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "inplay_shot_assists",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "set_piece_shot_assists",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "second_shot_assist",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "third_shot_assist",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "ground_forward_passes_final_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "forward_ground_short_passes_opposition_half",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_stopped_progress",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_own_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_midfield",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_final_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_own_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_midfield",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_final_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_central_own_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_wing_own_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_after_throw_in",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "gk_free_kick_saves",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "gk_penalty_saves",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "gk_saves_inplay_inside_penalty",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "gk_saves_inplay_outside_penalty",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "gk_bad_saves",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "interceptions",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "interception_accurate_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "duel_fouls",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "missed_penalties",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "inplay_ground_interceptions",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "inplay_midfield_ground_interceptions",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "inplay_final_third_ground_interceptions",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "fouls_leading_to_direct_goal",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "active_recoveries",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "positional_recoveries",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "non_clearance_interceptions",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "actions_under_pressure",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_actions_under_pressure",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_recoveries",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "ground_recoveries",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "reactions_after_loss",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_dribbles",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "dribbles_no_foul",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_dribbles_no_foul",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "blocked_crosses",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "offensive_duels_progressed_with_ball",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "offensive_duels_kept_possession",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "opponent_half_recoveries",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_aerial_backward_pass",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_aerial_lateral_pass",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_aerial_diagonal_passes_opposition_half",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_aerial_diagonal_passes_final_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_aerial_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_aerial_passes_not_clearance",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "back_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "head_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "lateral_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "forward_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "linkup_plays",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "progressive_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "through_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "passes_to_final_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "passes_to_penalty_area",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "crosses",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "ground_duels",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "loose_ball_duels",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "offensive_duels",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "dribbles",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "dribbled_past_attempts",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "free_kick_crosses",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "free_kick_shots",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "counterpressing_recoveries",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "fouls",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "fouls_suffered",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "recoveries",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_block",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "head_shots",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_corner",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_free_kick",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_throw_in",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "assists",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "second_assists",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shot_assists",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "third_assists",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "losses",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "progressive_runs",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "accelerations",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "free_kicks",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "corners",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_ground_forward_passes_final_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_forward_ground_short_passes_opposition_half",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_back_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_head_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_lateral_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_forward_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_progressive_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_through_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_passes_to_final_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_passes_to_penalty_area",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_crosses",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_free_kick_crosses",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_outside_penalty_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_inside_penalty_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "left_foot_shots_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "right_foot_shots_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_from_the_wing_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_cross_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_set_piece_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_inplay_cross_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_through_pass_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "free_kick_shots_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "head_shots_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_corner_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_free_kick_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_throw_in_on_target",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_own_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_midfield",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_final_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_central_own_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_wing_own_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_after_throw_in",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_won",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_won_own_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_won_midfield",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_won_final_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_aerial_backward_pass",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_aerial_lateral_pass",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_aerial_diagonal_passes_opposition_half",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_aerial_diagonal_passes_final_third",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_aerial_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_aerial_passes_not_clearance",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "mean_xg",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "sum_xg",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shot_xg",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "assist_xg",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.1_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.1_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.1_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.2_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.2_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.2_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.3_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.3_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.3_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.4_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.4_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.4_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.5_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.5_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.5_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.6_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.6_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.6_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.7_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.7_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.7_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.8_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.8_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.8_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.9_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.9_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.9_quantile",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_outside_penalty_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_inside_penalty_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "left_foot_shots_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "right_foot_shots_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_from_the_wing_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_cross_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_set_piece_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_inplay_cross_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_through_pass_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shot_assists_penalty_area_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "inplay_shot_assists_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "set_piece_shot_assists_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "second_shot_assist_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "third_shot_assist_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "ground_forward_passes_final_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "forward_ground_short_passes_opposition_half_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_stopped_progress_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_own_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_midfield_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_final_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_own_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_midfield_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_final_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_central_own_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_wing_own_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_after_throw_in_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "gk_free_kick_saves_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "gk_penalty_saves_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "gk_saves_inplay_inside_penalty_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "gk_saves_inplay_outside_penalty_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "gk_bad_saves_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "interceptions_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "interception_accurate_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "duel_fouls_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "missed_penalties_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "inplay_ground_interceptions_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "inplay_midfield_ground_interceptions_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "inplay_final_third_ground_interceptions_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "fouls_leading_to_direct_goal_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "active_recoveries_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "positional_recoveries_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "non_clearance_interceptions_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "actions_under_pressure_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_actions_under_pressure_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_recoveries_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "ground_recoveries_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "reactions_after_loss_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_dribbles_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "dribbles_no_foul_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_dribbles_no_foul_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "blocked_crosses_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "offensive_duels_progressed_with_ball_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "offensive_duels_kept_possession_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "opponent_half_recoveries_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_aerial_backward_pass_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_aerial_lateral_pass_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_aerial_diagonal_passes_opposition_half_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_aerial_diagonal_passes_final_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_aerial_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_aerial_passes_not_clearance_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "back_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "head_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "lateral_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "forward_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "linkup_plays_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "progressive_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "through_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "passes_to_final_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "passes_to_penalty_area_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "long_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "crosses_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "ground_duels_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "loose_ball_duels_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "offensive_duels_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "dribbles_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "dribbled_past_attempts_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "free_kick_crosses_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "free_kick_shots_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "counterpressing_recoveries_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "fouls_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "fouls_suffered_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "recoveries_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_block_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "head_shots_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_corner_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_free_kick_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_throw_in_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "assists_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "second_assists_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shot_assists_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "third_assists_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "losses_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "progressive_runs_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "accelerations_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "free_kicks_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "corners_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_ground_forward_passes_final_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_forward_ground_short_passes_opposition_half_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_back_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_head_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_lateral_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_forward_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_progressive_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_through_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_passes_to_final_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_passes_to_penalty_area_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_crosses_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_free_kick_crosses_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_outside_penalty_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_inside_penalty_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "left_foot_shots_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "right_foot_shots_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_from_the_wing_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_cross_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_set_piece_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_inplay_cross_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_through_pass_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "free_kick_shots_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "head_shots_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_corner_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_free_kick_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_after_throw_in_on_target_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_own_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_midfield_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_final_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_central_own_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_wing_own_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "defensive_duels_won_after_throw_in_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_won_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_won_own_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_won_midfield_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "aerial_duels_won_final_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_aerial_backward_pass_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_aerial_lateral_pass_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_aerial_diagonal_passes_opposition_half_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_aerial_diagonal_passes_final_third_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_aerial_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "successful_long_aerial_passes_not_clearance_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "mean_xg_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "sum_xg_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shot_xg_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "assist_xg_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.1_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.1_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.1_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.2_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.2_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.2_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.3_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.3_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.3_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.4_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.4_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.4_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.5_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.5_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.5_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.6_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.6_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.6_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.7_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.7_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.7_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.8_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.8_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.8_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_x_0.9_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "location_y_0.9_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "centrality_y_0.9_quantile_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "air_duel_to_passes",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "def_duels_to_crosses",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "looseDuels_intercepts_clears",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shot_assists_to_progruns",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "intercepts_forwardPasses",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "crosses_to_shots",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "dribbles_to_shots",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shotAssists_defensiveDuels",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "longPasses_shotAssists",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "interceptions_to_shotAssists",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_to_forwardPasses",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_shot_Assists_to_forwardPasses",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "air_duel_to_passes_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "def_duels_to_crosses_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "looseDuels_intercepts_clears_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shot_assists_to_progruns_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "intercepts_forwardPasses_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "crosses_to_shots_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "dribbles_to_shots_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shotAssists_defensiveDuels_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "longPasses_shotAssists_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "interceptions_to_shotAssists_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_to_forwardPasses_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column(
                "shots_shot_Assists_to_forwardPasses_scaled",
                postgresql.DOUBLE_PRECISION(precision=53),
                autoincrement=False,
                nullable=True,
            ),
            sa.Column("weight_40_68", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("weight_68_72", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("weight_72_76", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("weight_76_80", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("weight_80_120", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("weight_nan", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column(
                "height_130_174", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "height_174_178", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "height_178_182", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "height_182_186", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "height_186_210", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column("height_nan", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("role_code2_DF", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("role_code2_FW", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("role_code2_MD", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column(
                "form_3_2_3_2_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_3_2_3_2_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_3_3_3_1_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_3_3_3_1_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column("form_3_4_1_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_3_4_1_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column(
                "form_3_4_1_1_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_3_4_1_1_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_3_4_1_2_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_3_4_1_2_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column("form_3_4_2_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_3_4_2_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column(
                "form_3_4_2_1_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_3_4_2_1_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column("form_3_4_3_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_3_4_3_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_3_5_1_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_3_5_1_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column(
                "form_3_5_1_1_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_3_5_1_1_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column("form_3_5_2_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_3_5_2_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column(
                "form_4_1_3_1_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_1_3_1_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_1_3_2_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_1_3_2_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_1_4_1_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_1_4_1_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_2_1_2_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_2_1_2_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_2_1_3_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_2_1_3_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_2_2_0_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_2_2_0_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_2_2_1_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_2_2_1_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_2_2_2_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_2_2_2_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_2_3_1_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_2_3_1_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column("form_4_3_1_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_4_3_1_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column(
                "form_4_3_1_1_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_3_1_1_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_3_1_2_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_3_1_2_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column("form_4_3_2_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_4_3_2_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column(
                "form_4_3_2_1_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_3_2_1_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column("form_4_3_3_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_4_3_3_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_4_4_0_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_4_4_0_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_4_4_1_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_4_4_1_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column(
                "form_4_4_1_1_0", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column(
                "form_4_4_1_1_1", sa.BIGINT(), autoincrement=False, nullable=True
            ),
            sa.Column("form_4_4_2_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_4_4_2_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_4_5_1_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_4_5_1_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_5_3_0_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_5_3_0_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_5_3_1_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_5_3_1_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_5_3_2_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_5_3_2_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_5_4_1_0", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("form_5_4_1_1", sa.BIGINT(), autoincrement=False, nullable=True),
            sa.Column("role", sa.TEXT(), autoincrement=False, nullable=True),
        ]
    )
    op.create_table(
        "manually_matched_teams",
        sa.Column(
            "transfermarkt_team_id", sa.BIGINT(), autoincrement=False, nullable=True
        ),
        sa.Column(
            "teamId",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.create_table(
        "unmatched_tm_players",
        sa.Column("playerId", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_table(
        "sorare_tweet_thresholds",
        sa.Column("time", postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column("player_id", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "estimated_tweets",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "percentile_90_threshold",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "percentile_95_threhsold",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "percentile_99_threshold",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "percentile_999_threshold",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.create_table(
        "ws_tm_unmatchable_players",
        sa.Column("playerId", sa.BIGINT(), autoincrement=False, nullable=True),
    )
    op.create_table(
        "player_speed_rating",
        sa.Column("playerId", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column(
            "median_speed",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("speed_count", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column("speed_rating", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_table(
        "player_model_speeds",
        sa.Column("index", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column("playerId", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column("matchId", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column("x_start", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column("y_start", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column(
            "x_end",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "y_end",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "matchPeriod",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "eventSec",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("subEventName", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "end_sec",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("next_event", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("prev_event", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "duration",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "distance",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "speed",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("date", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "model_speed",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.create_index(
        "player_model_speeds_player", "player_model_speeds", ["playerId"], unique=False
    )
    op.create_table(
        "uefa_speed_measurements",
        sa.Column("player_age", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column("player_birthDate", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("player_countryCode", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "player_detailedFieldPosition",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "player_fieldPosition", sa.TEXT(), autoincrement=False, nullable=True
        ),
        sa.Column("player_height", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column("player_id", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column("player_imageUrl", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "player_internationalName", sa.TEXT(), autoincrement=False, nullable=True
        ),
        sa.Column(
            "player_weight",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "statistics_minutes_played_official",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "statistics_matches_appearance",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "statistics_top_speed",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "statistics_distance_covered",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "team_associationId", sa.BIGINT(), autoincrement=False, nullable=True
        ),
        sa.Column(
            "team_associationLogoUrl", sa.TEXT(), autoincrement=False, nullable=True
        ),
        sa.Column("team_bigLogoUrl", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "team_confederationType", sa.TEXT(), autoincrement=False, nullable=True
        ),
        sa.Column("team_countryCode", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("team_id", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column("team_idProvider", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "team_internationalName", sa.TEXT(), autoincrement=False, nullable=True
        ),
        sa.Column(
            "team_isPlaceHolder", sa.BOOLEAN(), autoincrement=False, nullable=True
        ),
        sa.Column("team_logoUrl", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("team_mediumLogoUrl", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("team_teamCode", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("team_teamTypeDetail", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "team_typeIsNational", sa.BOOLEAN(), autoincrement=False, nullable=True
        ),
        sa.Column("team_typeTeam", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("playerId", sa.BIGINT(), autoincrement=False, nullable=True),
    )
    op.create_table(
        "migration_status",
        sa.Column("status", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_table(
        "bundesliga_speeds",
        sa.Column("first_name", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("last_name", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("team", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "speed",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.create_table(
        "uefa_club_ratings",
        sa.Column("Pos", sa.BIGINT(), autoincrement=False, nullable=True),
        sa.Column("Club", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("Country", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "15/16",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "16/17",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "17/18",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "18/19",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "19/20",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "Pts",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "NA",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "teamId",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.create_table(
        "player_report_inputs",
        sa.Column("playerId", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("date", sa.DATE(), autoincrement=False, nullable=True),
        sa.Column(
            "params",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "results",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "scaled_results",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.drop_index(
        op.f("ix_crm_dev_proposals_last_updated"),
        table_name="proposals",
        schema="crm_dev",
    )
    op.drop_index(
        op.f("ix_crm_dev_proposals_created_by"),
        table_name="proposals",
        schema="crm_dev",
    )
    op.drop_index(
        op.f("ix_crm_dev_proposals_created_at"),
        table_name="proposals",
        schema="crm_dev",
    )
    op.drop_table("proposals", schema="crm_dev")
    op.drop_index(op.f("ix_crm_dev_user_email"), table_name="user", schema="crm_dev")
    op.drop_table("user", schema="crm_dev")
    op.drop_index(
        op.f("ix_crm_dev_team_requests_teamId"),
        table_name="team_requests",
        schema="crm_dev",
    )
    op.drop_index(
        op.f("ix_crm_dev_team_requests_last_updated"),
        table_name="team_requests",
        schema="crm_dev",
    )
    op.drop_index(
        op.f("ix_crm_dev_team_requests_created_by"),
        table_name="team_requests",
        schema="crm_dev",
    )
    op.drop_index(
        op.f("ix_crm_dev_team_requests_created_at"),
        table_name="team_requests",
        schema="crm_dev",
    )
    op.drop_table("team_requests", schema="crm_dev")
    op.drop_index(
        op.f("ix_crm_dev_reports_player_id"), table_name="reports", schema="crm_dev"
    )
    op.drop_index(
        op.f("ix_crm_dev_reports_last_updated"), table_name="reports", schema="crm_dev"
    )
    op.drop_index(
        op.f("ix_crm_dev_reports_created_by"), table_name="reports", schema="crm_dev"
    )
    op.drop_index(
        op.f("ix_crm_dev_reports_created_at"), table_name="reports", schema="crm_dev"
    )
    op.drop_table("reports", schema="crm_dev")
    op.drop_index(
        op.f("ix_crm_dev_purchases_created_at"),
        table_name="purchases",
        schema="crm_dev",
    )
    op.drop_index(
        op.f("ix_crm_dev_purchases_active_until"),
        table_name="purchases",
        schema="crm_dev",
    )
    op.drop_table("purchases", schema="crm_dev")
    op.drop_table("user_roles", schema="crm_dev")
    op.drop_index(
        op.f("ix_crm_dev_player_records_playerId"),
        table_name="player_records",
        schema="crm_dev",
    )
    op.drop_index(
        op.f("ix_crm_dev_player_records_last_updated"),
        table_name="player_records",
        schema="crm_dev",
    )
    op.drop_index(
        op.f("ix_crm_dev_player_records_created_by"),
        table_name="player_records",
        schema="crm_dev",
    )
    op.drop_index(
        op.f("ix_crm_dev_player_records_created_at"),
        table_name="player_records",
        schema="crm_dev",
    )
    op.drop_table("player_records", schema="crm_dev")
    op.drop_index(
        op.f("ix_crm_dev_organizations_last_updated"),
        table_name="organizations",
        schema="crm_dev",
    )
    op.drop_index(
        op.f("ix_crm_dev_organizations_created_by"),
        table_name="organizations",
        schema="crm_dev",
    )
    op.drop_index(
        op.f("ix_crm_dev_organizations_created_at"),
        table_name="organizations",
        schema="crm_dev",
    )
    op.drop_table("organizations", schema="crm_dev")
    op.drop_table("modules", schema="crm_dev")
    op.drop_index(
        op.f("ix_crm_dev_contacts_last_updated"),
        table_name="contacts",
        schema="crm_dev",
    )
    op.drop_index(
        op.f("ix_crm_dev_contacts_created_by"), table_name="contacts", schema="crm_dev"
    )
    op.drop_index(
        op.f("ix_crm_dev_contacts_created_at"), table_name="contacts", schema="crm_dev"
    )
    op.drop_table("contacts", schema="crm_dev")
    # ### end Alembic commands ###