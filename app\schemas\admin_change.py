from datetime import datetime
import uuid
from typing import Any, Optional
from pydantic import BaseModel, Field

from app.schemas.user import UserShort


class AdminChangeBase(BaseModel):
    action_type: str
    target_type: str
    target_id: uuid.UUID
    details: Optional[str] = None


class AdminChangeCreate(AdminChangeBase):
    pass


class AdminChangeUpdate(AdminChangeBase):
    pass


class AdminChange(AdminChangeBase):
    id: uuid.UUID
    edit_at: datetime
    edit_by: uuid.UUID
    editor: UserShort

    class Config:
        orm_mode = True
