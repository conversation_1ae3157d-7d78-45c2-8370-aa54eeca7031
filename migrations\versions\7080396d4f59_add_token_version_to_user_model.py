"""Add token_version to user model

Revision ID: 7080396d4f59
Revises: 806b8ecbe96c
Create Date: 2025-05-12 15:03:05.272064

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7080396d4f59'
down_revision = '806b8ecbe96c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('token_version', sa.Integer(), nullable=False, server_default='0'), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'token_version',server_default=None, schema='crm_test')
    # ### end Alembic commands ###