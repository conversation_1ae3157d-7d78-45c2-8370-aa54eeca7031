"""Message subscriptions for firebase

Revision ID: b103131910d6
Revises: f6b5b75ead32
Create Date: 2024-02-20 13:56:07.446217

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b103131910d6'
down_revision = 'f6b5b75ead32'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('message_subscription',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('token', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('approved', sa.<PERSON>(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['crm_test.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_message_subscription_created_at'), 'message_subscription', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_message_subscription_user_id'), 'message_subscription', ['user_id'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_test_message_subscription_user_id'), table_name='message_subscription', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_message_subscription_created_at'), table_name='message_subscription', schema='crm_test')
    op.drop_table('message_subscription', schema='crm_test')
    # ### end Alembic commands ###