from app.config import settings
from google.oauth2 import service_account
from google.auth.transport.requests import Request

def get_access_token():
    # Path to the service account key file
    service_account_file = settings.GOOGLE_APPLICATION_CREDENTIALS

    # Define the scope for the OAuth2 request
    scopes = ["https://www.googleapis.com/auth/firebase.messaging"]

    # Load the credentials from the service account file
    credentials = service_account.Credentials.from_service_account_file(
        service_account_file,
        scopes=scopes
    )

    # Request a new access token
    credentials.refresh(Request())

    return credentials.token