import uuid
from fastapi.encoders import jsonable_encoder
from typing import Any, Dict, List, Optional, Type, TypeVar, Union
from pydantic import BaseModel
from sqlalchemy.orm import Session, lazyload, Load, selectinload
from app.crud.crud_base_w_changelog import CRUDBaseWChangelog
from app import models
from app.db.base_class import Base
from app.models import User
from app.utils import compare_version_of_objects
import json
from app.utils import control_stages
from sqlalchemy import text
from datetime import datetime

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)
ChangeType = TypeVar("ChangeType", bound=Base)


class CRUDPlayerRecord(CRUDBaseWChangelog):
    def get_all_with_filters(
        self, db: Session, org_id: str, can_access_sensitive: bool, filters: dict
    ) -> List[ModelType]:
        # Base query
        query = (
            db.query(self.model)
            .options(
                selectinload(self.model.player_info),
                selectinload(self.model.organization),
                selectinload(self.model.creator),
                selectinload(self.model.assigned_to_record).selectinload(
                    models.AssignedToRecord.contact
                ),
            )
            .filter(self.model.organization_id == org_id)
        )

        # Apply sensitive data filter
        if not can_access_sensitive:
            query = query.filter(self.model.is_sensitive != True)

            # Apply control_stage filter (if provided)
        if filters.get("control_stage"):
            query = query.filter(self.model.control_stage.in_(filters["control_stage"]))

        if "active" in filters and filters["active"] is not None:
            if filters["active"]:
                query = query.filter(self.model.control_stage.in_(control_stages))
            else:
                query = query.filter(self.model.control_stage == "closed")

        # Order by last_updated
        query = query.order_by(self.model.last_updated.desc())

        return query.all()

    def create_with_user(
        self, db: Session, *, obj_in: CreateSchemaType, user: User
    ) -> ModelType:
        obj_in_data = jsonable_encoder(obj_in)
        assigned_in_data = obj_in_data.pop("assigned_to_record")

        ll_assignees = [
            models.AssignedToRecord(contact_id=con, id=uuid.uuid4())
            for con in assigned_in_data
        ]
        db_obj = self.model(
            **obj_in_data,
            created_by=user.id,
            organization_id=user.organization_id,
            assigned_to_record=ll_assignees,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update_with_changelog(
        self,
        db: Session,
        *,
        record_id: str,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
        change_model: Type[ChangeType],
        record_type: str,
        user: User,
    ) -> ModelType:
        # we pop out player_features and compute changes separately:
        obj_in_data = jsonable_encoder(obj_in)
        if obj_in_data["assigned_to_record"]:
            source_in_data = obj_in_data.pop("assigned_to_record")
            obj_in_data["assigned_to_record"] = [
                models.AssignedToRecord(contact_id=con, id=uuid.uuid4())
                for con in source_in_data
            ]
        else:
            source_in_data = []

        changes = []
        if not obj_in_data["assigned_to_record"] and db_obj.assigned_to_record is None:
            changes = compare_version_of_objects(
                db_obj,
                obj_in_data,
                user.id,
            )
        else:
            changes = []
        print(changes)
        ll_sources = [
            models.AssignedToRecord(contact_id=con, id=uuid.uuid4())
            for con in source_in_data
        ]
        db_obj.assigned_to_record = ll_sources

        for field, value in obj_in_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        for c in changes:
            c[f"{record_type}_id"] = record_id
            # print(c)
            if c["field"] == "source_to_record":
                # print(c)
                try:
                    c["previous"] = json.dumps(c["previous"])
                except:
                    c["previous"] = ""

                try:
                    c["updated"] = json.dumps(c["updated"])
                except:
                    c["updated"] = ""

            db.add(change_model(**c))
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update_selective_with_changelog(
        self,
        db: Session,
        *,
        record_id: str,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
        change_model: Type[ChangeType],
        record_type: str,
        user: User,
        provided_fields: Optional[List[str]] = None,
    ) -> ModelType:
        """
        Selective update method that only updates explicitly provided fields.

        Args:
            provided_fields: List of field names that were explicitly provided in the request.
                           If None, will attempt to determine from obj_in if it's a Pydantic model.

        Behavior:
        - Only updates fields that are explicitly provided
        - If a field is not provided, it remains unchanged
        - If a field is provided with null value, it will be set to null/empty
        """
        # Convert input to dictionary
        if isinstance(obj_in, dict):
            obj_in_data = obj_in.copy()
            # If provided_fields not specified and we have a dict, assume all keys were provided
            if provided_fields is None:
                provided_fields = list(obj_in_data.keys())
        else:
            # For Pydantic models, get only the fields that were explicitly set
            obj_in_data = jsonable_encoder(obj_in)
            if provided_fields is None:
                # Try to get fields that were explicitly set in the Pydantic model
                if hasattr(obj_in, "__fields_set__"):
                    provided_fields = list(obj_in.__fields_set__)
                else:
                    # Fallback: assume all non-None fields were provided
                    provided_fields = [
                        k for k, v in obj_in_data.items() if v is not None
                    ]

        # Handle assigned_to_record specially if it's in provided fields
        source_in_data = []
        if "assigned_to_record" in provided_fields:
            if obj_in_data.get("assigned_to_record"):
                source_in_data = obj_in_data.pop("assigned_to_record")
                obj_in_data["assigned_to_record"] = [
                    models.AssignedToRecord(contact_id=con, id=uuid.uuid4())
                    for con in source_in_data
                ]
            else:
                source_in_data = []
                obj_in_data["assigned_to_record"] = []

        # Filter obj_in_data to only include provided fields
        filtered_data = {k: v for k, v in obj_in_data.items() if k in provided_fields}

        # Calculate changes only for the fields that are being updated
        changes = compare_version_of_objects(
            db_obj,
            filtered_data,
            user.id,
        )

        # Handle assigned_to_record assignment
        if "assigned_to_record" in provided_fields:
            ll_sources = [
                models.AssignedToRecord(contact_id=con, id=uuid.uuid4())
                for con in source_in_data
            ]
            db_obj.assigned_to_record = ll_sources

        # Update only the provided fields
        for field, value in filtered_data.items():
            if (
                field != "assigned_to_record"
            ):  # Skip assigned_to_record as it's handled above
                setattr(db_obj, field, value)

        db_obj.last_updated = datetime.now()

        db.add(db_obj)

        # Add changelog entries
        for c in changes:
            c[f"{record_type}_id"] = record_id
            # Handle complex fields that need JSON serialization
            if c["field"] in ["source_to_record", "assigned_to_record"]:
                try:
                    c["previous"] = json.dumps(c["previous"])
                except:
                    c["previous"] = ""
                try:
                    c["updated"] = json.dumps(c["updated"])
                except:
                    c["updated"] = ""
            db.add(change_model(**c))

        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_with_wyscout(self, db: Session, wyId: Any, org_id) -> Optional[ModelType]:
        return (
            db.query(self.model)
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
            .filter(self.model.playerId == wyId, self.model.organization_id == org_id)
            .first()
        )

    def get_minutes_played(self, db: Session, wyId: Any) -> Optional[int]:
        query = text(
            f'select * from wyscout.player_year_minutes psm where "playerId" = :wyId'
        )
        result = db.execute(query, {"wyId": wyId})
        return result.mappings().first()

    def get_stats_for_playerId(self, db: Session, playerId: int):
        query = text(
            """
            SELECT
                SUM(goals) AS goals,
                COUNT("matchId") AS appearances,
                pym.total_mins_in_year,
                as2."playerId"
            FROM wyscout.advanced_stats as2
            JOIN wyscout.player_year_minutes pym ON pym."playerId" = as2."playerId"
            WHERE as2."playerId" = :playerId
            GROUP BY as2."playerId", pym.total_mins_in_year
        """
        )
        resultproxy = db.execute(query, {"playerId": playerId})
        return resultproxy.mappings().first()

    def get_transfers_for_playerId(
        self, db: Session, playerId: int
    ) -> Optional[List[ModelType]]:
        query = """
        SELECT "fromTeamName", "toTeamName", "startDate" AS date, type
        FROM wyscout.transfers t
        WHERE "playerId" = :playerId
        ORDER BY date DESC
        """
        result = db.execute(text(query), {"playerId": playerId})
        return result.mappings().all()

    def get_wyscout_player_data_playerId(self, db: Session, playerId: int):
        query = text(
            """
            SELECT *
            FROM wyscout.player_info2
            WHERE "playerId" = :playerId
        """
        )
        return db.execute(query, {"playerId": playerId}).mappings().first()

    def get_wyscout_player_data_tmId(self, db: Session, tm_player_id: int):
        query = text(
            """
            SELECT pi2.*
            FROM wyscout.player_info2 pi2
            JOIN transfermarkt.tm_to_ws_ids ttwi ON ttwi."playerId" = pi2."playerId"
            WHERE ttwi.tm_player_id = :tm_player_id
        """
        )
        return db.execute(query, {"tm_player_id": tm_player_id}).mappings().first()

    # def get_all_w_suitability(self, db: Session, org_id, can_access_sensitive, teamId) -> Optional[int]:
    #     # return self.get_all_w_org(db, org_id, can_access_sensitive)
    #     filter_cond = self.model.is_sensitive != True if not can_access_sensitive else True
    #     return db.query(self.model, SuitScores).join(SuitScores, self.model.playerId==SuitScores.playerId).options(
    #         Load(self.model).selectinload("*"),
    #         lazyload("*"),
    #         ).filter(SuitScores.hiring_team_id == teamId,self.model.organization_id == org_id, filter_cond).all()


player_record = CRUDPlayerRecord(models.PlayerRecord)
