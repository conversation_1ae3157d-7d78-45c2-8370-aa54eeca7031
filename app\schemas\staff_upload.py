import uuid
from typing import TYPE_CHECKING
from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)
if TYPE_CHECKING:
    from app.schemas.staff_record import StaffRecord


class StaffUploadUpdate(ExtendedUpdateBase):
    ...


class StaffUploadCreate(ExtendedCreateBase):
    staff_id: uuid.UUID
    id: str
    name: str

class StaffUpload(StaffUploadCreate, ExtendedBase):
    ...

    class Config:
        orm_mode = True
        use_cache = True
