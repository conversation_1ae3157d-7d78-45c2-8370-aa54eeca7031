from fastapi.testclient import TestClient
import json
from app.main import app
from app.models import organization
from app.testing.fixtures import (switch_between_users,
 client_chestnite,
  client_enskai,
  get_current_user_enskai_owner,
  get_current_user_chesnite_limited, get_test_org, get_test_user_email, get_test_user_password, rank_params)

from app.schemas.rank_record import RankRecordShort
from app.utils.base import create_org, create_user, delete_org, delete_user, define_user, delete_ranks
from app.config import settings
from app.api.endpoints.users.auth import current_active_user as get_current_active_user

import pytest
import uuid
from sqlalchemy import create_engine

client = TestClient(app)
engine = create_engine(settings.PG_URL)


def test_authorized_access_to_quality_endpoint(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]
    test_ord_auth = test_org + '_auth'

    delete_org(name=test_ord_auth)
    
    org_id = create_org(test_ord_auth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/cutoff_filters""")
    delete_org(name=test_ord_auth)
    

    assert response.status_code == 200

def test_unauthorized_access_to_quality_endpoint(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["gbe"]
    test_ord_unauth = test_org + '_unauth'

    delete_org(name=test_ord_unauth)
    
    org_id = create_org(test_ord_unauth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)
    
    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)  

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/cutoff_filters""")
    delete_org(name=test_ord_unauth)
    

    assert response.status_code == 403


def test_rank_output_structure(test_org, test_user, test_pass, rank_params):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]

    delete_org(name=test_org)
    
    org_id = create_org(test_org, "temporary_pass", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield

    response = client.put("""/quality/ranks""", data = rank_params)


    delete_ranks(id=json.loads(response.json())['rank_record_id'])
    delete_org(name=test_org)

    assert response.status_code == 200
    assert list(json.loads(response.json()).keys()) == ['rank_record_id']

def test_automated_quality_valid_request(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]
    test_ord_auth = test_org + '_auth'

    delete_org(name=test_ord_auth)
    
    org_id = create_org(test_ord_auth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/average_quality""", params={'playerId':15, 'player_role':'CAM', 'last_n_averages':1})
    delete_org(name=test_ord_auth)
    

    assert response.status_code == 200   
    assert isinstance(response.json(), list)
    assert len(response.json()) == 1

def test_automated_quality_valid_request_no_data(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]
    test_ord_auth = test_org + '_auth'

    delete_org(name=test_ord_auth)
    
    org_id = create_org(test_ord_auth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/average_quality""", params={'playerId':15, 'player_role':'CB', 'last_n_averages':1})
    delete_org(name=test_ord_auth)
    

    assert response.status_code == 200   
    assert isinstance(response.json(), list)
    assert len(response.json()) == 0

def test_chart_factory_vars(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]
    test_ord_auth = test_org + '_auth'

    delete_org(name=test_ord_auth)
    
    org_id = create_org(test_ord_auth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/chart_factory_vars""")
    delete_org(name=test_ord_auth)
    

    assert response.status_code == 200   
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0

def test_comparable_positions(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]
    test_ord_auth = test_org + '_auth'

    delete_org(name=test_ord_auth)
    
    org_id = create_org(test_ord_auth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/comparable_positions""")
    comp_positions = response.json()
    delete_org(name=test_ord_auth)
    

    assert response.status_code == 200   
    assert isinstance(comp_positions, list)
    assert len(comp_positions) > 0
    assert comp_positions[0]['main_role'] is not None
    assert isinstance(json.loads(comp_positions[0]['comparable_positions']), list)

def test_cutoff_filters(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]
    test_ord_auth = test_org + '_auth'

    delete_org(name=test_ord_auth)
    
    org_id = create_org(test_ord_auth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/cutoff_filters""")
    cutoff_filts = response.json()
    delete_org(name=test_ord_auth)
    

    assert response.status_code == 200   
    assert isinstance(cutoff_filts, dict)
    assert len(set(cutoff_filts.keys()).difference(set(['time_interval',
    'relevant_competitions',
    'min_num_games',
    'min_tagged',
    'min_pct',
    'minute_scaling_log_base',
    'competition_scaling',
    'difficulty_scaling',
    'quality_scaling',
    'activity_vs_accuracy',
    'time_decay_coef']))) == 0

    
def test_def_weights(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]
    test_ord_auth = test_org + '_auth'

    delete_org(name=test_ord_auth)
    
    org_id = create_org(test_ord_auth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/default_weights""")
    def_weights = response.json()
    delete_org(name=test_ord_auth)
    

    assert response.status_code == 200   
    assert isinstance(def_weights, list)
    assert len(def_weights) > 0
    assert def_weights[0]['main_role'] is not None
    assert isinstance(json.loads(def_weights[0]['weights']), dict)

def test_variable_definitions(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]
    test_ord_auth = test_org + '_auth'

    delete_org(name=test_ord_auth)
    
    org_id = create_org(test_ord_auth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/variable_definitions""")
    var_defs = response.json()
    delete_org(name=test_ord_auth)
    

    assert response.status_code == 200   
    assert isinstance(var_defs, list)
    assert len(var_defs) > 0
    assert var_defs[0]['variable_name'] is not None
    assert isinstance(json.loads(var_defs[0]['definition']), dict)

def test_area_filter_options(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]
    test_ord_auth = test_org + '_auth'

    delete_org(name=test_ord_auth)
    
    org_id = create_org(test_ord_auth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/filter_options/areas""")
    var_defs = response.json()
    delete_org(name=test_ord_auth)
    

    assert response.status_code == 200   
    assert isinstance(var_defs, list)
    assert len(var_defs) > 0

def test_role_filter_options(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]
    test_ord_auth = test_org + '_auth'

    delete_org(name=test_ord_auth)
    
    org_id = create_org(test_ord_auth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/filter_options/roles""")
    var_defs = response.json()
    delete_org(name=test_ord_auth)
    

    assert response.status_code == 200   
    assert isinstance(var_defs, list)
    assert len(var_defs) > 0

def test_agency_filter_options(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]
    test_ord_auth = test_org + '_auth'

    delete_org(name=test_ord_auth)
    
    org_id = create_org(test_ord_auth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/filter_options/agency""")
    var_defs = response.json()
    delete_org(name=test_ord_auth)
    

    assert response.status_code == 200   
    assert isinstance(var_defs, list)
    assert len(var_defs) > 0

def test_wrong_filter_options(test_org, test_user, test_pass):
    # Create a user with access to quality endpoints
    role = 'user_limited'
    org_id_original = uuid.uuid4()
    user_id = uuid.uuid4()
    modules = ["ranks"]
    test_ord_auth = test_org + '_auth'

    delete_org(name=test_ord_auth)
    
    org_id = create_org(test_ord_auth, "temporary org to test access to ranks", modules = modules, org_uuid=org_id_original)

    delete_user(email=test_user)
    email, user_id, role_id, role, org_id, is_enabled = create_user(test_user, test_pass, True, False, True, True, org_id, role, settings.PG_SCHEMA, user_id)

    current = define_user(email, user_id, role_id, org_id, role, modules)    

    def get_user_yield():
        yield current

    app.dependency_overrides[get_current_active_user] = get_user_yield
    response = client.get("""/quality/filter_options/wrong""")
    delete_org(name=test_ord_auth)
    

    assert response.status_code != 200  
    