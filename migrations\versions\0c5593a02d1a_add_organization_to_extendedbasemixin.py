"""add organization to extendedbasemixin~

Revision ID: 0c5593a02d1a
Revises: 7f10c3bf5467
Create Date: 2022-08-08 13:16:08.582450

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0c5593a02d1a'
down_revision = '7f10c3bf5467'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('proposals', sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_dev')
    op.create_foreign_key(None, 'proposals', 'organizations', ['organization_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.add_column('reports', sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_dev')
    op.create_foreign_key(None, 'reports', 'organizations', ['organization_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'reports', schema='crm_dev', type_='foreignkey')
    op.drop_column('reports', 'organization_id', schema='crm_dev')
    op.drop_constraint(None, 'proposals', schema='crm_dev', type_='foreignkey')
    op.drop_column('proposals', 'organization_id', schema='crm_dev')
    # ### end Alembic commands ###