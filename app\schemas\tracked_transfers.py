import uuid
from typing import Optional
from datetime import datetime
from pydantic import BaseModel


class TrackedTransferUpdate(BaseModel):
    request_id: Optional[uuid.UUID]
    position: Optional[str]
    player_name: Optional[str]
    left_name: Optional[str]
    fee: Optional[str]
    date: Optional[datetime]
    player_url: Optional[str]
    active: Optional[bool]

    class Config:
        orm_mode = True
        use_cache=True

class TrackedTransferCreate(TrackedTransferUpdate):
    pass
    
class TrackedTransfer(BaseModel):
    id: uuid .UUID
    request_id: uuid.UUID
    position: Optional[str] = None
    player_name: Optional[str] = None
    left_name: Optional[str] = None
    fee: Optional[str] = None
    date: datetime
    player_url: Optional[str] = None
    active: bool

    class Config:
        orm_mode = True
        use_cache=True
