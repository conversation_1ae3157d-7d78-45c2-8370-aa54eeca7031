"""New assigned_to_record + added/removed player fields

Revision ID: c5cb046e4bcd
Revises: d918de4c5689
Create Date: 2024-10-31 15:43:55.408126

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy
# revision identifiers, used by Alembic.
revision = 'c5cb046e4bcd'
down_revision = 'd918de4c5689'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('community_tokens',
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('tokens', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['crm.organizations.id'], ),
    sa.PrimaryKeyConstraint('organization_id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_community_tokens_organization_id'), 'community_tokens', ['organization_id'], unique=False, schema='crm')
    op.create_table('player_uploads',
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm.organizations.id'], ),
    sa.ForeignKeyConstraint(['player_id'], ['crm.player_records.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_player_uploads_created_at'), 'player_uploads', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_uploads_created_by'), 'player_uploads', ['created_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_uploads_is_sensitive'), 'player_uploads', ['is_sensitive'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_uploads_last_updated'), 'player_uploads', ['last_updated'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_uploads_organization_id'), 'player_uploads', ['organization_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_player_uploads_player_id'), 'player_uploads', ['player_id'], unique=False, schema='crm')
    op.create_table('assigned_to_record',
    sa.Column('contact_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('activity_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['activity_id'], ['crm.activity.id'], ),
    sa.ForeignKeyConstraint(['contact_id'], ['crm.contacts.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['player_id'], ['crm.player_records.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_assigned_to_record_activity_id'), 'assigned_to_record', ['activity_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_assigned_to_record_contact_id'), 'assigned_to_record', ['contact_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_assigned_to_record_player_id'), 'assigned_to_record', ['player_id'], unique=False, schema='crm')
    op.drop_index('ix_crm_activity_assigned_to_id', table_name='activity', schema='crm')
    op.drop_constraint('activity_assigned_to_id_fkey', 'activity', schema='crm', type_='foreignkey')
    op.drop_column('activity', 'assigned_to_id', schema='crm')
    op.add_column('comments_activity', sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm')
    op.create_index(op.f('ix_crm_comments_activity_player_id'), 'comments_activity', ['player_id'], unique=False, schema='crm')
    op.create_foreign_key(None, 'comments_activity', 'player_records', ['player_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.add_column('contract_uploads', sa.Column('name', sa.String(), nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('expected_net_salary', sa.Float(), nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('video_link', sa.String(), nullable=True), schema='crm')
    op.drop_index('ix_crm_player_records_assigned_to_id', table_name='player_records', schema='crm')
    op.drop_constraint('player_records_assigned_to_id_fkey', 'player_records', schema='crm', type_='foreignkey')
    op.drop_column('player_records', 'contact_channel', schema='crm')
    op.drop_column('player_records', 'rec_max_investment', schema='crm')
    op.drop_column('player_records', 'xgross_salary_next_low', schema='crm')
    op.drop_column('player_records', 'xgross_salary_next_high', schema='crm')
    op.drop_column('player_records', 'birth_date', schema='crm')
    op.drop_column('player_records', 'priority_player', schema='crm')
    op.drop_column('player_records', 'agency', schema='crm')
    op.drop_column('player_records', 'xgross_salary_low', schema='crm')
    op.drop_column('player_records', 'xtransfer_next_high', schema='crm')
    op.drop_column('player_records', 'xtransfer_low', schema='crm')
    op.drop_column('player_records', 'xtransfer_high', schema='crm')
    op.drop_column('player_records', 'phone_number', schema='crm')
    op.drop_column('player_records', 'xtransfer_next_low', schema='crm')
    op.drop_column('player_records', 'proactively_scouted', schema='crm')
    op.drop_column('player_records', 'assigned_to_id', schema='crm')
    op.drop_column('player_records', 'xgross_salary_high', schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('xgross_salary_high', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('assigned_to_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('proactively_scouted', sa.BOOLEAN(), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('xtransfer_next_low', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('phone_number', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('xtransfer_high', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('xtransfer_low', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('xtransfer_next_high', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('xgross_salary_low', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('agency', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('priority_player', sa.BOOLEAN(), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('birth_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('xgross_salary_next_high', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('xgross_salary_next_low', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('rec_max_investment', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm')
    op.add_column('player_records', sa.Column('contact_channel', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True), schema='crm')
    op.create_foreign_key('player_records_assigned_to_id_fkey', 'player_records', 'contacts', ['assigned_to_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_index('ix_crm_player_records_assigned_to_id', 'player_records', ['assigned_to_id'], unique=False, schema='crm')
    op.drop_column('player_records', 'video_link', schema='crm')
    op.drop_column('player_records', 'expected_net_salary', schema='crm')
    op.drop_column('contract_uploads', 'name', schema='crm')
    op.drop_constraint(None, 'comments_activity', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_comments_activity_player_id'), table_name='comments_activity', schema='crm')
    op.drop_column('comments_activity', 'player_id', schema='crm')
    op.add_column('activity', sa.Column('assigned_to_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm')
    op.create_foreign_key('activity_assigned_to_id_fkey', 'activity', 'contacts', ['assigned_to_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.create_index('ix_crm_activity_assigned_to_id', 'activity', ['assigned_to_id'], unique=False, schema='crm')
    op.drop_index(op.f('ix_crm_assigned_to_record_player_id'), table_name='assigned_to_record', schema='crm')
    op.drop_index(op.f('ix_crm_assigned_to_record_contact_id'), table_name='assigned_to_record', schema='crm')
    op.drop_index(op.f('ix_crm_assigned_to_record_activity_id'), table_name='assigned_to_record', schema='crm')
    op.drop_table('assigned_to_record', schema='crm')
    op.drop_index(op.f('ix_crm_player_uploads_player_id'), table_name='player_uploads', schema='crm')
    op.drop_index(op.f('ix_crm_player_uploads_organization_id'), table_name='player_uploads', schema='crm')
    op.drop_index(op.f('ix_crm_player_uploads_last_updated'), table_name='player_uploads', schema='crm')
    op.drop_index(op.f('ix_crm_player_uploads_is_sensitive'), table_name='player_uploads', schema='crm')
    op.drop_index(op.f('ix_crm_player_uploads_created_by'), table_name='player_uploads', schema='crm')
    op.drop_index(op.f('ix_crm_player_uploads_created_at'), table_name='player_uploads', schema='crm')
    op.drop_table('player_uploads', schema='crm')
    op.drop_index(op.f('ix_crm_community_tokens_organization_id'), table_name='community_tokens', schema='crm')
    op.drop_table('community_tokens', schema='crm')
    # ### end Alembic commands ###