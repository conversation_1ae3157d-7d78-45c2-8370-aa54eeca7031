"""Changed notes field of activity tracker to json

Revision ID: f94094d0d57e
Revises: 68112263b03f
Create Date: 2024-07-31 10:11:31.546171

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f94094d0d57e'
down_revision = '68112263b03f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('activity', 'notes',
               existing_type=sa.VARCHAR(),
               type_=sa.JSON(),
               existing_nullable=True,
               schema='crm_test',
               postgresql_using='notes::json')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('activity', 'notes',
               existing_type=sa.JSON(),
               type_=sa.VARCHAR(),
               existing_nullable=True,
               schema='crm_test',
               postgresql_using='notes::text')
    # ### end Alembic commands ###