from sqlalchemy import Column, Integer, Table
from app.db.session import engine
from app.db.base_class import Base


# class SuitScores(Base):
#     __table__ = Table(
#         "suit_scores",
#         Base.metadata,
#         Column("playerId", Integer, primary_key=True),
#         Column("hiring_team_id", Integer, primary_key=True),
#         autoload=True,
#         extend_existing=True,
#         autoload_with=engine,
#         schema="wyscout",
#         info=dict(is_view=True)
#     )

class CompTeamElos(Base):
    __table__ = Table(
        "competition_teams",
        Base.metadata,
        Column("competitionId", Integer, primary_key=True),
        Column("teamId", Integer, primary_key=True),
        extend_existing=True,
        autoload_with=engine,
        schema="wyscout",
        info=dict(is_view=True)
    )

class CompElos(Base):
    __table__ = Table(
        "competition_ratings",
        Base.metadata,
        Column("competitionId", Integer, primary_key=True),
        extend_existing=True,
        autoload_with=engine,
        schema="wyscout",
        info=dict(is_view=True)
    )

class PlayerYearMinutes(Base):
    __table__ = Table(
        "player_year_minutes",
        Base.metadata,
        Column("playerId", Integer, primary_key=True),
        extend_existing=True,
        autoload_with=engine,
        schema="wyscout",
        info=dict(is_view=True)
    )


# class MostSuitablePlayers(Base):
#     __table__ = Table(
#         "most_suitable_players",
#         Base.metadata,
#         Column("playerId", Integer, primary_key=True),
#         Column("hiring_team_id", Integer, primary_key=True),
#         autoload=True,
#         extend_existing=True,
#         autoload_with=engine,
#         schema="wyscout",
#         info=dict(is_view=True)
#     )
