"""Paid organization checkbox

Revision ID: 56da7566c072
Revises: 3e72d9c6348e
Create Date: 2025-03-25 17:07:32.808356

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '56da7566c072'
down_revision = '3e72d9c6348e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('paid', sa.<PERSON>(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organizations', 'paid', schema='crm_test')
    # ### end Alembic commands ###