"""New contract type and other legal fixes - master

Revision ID: 29d5d5ea9521
Revises: 962f472bbf02
Create Date: 2024-06-20 15:33:15.114972

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '29d5d5ea9521'
down_revision = '962f472bbf02'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('activity',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('playerId', sa.Integer(), nullable=True),
    sa.Column('teamId', sa.Integer(), nullable=True),
    sa.<PERSON>umn('assigned_to_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('stage', sa.String(), nullable=True),
    sa.Column('staff', sa.String(), nullable=True),
    sa.Column('next_action', sa.DateTime(), nullable=True),
    sa.Column('updated_by_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('due_date', sa.DateTime(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['assigned_to_id'], ['crm.contacts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['crm.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm.organizations.id'], ),
    #sa.ForeignKeyConstraint(['playerId'], ['wyscout.player_info2.playerId'], ),
    #sa.ForeignKeyConstraint(['teamId'], ['wyscout.team_info2.teamId'], ),
    sa.ForeignKeyConstraint(['updated_by_id'], ['crm.contacts.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_activity_assigned_to_id'), 'activity', ['assigned_to_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_activity_created_at'), 'activity', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_activity_created_by'), 'activity', ['created_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_activity_is_sensitive'), 'activity', ['is_sensitive'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_activity_last_updated'), 'activity', ['last_updated'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_activity_organization_id'), 'activity', ['organization_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_activity_playerId'), 'activity', ['playerId'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_activity_teamId'), 'activity', ['teamId'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_activity_updated_by_id'), 'activity', ['updated_by_id'], unique=False, schema='crm')
    op.add_column('contracts', sa.Column('notify', postgresql.ARRAY(sa.String()), nullable=True), schema='crm')
    op.add_column('contracts', sa.Column('company', sa.String(), nullable=True), schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('contracts', 'company', schema='crm')
    op.drop_column('contracts', 'notify', schema='crm')
    op.drop_index(op.f('ix_crm_activity_updated_by_id'), table_name='activity', schema='crm')
    op.drop_index(op.f('ix_crm_activity_teamId'), table_name='activity', schema='crm')
    op.drop_index(op.f('ix_crm_activity_playerId'), table_name='activity', schema='crm')
    op.drop_index(op.f('ix_crm_activity_organization_id'), table_name='activity', schema='crm')
    op.drop_index(op.f('ix_crm_activity_last_updated'), table_name='activity', schema='crm')
    op.drop_index(op.f('ix_crm_activity_is_sensitive'), table_name='activity', schema='crm')
    op.drop_index(op.f('ix_crm_activity_created_by'), table_name='activity', schema='crm')
    op.drop_index(op.f('ix_crm_activity_created_at'), table_name='activity', schema='crm')
    op.drop_index(op.f('ix_crm_activity_assigned_to_id'), table_name='activity', schema='crm')
    op.drop_table('activity', schema='crm')
    # ### end Alembic commands ###