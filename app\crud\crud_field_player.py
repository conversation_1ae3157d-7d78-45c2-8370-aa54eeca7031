from app.crud.crud_base import CRUDBase
from app import models, crud
from app.schemas.field_players import FieldPlayersCreate, FieldPlayersUpdate
from sqlalchemy.orm import Session
from fastapi.encoders import jsonable_encoder


class CRUDFieldPlayers(
    CRUDBase[models.FieldPlayers, FieldPlayersCreate, FieldPlayersUpdate]
):
    def create_player_w_suitability(self, db: Session, obj_in: FieldPlayersCreate):
        if obj_in.teamId:
            player_suitability = crud.suit_score_joined.get_player_team_suitability(db, team_id = obj_in.teamId, player_id = obj_in.playerId)
            obj_in.suitability_score = player_suitability[0][1]
        else:
            obj_in.suitability_score = -1
        obj_in_data = jsonable_encoder(obj_in)
        _ = obj_in_data.pop('teamId')
        db_obj = self.model(
            **obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def delete_all_players(self, db: Session, field_id: str):
        db.query(models.FieldPlayers).filter(models.FieldPlayers.field_id == field_id).delete(synchronize_session=False)
        db.commit()
        return True


field_player = CRUDFieldPlayers(models.FieldPlayers)
