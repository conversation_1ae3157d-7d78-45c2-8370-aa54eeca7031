"""Create sherable token

Revision ID: 9a30e5c5f6fd
Revises: d767e79ccb6e
Create Date: 2025-03-24 15:13:58.855349

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9a30e5c5f6fd'
down_revision = 'd767e79ccb6e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_team_activities', table_name='activity', schema='crm_test')
    op.drop_index('idx_player_records', table_name='player_records', schema='crm_test')
    op.drop_index('idx_team_requests', table_name='team_requests', schema='crm_test')
    op.drop_index('idx_team_requests_position', table_name='team_requests', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_team_requests_position', 'team_requests', ['position'], unique=False, schema='crm_test')
    op.create_index('idx_team_requests', 'team_requests', ['status'], unique=False, schema='crm_test')
    op.drop_constraint(None, 'staff_records', schema='crm_test', type_='foreignkey')
    op.create_index('idx_player_records', 'player_records', ['control_stage'], unique=False, schema='crm_test')
    op.drop_constraint(None, 'activity', schema='crm_test', type_='foreignkey')
    op.create_index('idx_team_activities', 'activity', ['stage'], unique=False, schema='crm_test')
    # ### end Alembic commands ###