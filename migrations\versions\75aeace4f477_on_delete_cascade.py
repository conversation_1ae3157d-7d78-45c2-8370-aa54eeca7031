"""On delete cascade

Revision ID: 75aeace4f477
Revises: 5e102eee1180
Create Date: 2024-11-28 10:52:45.080422

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '75aeace4f477'
down_revision = '5e102eee1180'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('community_deal_community_proposal_id_fkey', 'community_deal', schema='crm', type_='foreignkey')
    op.create_foreign_key(None, 'community_deal', 'community_proposals', ['community_proposal_id'], ['id'], source_schema='crm', referent_schema='crm', ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'community_deal', schema='crm', type_='foreignkey')
    op.create_foreign_key('community_deal_community_proposal_id_fkey', 'community_deal', 'community_proposals', ['community_proposal_id'], ['id'], source_schema='crm', referent_schema='crm')
    # ### end Alembic commands ###