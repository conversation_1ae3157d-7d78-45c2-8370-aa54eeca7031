"""Add name column to rank records table structure

Revision ID: 83055aa28b70
Revises: 7b1b5d96fa99
Create Date: 2022-08-24 14:31:46.495826

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '83055aa28b70'
down_revision = '7b1b5d96fa99'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rank_records', sa.Column('name', sa.String(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rank_records', 'name', schema='crm_dev')
    # ### end Alembic commands ###