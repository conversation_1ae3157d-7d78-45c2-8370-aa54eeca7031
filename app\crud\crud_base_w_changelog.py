from .crud_base import CRUDBase
from typing import Any, Dict, Type, TypeVar, Union

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.db.base_class import Base
from app.models import User
from app.utils import compare_version_of_objects

ModelType = TypeVar("ModelType", bound=Base)
ChangeType = TypeVar("ChangeType", bound=Base)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)

class CRUDBaseWChangelog(CRUDBase):

    def update_with_changelog(
        self,
        db: Session,
        *,
        record_id: str,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
        change_model: Type[ChangeType],
        record_type: str,
        user: User
    ) -> ModelType:
        obj_in_data = jsonable_encoder(obj_in)

        if (record_type in ('team_request')):
            db_obj_data = jsonable_encoder(db_obj)
            if (str(obj_in_data['source_id']) == str(db_obj_data['source_id'])):
                obj_in_data.pop("source_id")
                
        changes = compare_version_of_objects(db_obj, obj_in_data, user.id,)

        for field, value in obj_in_data.items():
            if value is not None:
                setattr(db_obj, field, value)
        db.add(db_obj)
        for c in changes:
            c[f"{record_type}_id"] = record_id
            db.add(change_model(**c))
        db.commit()
        db.refresh(db_obj)
        return db_obj