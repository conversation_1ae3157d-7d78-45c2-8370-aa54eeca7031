"""Community deal - add a linked deal

Revision ID: b6f912ef102b
Revises: 42806172932f
Create Date: 2024-11-07 11:12:46.096759

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b6f912ef102b'
down_revision = '42806172932f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('community_deal', sa.Column('community_deal_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('community_deal', 'community_deal_id', schema='crm_test')
    # ### end Alembic commands ###