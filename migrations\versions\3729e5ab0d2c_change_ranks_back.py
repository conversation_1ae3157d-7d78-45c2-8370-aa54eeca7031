"""change ranks back

Revision ID: 3729e5ab0d2c
Revises: ae70e923a815
Create Date: 2023-04-24 16:25:22.740837

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3729e5ab0d2c'
down_revision = 'ae70e923a815'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reports', sa.Column('model_ranks', sa.String(), nullable=True), schema='crm_dev')
    op.drop_column('reports', 'reported_rank', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reports', sa.Column('reported_rank', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_dev')
    op.drop_column('reports', 'model_ranks', schema='crm_dev')
    # ### end Alembic commands ###