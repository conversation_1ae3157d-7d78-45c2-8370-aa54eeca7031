from pydantic import BaseModel, Field
from datetime import datetime, timedelta, timezone
import uuid
from typing import Optional
class ShareTokenCreate(BaseModel):
    resource_id: uuid.UUID
    expires_at: Optional[datetime]
    hide_field_view: Optional[bool] = False

class ShareTokenRead(BaseModel):
    token: uuid.UUID
    expires_at: datetime
    is_disabled: bool
    hide_field_view: Optional[bool] = False
