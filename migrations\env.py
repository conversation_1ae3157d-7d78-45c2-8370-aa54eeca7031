from __future__ import with_statement
from xml.etree.ElementInclude import include

from alembic import context
from sqlalchemy import engine_from_config, pool
from logging.config import fileConfig

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
from app.config import settings

if hasattr(context, 'config'):
    config = context.config

    # Interpret the config file for Python logging.
    # This line sets up loggers basically.
    fileConfig(config.config_file_name)

    # add your model's MetaData object here
    # for 'autogenerate' support
    # from myapp import mymodel
    # target_metadata = mymodel.Base.metadata
    # target_metadata = None

    from app.config import settings
    from app.db.base import Base  # noqa

    target_metadata = Base.metadata

    # other values from the config, defined by the needs of env.py,
    # can be acquired:
    # my_important_option = config.get_main_option("my_important_option")
    # ... etc.

    # adding include name, so that Alembic makes migrations only for the crm schema,
    # this should be passed to context.configure in addition to include_schemas=True:
    def include_name(name, type_, parent_names):
        return name in (settings.PG_SCHEMA, "config", "player_quality") if type_ == "schema" else True


    def include_object(object, name, type_, reflected, compare_to):
        """
        Exclude views from Alembic's consideration.
        """
        # skip foreign indexes to view because they will crash but are necessary for orm relationships:
        if type_ == "foreign_key_constraint" and object.referred_table.name in (
            "team_info",
            "team_info2",
            "player_info",
            "player_info2",
            "suit_scores",
        ):
            return False
        if type_ == "table" and reflected and compare_to is None:
            return False
        return not object.info.get("is_view", False)


    def run_migrations_offline():
        """Run migrations in 'offline' mode.
        This configures the context with just a URL
        and not an Engine, though an Engine is acceptable
        here as well.  By skipping the Engine creation
        we don't even need a DBAPI to be available.
        Calls to context.execute() here emit the given string to the
        script output.
        """
        url = settings.PG_URL
        context.configure(
            url=url,
            target_metadata=target_metadata,
            literal_binds=True,
            compare_type=True,
            include_schemas=True,
            include_name=include_name,
            include_object=include_object,
        )

        with context.begin_transaction():
            context.run_migrations()


    def run_migrations_online():
        """Run migrations in 'online' mode.
        In this scenario we need to create an Engine
        and associate a connection with the context.
        """
        connectable = context.config.attributes.get("connection", None)

        if connectable is None:
            configuration = config.get_section(config.config_ini_section)
            configuration["sqlalchemy.url"] = settings.PG_URL
            connectable = engine_from_config(
                configuration,
                prefix="sqlalchemy.",
                poolclass=pool.NullPool,
            )

        with connectable.connect() as connection:
            context.configure(
                connection=connection,
                target_metadata=target_metadata,
                compare_type=True,
                include_schemas=True,
                include_name=include_name,
                include_object=include_object,
            )

            with context.begin_transaction():
                context.run_migrations()


    if context.is_offline_mode():
        run_migrations_offline()
    else:
        run_migrations_online()