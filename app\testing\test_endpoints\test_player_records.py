from fastapi.testclient import TestClient
import json
from app.main import app
from app.testing.fixtures import (switch_between_users,
 client_chestnite,
  client_enskai,
  get_current_user_enskai_owner,
  get_current_user_chesnite_limited)
import pytest

client = TestClient(app)

player_records_dict = {'player_id_enskai': None, 'created_player_obj_enskai': None,
'player_id_chestnite': None, 'created_player_obj_chestnite': None, 'playerId': '480084'}

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_read_player_records(switch_between_users: TestClient):
    client, org = switch_between_users
    response = client.get("player_records/")
    data = response.json()
    assert data is not None, "Response returned null data"

    if org == 'enskai':
        first_data = data[0]
        assert "transfer_period" in first_data, "transfer_period is not in the returned contacts"
        assert "club_asking_price" in first_data, "club_asking_price is not in the returned contacts"
        assert "created_by" in first_data, "created_by is not in the returned contacts"
        assert "created_at" in first_data, "created_at is not in the returned contacts"
        assert "player_info" in first_data,"player_info is not in the returned contacts"
    else:
        assert len(data) == 0

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_create_player_record(switch_between_users: TestClient):
    client, org = switch_between_users
    data = json.dumps({
    "firstName": "Sid Ahmed",
    "lastName": "Dfeili",
    "team_name": "Créteil II",
    "birth_date": "1997-09-10",
    "passport": "France",
    "birth_area": "France",
    "playerId": "480084",
    "teamId": "4035",
    "tm_value": "",
    "tm_link": "",
    "agency": "",
    "club_asking_price": "",
    "current_gross_salary": "",
    "player_role": "",
    "priority_player": False,
    "proactively_scouted": False,
    "phone_number": "",
    "quality": "4",
    "potential": "4",
    "position": [
        "rw"
    ],
    "foot": "",
    "transfer_period": [],
    "control_stage": "signed",
    "contact_channel": [],
})
    response = client.post('player_records/', data=data, headers={"Content-Type": "application/json"})
    assert response.status_code == 200, f"Request failed, status code is {response.status_code}"
    reponse_obj = response.json()

    player_records_dict[f"created_player_obj_{org}"] = reponse_obj
    player_records_dict[f"player_id_{org}"] = reponse_obj['id']
    assert player_records_dict[f"player_id_{org}"] is not None, "Player Record id is None"

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_get_specific_player_record(switch_between_users: TestClient):
    client, org = switch_between_users

    response = client.get(f"""player_records/{player_records_dict[f"player_id_{org}"]}""")
    assert response.status_code == 200, f"Request failed, status code is {response.status_code}"
    returned_obj = response.json()
    assert returned_obj == player_records_dict[f"created_player_obj_{org}"], "The returned object should be equal to the just created one"
    
    non_existent_id_response = client.get("player_records/9f6b7234-6a6d-11ed-a1eb-0242ac120002")
    assert non_existent_id_response.status_code == 404, "Maybe we have this id in the DB -_-"

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_edit_specific_player_record(switch_between_users: TestClient):
    client, org = switch_between_users
    data = json.dumps({
    "position": [
        "rw"
    ],
    "player_role": "",
    "control_stage": "on_hold",
    "notes": '',
    "assigned_to": None,
    "foot": "",
    "transfer_period": [],
    "description": None,
    "contact_channel": [],
    "deals_with_player": [],
    "priority_player": False,
    "proactively_scouted": False,
    "current_gross_salary": "",
    "quality": 4,
    "potential": 4,
    "phone_number": "696969",
    "club_asking_price": "",
    "scouting_reports": [],
    "source": {
        "name": "",
        "id": []
    },
    "player_features": {
        "speed": "good",
        "explosive": "",
        "dynamic": "",
        "build": "",
        "intelligence": "",
        "scanning": "",
        "pressing": "",
        "aggressive": "",
        "leader": "",
        "teamwork": "",
        "education": "",
        "entourage": "",
        "first_touch": "",
        "dribble": "",
        "progress_with_the_ball": "",
        "play_with_the_back": "",
        "diagonal_passing": "",
        "through_ball_passing": "",
        "passes_to_final_third": "",
        "pushes_inbetween_lines": "",
        "defensive_positioning": "",
        "interceptions": "",
        "aerial_duels": "",
        "defensive_1_vs_one_duels": "",
        "finishing": "",
        "heading": "",
        "attacking_positioning": "",
        "cut_inside": "",
        "gk_1_vs_one_duels": "",
        "closing_space": "",
        "ball_control": "",
        "reflexes": "",
        "set_pieces": ""
    },
    "assigned_to_id": "",
    "source_to_record": []
})
    response = client.put(f"""player_records/{player_records_dict[f"player_id_{org}"]}""", data=data, headers={"Content-Type": "application/json"})
    assert response.status_code == 200,  f"Request failed, status code is {response.status_code}"
    response_obj = response.json()
    assert response_obj != player_records_dict[f"created_player_obj_{org}"], "Put request should have made updates"
    assert response_obj['phone_number'] == '696969', "Phone number is not changed to '696969'"
    assert response_obj['control_stage'] == 'on_hold', "control stage is not changed, but it should be"
    assert len(response_obj['changelog']) == 2, 'Changelog should have 2 items'

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_check_player_byId_endpoint(switch_between_users: TestClient):
    client, org = switch_between_users
    response = client.get(f"""player_records/check_player/{player_records_dict['playerId']}""")
    assert response.status_code == 200,  f"Request failed, status code is {response.status_code}"
    assert response.json() is True, "This Player should be in the database"

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_delete_specific_contact(switch_between_users: TestClient):
    client, org = switch_between_users

    response = client.delete(f"""player_records/{player_records_dict[f"player_id_{org}"]}""")
    assert response.status_code == 200,  f"Request failed, status code is {response.status_code}"
    response = client.get(f"""player_records/{player_records_dict[f"player_id_{org}"]}""")
    assert response.status_code == 404, "Id should be deleted, but apperantly it isn't"