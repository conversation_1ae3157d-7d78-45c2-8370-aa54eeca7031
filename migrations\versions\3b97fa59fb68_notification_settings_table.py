"""Notification settings table

Revision ID: 3b97fa59fb68
Revises: d79941067273
Create Date: 2023-07-07 12:29:41.434611

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3b97fa59fb68'
down_revision = 'd79941067273'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    # ### end Alembic commands ###