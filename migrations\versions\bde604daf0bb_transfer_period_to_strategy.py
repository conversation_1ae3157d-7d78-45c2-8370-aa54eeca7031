"""Transfer period to strategy

Revision ID: bde604daf0bb
Revises: 6430091787fa
Create Date: 2024-09-17 09:35:00.945664

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'bde604daf0bb'
down_revision = '6430091787fa'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('transfer_strategy', sa.String(), nullable=True), schema='crm_dev')
    op.drop_column('player_records', 'transfer_period', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('transfer_period', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True), schema='crm_dev')
    op.drop_column('player_records', 'transfer_strategy', schema='crm_dev')
    # ### end Alembic commands ###