from typing import Type, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, Response, status, Form
from sqlalchemy.orm import Session
from fastapi_users import exceptions, models, schemas
from fastapi_users.manager import BaseUserManager, UserManagerDependency
from fastapi_users.router.common import ErrorCode, ErrorModel
from app import crud
from app.schemas.organization import OrganizationCreate
from app.api import deps
from app.utils import generate_password
from app.utils.verify_recaptcha import verify_recaptcha
from sqlalchemy import create_engine
from app.config import settings
import pandas as pd
from firebase_admin import auth
from app.schemas.purchase import PurchaseCreate
from sqlalchemy import text
from hubspot import HubSpot
from hubspot.crm.contacts import SimplePublicObjectInput
# Initialize HubSpot Client
hubspot_client = HubSpot(access_token=settings.HUBSPOT_API_KEY)

def get_register_router(
    get_user_manager: UserManagerDependency[models.UP, models.ID],
    user_schema: Type[schemas.U],
    user_create_schema: Type[schemas.UC],
) -> APIRouter:
    """Generate a router with the register route."""
    router = APIRouter()

    @router.post(
        "/register",
        response_model=user_schema,
        status_code=status.HTTP_201_CREATED,
        name="register:register",
        responses={
            status.HTTP_400_BAD_REQUEST: {
                "model": ErrorModel,
                "content": {
                    "application/json": {
                        "examples": {
                            ErrorCode.REGISTER_USER_ALREADY_EXISTS: {
                                "summary": "A user with this email already exists.",
                                "value": {
                                    "detail": ErrorCode.REGISTER_USER_ALREADY_EXISTS
                                },
                            },
                            ErrorCode.REGISTER_INVALID_PASSWORD: {
                                "summary": "Password validation failed.",
                                "value": {
                                    "detail": {
                                        "code": ErrorCode.REGISTER_INVALID_PASSWORD,
                                        "reason": "Password should be"
                                        "at least 3 characters",
                                    }
                                },
                            },
                        }
                    }
                },
            },
        },
    )
    async def register(
        # user_create: user_create_schema,  # type: ignore
        email: str = Form(...),
        password: str = Form(...),
        recaptcha_token: Optional[str] = Form(None),
        first_name: str = Form(...),
        last_name: str = Form(...),
        organization_name: Optional[str] = Form(None),
        organization_id: Optional[str] = Form(None),
        role_id: Optional[str] = Form(None),
        phone_number: Optional[str] = Form(None),
        accept_terms: bool = Form(...),
        accept_marketing_emails: bool = Form(...),
        
        user_manager: BaseUserManager[models.UP, models.ID] = Depends(get_user_manager),
        db: Session = Depends(deps.get_db),
    ):

        request = {'email': email, 'password': password, 'first_name': first_name, 'last_name': last_name,
                    'accept_terms': accept_terms, 'accept_marketing_emails': accept_marketing_emails,
                    'phone_number': phone_number}
        user_create = user_create_schema(**request)
        # Part of our self service registration.
        # If organization_name is supplied, we will create a new organization
        # alongside the new user
        if organization_name:
            # We check recaptcha if the user is self registered
            if not verify_recaptcha(recaptcha_token):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="suspicious_activity",
                )

            new_organization = OrganizationCreate(
                name=organization_name, password=generate_password(), billing_email=email
            )
            organization = crud.organization.create(
                db=db,
                obj_in=new_organization,
            )
            user_create.organization_id = organization.id


        user_create.is_verified = False # We will send a verification email. If the user self-registered

        if not organization_name and organization_id and role_id:


            user_create.organization_id = organization_id
            user_create.is_verified
            user_create.role_id = role_id
            user_create.is_verified =True

        engine = create_engine(settings.PG_URL)

        if not user_create.role_id:
            # We need the owner role id
            roles = pd.read_sql(
            f"""select * from {settings.PG_SCHEMA}.user_roles where "name"='free_trial' """, engine
            )
            role_id = roles.id.values[0]
            user_create.role_id = role_id



        user_create.is_active = True
        user_create.is_enabled = True

        print(f'Creating user with active status: {user_create.is_active}')

        def create_hubspot_contact(first_name, last_name, email):
            contact_data = SimplePublicObjectInput(
                properties={
                    "email": email,
                    "firstname": first_name,
                    "lastname": last_name,
                    "jobtitle ": "self_registered",
                    "company": organization_name,
                }
            )
            try:
                response = hubspot_client.crm.contacts.basic_api.create(simple_public_object_input_for_create=contact_data)
                print(f"Contact created in HubSpot with ID: {response.id}")
            except Exception as e:
                print(f"Error creating contact in HubSpot: {e}")
        
        create_hubspot_contact(first_name, last_name, email)

        def remove_organization():
            crud.organization.remove(db=db, id=organization.id)

        try:
            created_user = await user_manager.create(
                user_create, safe=False, request=request
            )

            # Add user role to the default role table 
            with engine.connect() as conn:
                conn.execute(text(f"""insert into {settings.PG_SCHEMA}.user_roles_default (user_id, role_id) values('{created_user.id}', '{role_id}')"""))

            fbase_user = auth.ImportUserRecord(
                uid=str(created_user.id),
                display_name=created_user.email,
                email=created_user.email,
                email_verified=False,
                password_hash=created_user.hashed_password.encode(),
                custom_claims={
                    'schema': settings.PG_SCHEMA,
                    'is_active': created_user.is_active,
                    'is_superuser': created_user.is_superuser,
                    'is_verified': created_user.is_verified,
                    'organization_id': str(user_create.organization_id),
                    'role_id': str(user_create.role_id),
                    'is_enabled': created_user.is_enabled,
                }
            )

            hash_alg = auth.UserImportHash.bcrypt()
            result = auth.import_users([fbase_user], hash_alg=hash_alg)

            # print('Successfully imported {0} users. Failed to import {1} users.'.format(
            # result.success_count, result.failure_count))
        except exceptions.UserAlreadyExists:
            remove_organization()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=ErrorCode.REGISTER_USER_ALREADY_EXISTS,
            )
        except exceptions.InvalidPasswordException as e:
            remove_organization()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": ErrorCode.REGISTER_INVALID_PASSWORD,
                    "reason": e.reason,
                },
            )
        except Exception as e:
            print(e)
            remove_organization()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e),
            )
        if not organization_id:

            legal_module = pd.read_sql(text(f"""select * from {settings.PG_SCHEMA}.modules where name = 'legal'"""), engine)
            legal_module_id = legal_module.id.values[0]
            activity_tracker = pd.read_sql(text(f"""select * from {settings.PG_SCHEMA}.modules where name = 'activity_tracker'"""), engine)
            activity_tracker_id = activity_tracker.id.values[0]
            # We also buy the free trial modules
            legal = PurchaseCreate(module_id=legal_module_id, organization_id=organization.id)
            activity = PurchaseCreate(module_id=activity_tracker_id, organization_id=organization.id)
            crud.purchase.create(db=db, obj_in=legal)
            crud.purchase.create(db=db, obj_in=activity)

            await user_manager.request_verify(created_user) # Start the verification proccess

        return user_schema.from_orm(created_user)

    return router
