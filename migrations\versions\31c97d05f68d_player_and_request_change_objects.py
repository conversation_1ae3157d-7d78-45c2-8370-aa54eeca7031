"""player and request change objects

Revision ID: 31c97d05f68d
Revises: 444e068efa98
Create Date: 2022-08-03 06:06:35.072162

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '31c97d05f68d'
down_revision = '444e068efa98'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('team_request_changes',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('edit_at', sa.DateTime(), nullable=True),
    sa.Column('edit_by', sa.String(), nullable=True),
    sa.Column('field', sa.String(), nullable=True),
    sa.Column('previous', sa.String(), nullable=True),
    sa.Column('updated', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_team_request_changes_edit_at'), 'team_request_changes', ['edit_at'], unique=False, schema='crm_dev')
    op.create_table('player_changes',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('edit_at', sa.DateTime(), nullable=True),
    sa.Column('edit_by', sa.String(), nullable=True),
    sa.Column('field', sa.String(), nullable=True),
    sa.Column('previous', sa.String(), nullable=True),
    sa.Column('updated', sa.String(), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['player_id'], ['crm_dev.player_records.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_player_changes_edit_at'), 'player_changes', ['edit_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_player_changes_player_id'), 'player_changes', ['player_id'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_dev_player_changes_player_id'), table_name='player_changes', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_player_changes_edit_at'), table_name='player_changes', schema='crm_dev')
    op.drop_table('player_changes', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_team_request_changes_edit_at'), table_name='team_request_changes', schema='crm_dev')
    op.drop_table('team_request_changes', schema='crm_dev')
    # ### end Alembic commands ###