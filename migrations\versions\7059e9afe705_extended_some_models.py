"""extended some models

Revision ID: 7059e9afe705
Revises: 63e7d73495ac
Create Date: 2022-08-05 07:08:42.236429

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy
# revision identifiers, used by Alembic.
revision = '7059e9afe705'
down_revision = '63e7d73495ac'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('player_record_changes',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('edit_at', sa.DateTime(), nullable=True),
    sa.Column('previous', sa.String(), nullable=True),
    sa.Column('updated', sa.String(), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('edit_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.ForeignKeyConstraint(['edit_by'], ['crm_dev.user.id'], ),
    sa.ForeignKeyConstraint(['player_id'], ['crm_dev.player_records.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_player_record_changes_edit_at'), 'player_record_changes', ['edit_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_player_record_changes_player_id'), 'player_record_changes', ['player_id'], unique=False, schema='crm_dev')
    op.drop_index('ix_crm_dev_player_changes_edit_at', table_name='player_changes', schema='crm_dev')
    op.drop_index('ix_crm_dev_player_changes_player_id', table_name='player_changes', schema='crm_dev')
    op.drop_table('player_changes', schema='crm_dev')
    op.drop_index('ix_crm_dev_contacts_created_by', table_name='contacts', schema='crm_dev')
    op.create_foreign_key(None, 'contacts', 'user', ['created_by'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.add_column('modules', sa.Column('description', sa.String(), nullable=True), schema='crm_dev')
    op.drop_index('ix_crm_dev_player_records_created_by', table_name='player_records', schema='crm_dev')
    op.create_foreign_key(None, 'player_records', 'user', ['created_by'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.drop_index('ix_crm_dev_proposals_created_by', table_name='proposals', schema='crm_dev')
    op.create_foreign_key(None, 'proposals', 'user', ['created_by'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.drop_index('ix_crm_dev_reports_created_by', table_name='reports', schema='crm_dev')
    op.create_foreign_key(None, 'reports', 'user', ['created_by'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.add_column('team_request_changes', sa.Column('team_request_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_team_request_changes_team_request_id'), 'team_request_changes', ['team_request_id'], unique=False, schema='crm_dev')
    op.create_foreign_key(None, 'team_request_changes', 'team_requests', ['team_request_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.create_foreign_key(None, 'team_request_changes', 'user', ['edit_by'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.drop_column('team_request_changes', 'field', schema='crm_dev')
    op.drop_index('ix_crm_dev_team_requests_created_by', table_name='team_requests', schema='crm_dev')
    op.create_foreign_key(None, 'team_requests', 'user', ['created_by'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'team_requests', schema='crm_dev', type_='foreignkey')
    op.create_index('ix_crm_dev_team_requests_created_by', 'team_requests', ['created_by'], unique=False, schema='crm_dev')
    op.add_column('team_request_changes', sa.Column('field', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_dev')
    op.drop_constraint(None, 'team_request_changes', schema='crm_dev', type_='foreignkey')
    op.drop_constraint(None, 'team_request_changes', schema='crm_dev', type_='foreignkey')
    op.drop_index(op.f('ix_crm_dev_team_request_changes_team_request_id'), table_name='team_request_changes', schema='crm_dev')
    op.drop_column('team_request_changes', 'team_request_id', schema='crm_dev')
    op.drop_constraint(None, 'reports', schema='crm_dev', type_='foreignkey')
    op.create_index('ix_crm_dev_reports_created_by', 'reports', ['created_by'], unique=False, schema='crm_dev')
    op.drop_constraint(None, 'proposals', schema='crm_dev', type_='foreignkey')
    op.create_index('ix_crm_dev_proposals_created_by', 'proposals', ['created_by'], unique=False, schema='crm_dev')
    op.drop_constraint(None, 'player_records', schema='crm_dev', type_='foreignkey')
    op.create_index('ix_crm_dev_player_records_created_by', 'player_records', ['created_by'], unique=False, schema='crm_dev')
    op.drop_column('modules', 'description', schema='crm_dev')
    op.drop_constraint(None, 'contacts', schema='crm_dev', type_='foreignkey')
    op.create_index('ix_crm_dev_contacts_created_by', 'contacts', ['created_by'], unique=False, schema='crm_dev')
    op.create_table('player_changes',
    sa.Column('id', postgresql.UUID(), autoincrement=False, nullable=False),
    sa.Column('edit_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('edit_by', postgresql.UUID(), autoincrement=False, nullable=True),
    sa.Column('field', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('previous', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('updated', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('player_id', postgresql.UUID(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['player_id'], ['crm_dev.player_records.id'], name='player_changes_player_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='player_changes_pkey'),
    schema='crm_dev'
    )
    op.create_index('ix_crm_dev_player_changes_player_id', 'player_changes', ['player_id'], unique=False, schema='crm_dev')
    op.create_index('ix_crm_dev_player_changes_edit_at', 'player_changes', ['edit_at'], unique=False, schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_player_record_changes_player_id'), table_name='player_record_changes', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_player_record_changes_edit_at'), table_name='player_record_changes', schema='crm_dev')
    op.drop_table('player_record_changes', schema='crm_dev')
    # ### end Alembic commands ###