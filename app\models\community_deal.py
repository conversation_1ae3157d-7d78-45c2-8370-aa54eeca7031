from sqlalchemy import Column, String, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from app.models.comment import Comment

from app.models.extended_base_mixin import ExtendedBaseMixin
from app.db.base_class import Base
from app.config import settings


class CommunityDeal(Base, ExtendedBaseMixin):
    __tablename__ = "community_deal"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    community_proposal_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.community_proposals.id", ondelete="CASCADE"),
        index=True,
    )
    community_proposal = relationship(
        "CommunityProposal",
        viewonly=True,
        primaryjoin="foreign(CommunityDeal.community_proposal_id)==remote(CommunityProposal.id)",
    )
    community_deal_id = Column(UUID(as_uuid=True),)
    title = Column(String)
    feedback = Column(String)
    type = Column(String)
    organization_proposed_to = Column(String)
    email_proposed_to = Column(String)
    suitability_score = Column(Float)
    phone_number = Column(String)