from typing import Any, Optional
from fastapi.params import Query
import requests
from app.schemas.enums import Position
from app import models
from app import crud
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from app.api import deps, utils
from app.schemas.player_record import PlayerRecord
from app.schemas.team_request import TeamRequest
from app.api.endpoints.player_suitability import get_player_suitability_to_all, get_request_suitability_to_all
try:
    from ...config import settings
except:
    from app.config import settings

router = APIRouter()

@router.get("/player/{id}")
async def match_controlled_player(
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Match players by Id
    """
    resp = requests.get(
        f"{settings.MATCHING_API_URL}/match/player/{id}",
        auth=(settings.MATCHING_API_USR, settings.MATCHING_API_PASS),
    )
    return JSONResponse(status_code=resp.status_code, content=resp.json())

@router.get("/match/player/{id}")
async def match_controlled_player(
    id: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    utils.check_get_all(PlayerRecord, current_user)
    player_info = crud.player_info.get(db=db, id=id)
    if player_info is None:
        raise HTTPException(
            status_code=404, detail=f"player {id} not found"
        )
        
    return_requests = get_player_suitability_to_all(db=db, player_id=id, organization_id=current_user.organization_id)
    
    return return_requests

@router.get("/match/request/{request_id}")
async def match_team_request(
    request_id: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    active: Optional[bool] = Query(None),
    control_stage: Optional[str] = Query(None)
):
    
    # utils.check_get_all(TeamRequest, current_user)
    # team_request = crud.team_request.get(id=request_id, db=db)
    # if (team_request) is None:
    #     raise HTTPException(
    #         status_code=404, detail=f"team_request {request_id} not found"
    #     )
    control_stage_list = control_stage.split(",") if control_stage else None
    # return get_request_suitability_to_all(db=db, request_id=request_id, organization_id=current_user.organization_id)
    return crud.suit_score_joined.get_matches_for_request(db, current_user.organization_id, request_id, active, control_stage_list, current_user)