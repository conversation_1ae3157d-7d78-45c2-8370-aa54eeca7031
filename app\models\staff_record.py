from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime, Float
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy import and_, or_, func

from app.models.comment import Comment
from app.models.extended_base_mixin import ExtendedBaseMixin
from app.db.base_class import Base
from app.config import settings
from app.models.staff_info import StaffInfo 

class StaffRecord(Base, ExtendedBaseMixin):
    __tablename__ = "staff_records"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    staff_id = Column(
        Integer,
        ForeignKey("transfermarkt.staff_info.staff_id"),
        index=True,
    )
    staff_info = relationship(
        "StaffInfo",
        viewonly=True,
        primaryjoin="foreign(StaffRecord.staff_id)==remote(StaffInfo.staff_id)",
    )
    current_salary = Column(Float)
    expected_salary = Column(Float)
    early_termination_fee = Column(Float)
    licenses = Column(ARRAY(String))
    languages = Column(ARRAY(String))
    control_stage = Column(String)
    transfer_strategy = Column(String)
    assigned_to_record = relationship(
        "AssignedToRecord", primaryjoin='StaffRecord.id==AssignedToRecord.staff_record_id',
        cascade="all, delete-orphan"
    )
    description = Column(String)
    roles = Column(ARRAY(String))
    regions_of_interest = Column(ARRAY(String))
    roles_of_interest = Column(ARRAY(String))
    linked_tm_members = Column(ARRAY(Integer))

    uploads = relationship("StaffUpload", back_populates="staff")
    comments = relationship(
        "Comment",
        back_populates="staff",
        foreign_keys=Comment.staff_id,
        cascade="all, delete-orphan"
    )

class StaffUpload(Base, ExtendedBaseMixin):
    __tablename__ = "staff_uploads"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    id = Column(String, primary_key=True, )
    staff_id = Column(
        UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.staff_records.id"), index=True
    )
    staff = relationship("StaffRecord", back_populates="uploads")
    name = Column(String)