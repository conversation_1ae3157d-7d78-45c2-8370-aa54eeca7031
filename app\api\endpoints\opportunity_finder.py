from typing import Any, List, Optional
from datetime import datetime, date
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
import sqlalchemy
import pandas as pd
from app.utils import positionMappingWS
from app import models
from app.api import deps, utils
from app.db.session import engine
try:
    from ...config import settings
except:
    from app.config import settings
router = APIRouter()
from app.api.endpoints.player_suitability import get_suitability_scores_for_teams
from app.smart_tools.club_finder_func import fetch_club_finder_results
from app.smart_tools.staff_finder_func import fetch_staff_finder_data
from app.smart_tools.fetch_ex_teammates import fetch_ex_teammates

router = APIRouter(
    dependencies=[Depends(deps.generate_route_auth_check_func("opportunity_finder"))]
)


def fetch_team_requests(
    grouped_results: List[dict],
    current_user: models.User,
    position: str,
) -> None:
    """
    For each item in grouped_results:
      1) Map transfermarkt_team_id -> wyscout_team_id.
      2) Attach .wyscout_team_id
      3) In one query, group open requests by "teamId" and gather:
         - total_requests (count of rows)
         - position_requests (count of rows matching the position)
      4) item["team_requests"] = True if total_requests > 0
      5) item["team_request_for_position"] = True if position_requests > 0
    """

    # -- If you need to map the argument 'position' to what's stored in DB:
    # position_code = positionMappingWS.get(position, None)
    # If there's a chance position is not in positionMappingWS, handle that:
    try:
        position_code = positionMappingWS[position]
    except KeyError:
        position_code = None

    # 1) Gather Transfermarkt IDs
    all_tm_ids = [
        str(item["transfermarkt_team_id"])
        for item in grouped_results
        if item.get("transfermarkt_team_id") is not None
    ]
    if not all_tm_ids:
        # No teams => attach default false
        for item in grouped_results:
            item["wyscout_team_id"] = None
            item["team_requests"] = False
            item["team_request_for_position"] = False
        return

    tm_ids_str = ",".join(all_tm_ids)

    # 2) Map TM -> WS
    query_tm2ws = f"""
        SELECT transfermarkt_team_id AS tm_team_id,
               "teamId"             AS ws_team_id
        FROM transfermarkt.tm_to_ws_team_ids
        WHERE transfermarkt_team_id IN ({tm_ids_str})
    """
    tm2ws_df = pd.read_sql(query_tm2ws, engine)

    if tm2ws_df.empty:
        # No mappings => everything is None
        for item in grouped_results:
            item["wyscout_team_id"] = None
            item["team_requests"] = False
            item["team_request_for_position"] = False
        return

    # Build dict: TM -> WS
    tmid2wsid = {}
    for _, row in tm2ws_df.iterrows():
        tmid2wsid[str(row["tm_team_id"])] = str(row["ws_team_id"])

    # Gather all distinct WS IDs
    all_ws_ids = list(tmid2wsid.values())
    if not all_ws_ids:
        for item in grouped_results:
            item["wyscout_team_id"] = None
            item["team_requests"] = False
            item["team_request_for_position"] = False
        return

    ws_ids_str = ",".join(all_ws_ids)

    # 3) Single query to group open requests by teamId
    # We compute two aggregates:
    #   total_requests           = COUNT(*)
    #   position_requests_count = SUM(CASE WHEN position = :pos THEN 1 ELSE 0 END)
    # That way we see if there's at least 1 request for the given position.

    base_sql = f"""
        SELECT "teamId",
               COUNT(*) AS total_requests,
               SUM(CASE WHEN position = :pos THEN 1 ELSE 0 END) AS position_requests_count
        FROM {settings.PG_SCHEMA}.team_requests
        WHERE "teamId" IN ({ws_ids_str})
          AND organization_id = :org_id
          AND status = 'open'
        GROUP BY "teamId"
    """

    # Use None-safe approach for the position param (if needed)
    sql_params = {"org_id": current_user.organization_id}
    # If position_code is None, no team requests will match that position
    # so we can pass some invalid placeholder or handle via conditional
    if position_code is None:
        sql_params["pos"] = "___NO_SUCH_POSITION___"
    else:
        sql_params["pos"] = position_code

    requests_df = pd.read_sql(sqlalchemy.text(base_sql), engine, params=sql_params)

    # Turn the result into a dictionary: wsId -> (total_requests, position_requests_count)
    stats_by_ws_id = {}
    for _, row in requests_df.iterrows():
        wsid_str = str(row["teamId"])
        stats_by_ws_id[wsid_str] = {
            "total_requests": int(row["total_requests"]),
            "position_requests_count": int(row["position_requests_count"]),
        }

    # 4) Attach data back to grouped_results
    for item in grouped_results:
        tm_tid_str = str(item.get("transfermarkt_team_id", ""))
        ws_id = tmid2wsid.get(tm_tid_str)

        # We always set the Wyscout team ID (or None if not mapped)
        item["wyscout_team_id"] = ws_id if ws_id else None

        if ws_id and ws_id in stats_by_ws_id:
            counts = stats_by_ws_id[ws_id]
            item["team_requests"] = counts["total_requests"] > 0
            item["team_request_for_position"] = counts["position_requests_count"] > 0
        else:
            # No requests found
            item["team_requests"] = False
            item["team_request_for_position"] = False


@router.get("/")
def opportunity_finder(
    *,
    db: Session = Depends(deps.get_db),
    playerId: Optional[int] = Query(None),
    tm_player_id: Optional[str] = Query(None),
    league: bool = True,
    culture: bool = True,
    position: bool = False,
    division_level: List[str] = Query(default=["1", "2"]),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Example merged endpoint that:
      1) Ensures we have both Wyscout (playerId) and Transfermarkt (tm_player_id).
      2) Fetches club_finder results (only clubs with >1 transfer).
      3) Fetches staff_finder results.
      4) Groups them into a single structure by team.
      5) Fetches ex-teammates.
      6) Fetches team requests from CRM.
      7) Attaches them to the final structure & calculates suitability, sorts final results.
    """
    utils.check_module_access(current_user, "opportunity_finder")
    # --------------------------------------------------------------
    # (1) Ensure both playerId and tm_player_id
    # --------------------------------------------------------------
    if playerId and not tm_player_id:
        mapping_df = pd.read_sql(
            f"""
            SELECT tm_player_id 
            FROM transfermarkt.tm_to_ws_ids
            WHERE "playerId" = '{playerId}'
            """,
            engine,
        )
        if not mapping_df.empty:
            tm_player_id = str(mapping_df.iloc[0]["tm_player_id"])

    if tm_player_id and not playerId:
        mapping_df = pd.read_sql(
            f"""
            SELECT "playerId"
            FROM transfermarkt.tm_to_ws_ids
            WHERE tm_player_id = '{tm_player_id}'
            """,
            engine,
        )
        if not mapping_df.empty:
            playerId = int(mapping_df.iloc[0]["playerId"])

    if not playerId or not tm_player_id:
        raise HTTPException(status_code=409, detail="data_not_processed")

    # --------------------------------------------------------------
    # (2) Fetch the club_finder data
    # --------------------------------------------------------------
    club_finder_results = fetch_club_finder_results(
        db=db,
        playerId=playerId,
        league=league,
        culture=culture,
        position=position,
        division_level=division_level,
    )

    # We'll also read the player's primary_ws_position
    player_info = pd.read_sql(
        f"""
        SELECT "shortName", "primary_ws_position", "current_value"
        FROM wyscout.player_info2
        WHERE "playerId" = {playerId}
        """,
        engine,
    )
    try:
        primary_ws_position = player_info.iloc[0]["primary_ws_position"]
    except:
        primary_ws_position = None

    # --------------------------------------------------------------
    # (3) Fetch staff_finder data
    # --------------------------------------------------------------
    staff_finder_data = fetch_staff_finder_data(
        db=db, tm_player_id=tm_player_id, current_user=current_user
    )

    # --------------------------------------------------------------
    # (4) Group club_finder & staff_finder results by team
    # --------------------------------------------------------------
    teams_map = {}
    # Add all club_finder results
    for club_item in club_finder_results:
        tname = club_item["current_team"]
        tid = club_item["transfermarkt_team_id"]
        if tname not in teams_map:
            teams_map[tname] = {
                "team_name": tname,
                "transfermarkt_team_id": tid,
                "league_country": club_item["league_country"],
                "club_finder_entries": {},
                "staff_finder_entries": [],
            }
        teams_map[tname]["club_finder_entries"] = club_item["transfers"]
        teams_map[tname]["transfer_count"] = club_item["transfer_count"]

    # Add all staff_finder results
    for staff_item in staff_finder_data:
        tname = staff_item["curr_team"]
        tid = staff_item["curr_team_id"]
        if tname not in teams_map:
            teams_map[tname] = {
                "team_name": tname,
                "transfermarkt_team_id": tid,
                "league_country": staff_item["curr_team_country"],
                "club_finder_entries": [],
                "staff_finder_entries": [],
            }
        else:
            if "transfermarkt_team_id" not in teams_map[tname]:
                teams_map[tname]["transfermarkt_team_id"] = tid
        teams_map[tname]["staff_finder_entries"].append(staff_item)

    grouped_results = list(teams_map.values())

    # --------------------------------------------------------------
    # (5) Fetch ex-teammates and attach
    # --------------------------------------------------------------
    ex_teammates_rows = fetch_ex_teammates(db, int(tm_player_id))
    current_team_map = {}
    for row in ex_teammates_rows:

        ctid = row["current_team_id"]
        cname = row["current_team_name"]
        wsid = row["ws_team_id"]
        if row["ws_team_id"] == -1:
            wsid = None
        if ctid not in current_team_map:
            current_team_map[ctid] = {
                "transfermarkt_team_id": ctid,
                "team_name": cname,
                "league_country": row["league_country"],
                "current_ws_team_id": wsid,
                "ex_teammates_entries": [],
            }
        current_team_map[ctid]["ex_teammates_entries"].append(row)

    # Map existing grouped_results
    tmid2team = {}
    for team_dict in grouped_results:
        tid = team_dict["transfermarkt_team_id"]
        tmid2team[tid] = team_dict
        team_dict.setdefault("ex_teammates_entries", [])

    # Attach ex-teammates or create a new team entry
    for ctid, ex_obj in current_team_map.items():
        if ctid in tmid2team:
            tmid2team[ctid]["ex_teammates_entries"] = ex_obj["ex_teammates_entries"]
            tmid2team[ctid]["wyscout_team_id"] = ex_obj["current_ws_team_id"]
        else:
            new_team = {
                "team_name": ex_obj["team_name"],
                "league_country": ex_obj["league_country"],
                "transfermarkt_team_id": ctid,
                "club_finder_entries": [],
                "staff_finder_entries": [],
                "ex_teammates_entries": ex_obj["ex_teammates_entries"],
                "wyscout_team_id": ex_obj["current_ws_team_id"],
            }
            grouped_results.append(new_team)
            tmid2team[ctid] = new_team
    # --------------------------------------------------------------
    # (6) Fetch team requests & attach
    # --------------------------------------------------------------
    fetch_team_requests(
        grouped_results=grouped_results,
        current_user=current_user,
        position=primary_ws_position,
    )

    # Suitability
    team_ids = [
        item["wyscout_team_id"]
        for item in grouped_results
        if item.get("wyscout_team_id") is not None
    ]
    scores = get_suitability_scores_for_teams(db, playerId, team_ids)
    scores_map = {int(s["team_id"]): s["suitability_score"] for s in scores}
    for team in grouped_results:
        ws_id_str = team.get("wyscout_team_id")
        if ws_id_str is not None:
            ws_id = int(ws_id_str)
            team["suitability_score"] = scores_map.get(ws_id, 0.0)
        else:
            team["suitability_score"] = 0.0

    
    # Remove teams that have only 1 club finder result and nothing else
    grouped_results = [
        team for team in grouped_results
        if not (
            len(team["club_finder_entries"]) == 1 and
            not team["staff_finder_entries"] and
            not team["ex_teammates_entries"] and
            not team["team_request_for_position"]
        )
    ]
    
    # --------------------------------------------------------------
    # (8) Sort logic
    # --------------------------------------------------------------
    for team in grouped_results:
        try:
            suitability = round(team["suitability_score"] * 5 * 2) / 2 # In order to make it 0-5
        except:
            suitability = 0

        # Base score (0–6)
        base_score = (suitability / 5.0) * 6.0

        # Raw bonus from signals
        bonus = 0
        bonus += 0.5 if team.get("club_finder_entries") else 0
        bonus += 2.0 if team.get("staff_finder_entries") else 0
        bonus += 1.0 if team.get("ex_teammates_entries") else 0
        if team.get("team_requests"):
            bonus += 0.5
        elif team.get("team_request_for_position"):
            bonus += 1.5
        bonus = min(bonus, 5.0)

        # Tiered signal multiplier
        if suitability >= 4.5:
            multiplier = 1.0
        elif suitability >= 3.5:
            multiplier = 0.75
        elif suitability >= 2.5:
            multiplier = 0.5
        else:
            multiplier = 0.3

        # Signal impact (0–4 scaled by multiplier)
        signal_score = (bonus / 5.0) * 4.0 * multiplier

        # Final opportunity score (0–10)
        team["opportunity_score"] = round(base_score + signal_score, 2) / 2

    # Sort results descending by opportunity score
    grouped_results.sort(key=lambda t: t["opportunity_score"], reverse=True)


    # Return final JSON
    return {
        "input_player_name": (
            player_info.iloc[0]["shortName"] if not player_info.empty else ""
        ),
        "playerId": playerId,
        "tm_player_id": tm_player_id,
        "teams": grouped_results,
    }
