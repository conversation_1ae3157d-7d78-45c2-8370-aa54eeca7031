"""change orgs

Revision ID: 024f73d7a3f4
Revises: 303bc75acef4
Create Date: 2023-04-24 16:20:40.413338

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '024f73d7a3f4'
down_revision = '303bc75acef4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('passwd', sa.String(), nullable=True), schema='crm')
    op.drop_column('organizations', 'description', schema='crm')
    op.add_column('reports', sa.Column('reported_rank', sa.String(), nullable=True), schema='crm')
    op.drop_column('reports', 'model_ranks', schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reports', sa.Column('model_ranks', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm')
    op.drop_column('reports', 'reported_rank', schema='crm')
    op.add_column('organizations', sa.Column('description', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm')
    op.drop_column('organizations', 'passwd', schema='crm')
    # ### end Alembic commands ###