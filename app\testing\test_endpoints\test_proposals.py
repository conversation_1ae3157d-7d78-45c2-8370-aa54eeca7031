from fastapi.testclient import TestClient
import json
from app.main import app
from app.testing.fixtures import (switch_between_users,
 client_chestnite,
  client_enskai,
  get_current_user_enskai_owner,
  get_current_user_chesnite_limited)
import pytest

proposals_dict = {'proposal_id_enskai': None, 'created_proposal_obj_enskai': None}

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_read_proposals(switch_between_users: TestClient):
    client, org = switch_between_users
    response = client.get("proposals/")
    data = response.json()
    assert data is not None, "Response returned null data"

    if org == 'enskai':
        first_data = data[0]
        assert "player_id" in first_data, "player_id is not in the returned contacts"
        assert "request_id" in first_data, "request_id is not in the returned contacts"
        assert "created_at" in first_data, "created_at is not in the returned contacts"
        assert "notes" in first_data,"notes is not in the returned contacts"
    else:
        assert len(data) == 0 # Chestnite have 0 proposals

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_create_proposal(switch_between_users: TestClient):
    client, org = switch_between_users

    data = json.dumps({
        "player_id": "ef50150d-39d4-4de9-a454-7d4bf921bb47",
        "request_id": "49c15735-c4f6-488d-ad2a-9012be8a2865",
        "notes": "Good connection with club"
    })
    response = client.post("""proposals/""", data=data, headers={"Content-Type": "application/json"})
    reponse_obj = response.json()

    if org == 'enskai':
        assert response.status_code == 200, f"Request failed, status code is {response.status_code}"        
        proposals_dict[f"created_proposal_obj_{org}"] = reponse_obj
        proposals_dict[f"proposal_id_{org}"] = reponse_obj['id']
        assert proposals_dict[f"proposal_id_{org}"] is not None, "Proposal id is None"
    else:
        assert response.status_code == 403, "Chestnite shouldn't be authorised to view Enskai's requests"
    
@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_get_specific_proposal(switch_between_users: TestClient):
    client, org = switch_between_users
    
    if org == 'enskai':
        response = client.get(f"""proposals/{proposals_dict[f"proposal_id_{org}"]}""")
        assert response.status_code == 200, f"Request failed, status code is {response.status_code}"
        returned_obj = response.json()
        assert returned_obj == proposals_dict[f"created_proposal_obj_{org}"], "The returned object should be equal to the just created one"
    
    non_existent_id_response = client.get("proposals/9f6b7234-6a6d-11ed-a1eb-0242ac120002")
    assert non_existent_id_response.status_code == 404, "Maybe we have this id in the DB -_-"

#@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
#def test_edit_specific_proposal(switch_between_users: TestClient):
#    client, org = switch_between_users
#
#    data = json.dumps({
#        "player_id": "ef50150d-39d4-4de9-a454-7d4bf921bb47",
#        "request_id": "49c15735-c4f6-488d-ad2a-9012be8a2865",
#        "notes": "Bob"
#    })
#    
#    if org == 'enskai':
#        response = client.put(f"""proposals/{proposals_dict[f"proposal_id_{org}"]}""", data=data, headers={"Content-Type": "application/json"})
#        assert response.status_code == 200,  f"Request failed, status code is {response.status_code}"
#        response_obj = response.json()
#        assert response_obj != proposals_dict[f"created_proposal_obj_{org}"], "Put request should have made updates"
#        assert response_obj['notes'] == 'Bob', "Notes is not changed to 'Bob'"


@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_delete_specific_proposal(switch_between_users: TestClient):
    client, org = switch_between_users

    if org == 'enskai':
        response = client.delete(f"""proposals/{proposals_dict[f"proposal_id_{org}"]}""")
        assert response.status_code == 200,  f"Request failed, status code is {response.status_code}"
        response = client.get(f"""proposals/{proposals_dict[f"proposal_id_{org}"]}""")
        assert response.status_code == 404, "Id should be deleted, but apperantly it isn't"