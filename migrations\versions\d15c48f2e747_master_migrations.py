"""Master migrations

Revision ID: d15c48f2e747
Revises: 9d92ed5f43ac
Create Date: 2025-05-30 17:17:06.679987

"""
from alembic import op
import sqlalchemy as sa
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = 'd15c48f2e747'
down_revision = '9d92ed5f43ac'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('admin_changes',
    sa.Column('action_type', sa.String(), nullable=False),
    sa.Column('target_type', sa.String(), nullable=False),
    sa.Column('target_id', sa.UUID(), nullable=False),
    sa.Column('details', sa.String(), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('edit_at', sa.DateTime(), nullable=True),
    sa.Column('field', sa.String(), nullable=True),
    sa.Column('previous', sa.String(), nullable=True),
    sa.Column('updated', sa.String(), nullable=True),
    sa.Column('edit_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.ForeignKeyConstraint(['edit_by'], ['crm.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_admin_changes_edit_at'), 'admin_changes', ['edit_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_admin_changes_edit_by'), 'admin_changes', ['edit_by'], unique=False, schema='crm')
    op.add_column('field_requests', sa.Column('is_team_hidden', sa.Boolean(), nullable=True), schema='crm')
    op.add_column('football_fields', sa.Column('contact_name', sa.String(), nullable=True), schema='crm')
    op.add_column('football_fields', sa.Column('contact_email', sa.String(), nullable=True), schema='crm')
    op.add_column('football_fields', sa.Column('contact_phone', sa.String(), nullable=True), schema='crm')
    op.add_column('request_fields', sa.Column('contact_name', sa.String(), nullable=True), schema='crm')
    op.add_column('request_fields', sa.Column('contact_email', sa.String(), nullable=True), schema='crm')
    op.add_column('request_fields', sa.Column('contact_phone', sa.String(), nullable=True), schema='crm')
    op.add_column('share_tokens', sa.Column('hide_field_view', sa.Boolean(), nullable=True), schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('share_tokens', 'hide_field_view', schema='crm')
    op.drop_column('request_fields', 'contact_phone', schema='crm')
    op.drop_column('request_fields', 'contact_email', schema='crm')
    op.drop_column('request_fields', 'contact_name', schema='crm')
    op.drop_column('football_fields', 'contact_phone', schema='crm')
    op.drop_column('football_fields', 'contact_email', schema='crm')
    op.drop_column('football_fields', 'contact_name', schema='crm')
    op.drop_column('field_requests', 'is_team_hidden', schema='crm')
    op.drop_index(op.f('ix_crm_admin_changes_edit_by'), table_name='admin_changes', schema='crm')
    op.drop_index(op.f('ix_crm_admin_changes_edit_at'), table_name='admin_changes', schema='crm')
    op.drop_table('admin_changes', schema='crm')
    # ### end Alembic commands ###