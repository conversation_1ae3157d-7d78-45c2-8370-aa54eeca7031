"""migrate staging

Revision ID: ab67db4156ec
Revises: dab961a11741
Create Date: 2024-04-18 13:34:45.676511

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ab67db4156ec'
down_revision = 'dab961a11741'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_roles_default',
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('role_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['role_id'], ['crm_dev.user_roles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['crm_dev.user.id'], ),
    sa.PrimaryKeyConstraint('user_id'),
    schema='crm_dev'
    )
    op.create_table('tracked_transfers',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('position', sa.String(), nullable=True),
    sa.Column('player_name', sa.String(), nullable=True),
    sa.Column('left_name', sa.String(), nullable=True),
    sa.Column('fee', sa.String(), nullable=True),
    sa.Column('date', sa.DateTime(), nullable=True),
    sa.Column('player_url', sa.String(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['request_id'], ['crm_dev.team_requests.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('request_id', 'player_name', 'date', name='uix_request_id_player_name_date'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_tracked_transfers_request_id'), 'tracked_transfers', ['request_id'], unique=False, schema='crm_dev')
    op.add_column('team_requests', sa.Column('status', sa.String(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('team_requests', 'status', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_tracked_transfers_request_id'), table_name='tracked_transfers', schema='crm_dev')
    op.drop_table('tracked_transfers', schema='crm_dev')
    op.drop_table('user_roles_default', schema='crm_dev')
    # ### end Alembic commands ###