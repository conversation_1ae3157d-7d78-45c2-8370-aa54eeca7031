"""player_record foreign keys

Revision ID: 93dbb9f08652
Revises: 31c97d05f68d
Create Date: 2022-08-03 10:25:51.781558

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '93dbb9f08652'
down_revision = '31c97d05f68d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('assigned_to_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_dev')
    op.add_column('player_records', sa.Column('source_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_player_records_assigned_to_id'), 'player_records', ['assigned_to_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_player_records_source_id'), 'player_records', ['source_id'], unique=False, schema='crm_dev')
    op.create_foreign_key(None, 'player_records', 'contacts', ['assigned_to_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.create_foreign_key(None, 'player_records', 'contacts', ['source_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'player_records', schema='crm_dev', type_='foreignkey')
    op.drop_constraint(None, 'player_records', schema='crm_dev', type_='foreignkey')
    op.drop_index(op.f('ix_crm_dev_player_records_source_id'), table_name='player_records', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_player_records_assigned_to_id'), table_name='player_records', schema='crm_dev')
    op.drop_column('player_records', 'source_id', schema='crm_dev')
    op.drop_column('player_records', 'assigned_to_id', schema='crm_dev')
    # ### end Alembic commands ###