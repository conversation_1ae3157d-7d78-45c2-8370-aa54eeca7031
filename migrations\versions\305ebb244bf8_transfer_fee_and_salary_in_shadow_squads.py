"""Transfer fee and salary in shadow squads

Revision ID: 305ebb244bf8
Revises: 1c67db33bd80
Create Date: 2023-11-09 10:52:20.702244

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '305ebb244bf8'
down_revision = '1c67db33bd80'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_players', sa.Column('transfer_fee', sa.Float(), nullable=True), schema='crm_test')
    op.add_column('field_players', sa.Column('asking_salary', sa.Float(), nullable=True), schema='crm_test')
    op.drop_constraint('field_players_field_id_fkey', 'field_players', schema='crm_test', type_='foreignkey')
    op.create_foreign_key(None, 'field_players', 'football_fields', ['field_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'field_players', schema='crm_test', type_='foreignkey')
    op.create_foreign_key('field_players_field_id_fkey', 'field_players', 'football_fields', ['field_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_column('field_players', 'asking_salary', schema='crm_test')
    op.drop_column('field_players', 'transfer_fee', schema='crm_test')
    # ### end Alembic commands ###