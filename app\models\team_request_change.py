from sqlalchemy import Column, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from app.db.base_class import Base
from app.models.change_base_mixin import ChangeBaseMixin
from app.config import settings


class TeamRequestChange(Base, ChangeBaseMixin):
    __tablename__ = "team_request_changes"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    team_request_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.team_requests.id"),
        index=True,
    )
    # team_request = relationship("TeamRequest", back_populates="changelog")