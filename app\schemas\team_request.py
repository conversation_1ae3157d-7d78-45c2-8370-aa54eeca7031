import uuid
from typing import List, TYPE_CHECKING, Optional, Union
from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
    ExtendedCommunityBase
)
from pydantic import (
    BaseModel,
)
from app.schemas.organization import OrganizationShort as Organization
from app.schemas.team_info import TeamInfo, CommunityTeamInfo
from app.schemas.enums import Position, FootPreference, RequestStage, Type, TransferPeriod, Status
from app.schemas.user import UserShort
from app.schemas.source_to_record import SourceToRecord 
from app.schemas.tracked_transfers import TrackedTransfer

if TYPE_CHECKING:
    from app.schemas.proposal import Proposal


class TeamRequestUpdate(ExtendedUpdateBase):
    position: Optional[Position]
    is_community: Optional[bool]
    foot: Optional[FootPreference]
    max_age: Optional[int]
    max_value: Optional[int]
    max_net_salary: Optional[int]
    transfer_period: Optional[List[TransferPeriod]]
    description: Optional[str]
    type: Optional[List[Type]]
    teamId: Optional[int]
    source_to_record: Optional[List[uuid.UUID]]
    status: Optional[Status]
    club_contact: Optional[str]
    eu_passport: Optional[bool]

class TeamRequestUpdateCommunity(ExtendedUpdateBase):
    position: Optional[Position]
    is_community: Optional[bool]
    foot: Optional[FootPreference]
    max_age: Optional[int]
    max_value: Optional[int]
    max_net_salary: Optional[int]
    transfer_period: Optional[List[TransferPeriod]]
    type: Optional[List[Type]]
    teamId: Optional[int]
    status: Optional[Status]
    eu_passport: Optional[bool]

    class Config:
        orm_mode = True
        use_cache=True

class TeamRequestCreate(TeamRequestUpdate, ExtendedCreateBase):
    position: Position
    foot: FootPreference = FootPreference.no_prefence
    max_age: Optional[int]
    max_value: Optional[int]
    max_net_salary: Optional[int]
    transfer_period: Optional[List[TransferPeriod]]
    description: Optional[str]
    type: List[Union[Type, str]]
    teamId: int
    is_community: Optional[bool]=False
    status: Status = Status.open
    emails: Optional[List[str]]

class TeamRequest(TeamRequestUpdate, ExtendedBase):
    #proposed_players: "List[Proposal]"
    team_info: TeamInfo
    #changelog: List[Change]
    #organization: Organization  # need this to avoid circular imports
    source_to_record: Optional[List[SourceToRecord]]
    tracked_transfer: Optional[List[TrackedTransfer]]


class CommunityInfo(BaseModel):
    id: uuid.UUID
    agency: str
    has_player_in_club: bool
    has_propose_badge: bool
    has_uploaders_badge: bool
    has_all_rounder: bool

class CommunityTeamRequest(TeamRequestUpdateCommunity, ExtendedCommunityBase):
    team_info: CommunityTeamInfo
    created_by_info: List[CommunityInfo]
    count_duplicates: int

class TeamRequestExport(ExtendedUpdateBase):
    id: uuid.UUID
    creator: UserShort
    position: Position
    foot: FootPreference = FootPreference.no_prefence
    max_age: Optional[int]
    max_value: Optional[int]
    max_net_salary: Optional[int]
    transfer_period: Optional[List[TransferPeriod]]
    description: Optional[str]
    type: List[Union[Type, str]]
    team_info: TeamInfo
    source_to_record: Optional[List[SourceToRecord]]
    organization: Organization
    tracked_transfer: Optional[List[TrackedTransfer]]
    class Config:
        orm_mode = True

class TeamRequestShort(BaseModel):
    id: uuid.UUID
    team_info: TeamInfo
    class Config:
        orm_mode = True
        use_cache=True

class TeamRequestShortEmail(BaseModel):
    id: uuid.UUID
    team_info: TeamInfo
    position: Position

    class Config:
        orm_mode = True
        use_cache=True