"""Notification settings removed the extended

Revision ID: a5a1400d8f7f
Revises: d2732268f165
Create Date: 2023-07-07 12:57:47.738952

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a5a1400d8f7f'
down_revision = 'd2732268f165'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_crm_dev_notification_settings_created_at', table_name='notification_settings', schema='crm_dev')
    op.drop_index('ix_crm_dev_notification_settings_created_by', table_name='notification_settings', schema='crm_dev')
    op.drop_index('ix_crm_dev_notification_settings_is_sensitive', table_name='notification_settings', schema='crm_dev')
    op.drop_index('ix_crm_dev_notification_settings_last_updated', table_name='notification_settings', schema='crm_dev')
    op.drop_constraint('notification_settings_created_by_fkey', 'notification_settings', schema='crm_dev', type_='foreignkey')
    op.drop_column('notification_settings', 'notes', schema='crm_dev')
    op.drop_column('notification_settings', 'created_by', schema='crm_dev')
    op.drop_column('notification_settings', 'is_sensitive', schema='crm_dev')
    op.drop_column('notification_settings', 'last_updated', schema='crm_dev')
    op.drop_column('notification_settings', 'created_at', schema='crm_dev')
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    op.add_column('notification_settings', sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True), schema='crm_dev')
    op.add_column('notification_settings', sa.Column('last_updated', postgresql.TIMESTAMP(), autoincrement=False, nullable=True), schema='crm_dev')
    op.add_column('notification_settings', sa.Column('is_sensitive', sa.BOOLEAN(), autoincrement=False, nullable=True), schema='crm_dev')
    op.add_column('notification_settings', sa.Column('created_by', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm_dev')
    op.add_column('notification_settings', sa.Column('notes', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_dev')
    op.create_foreign_key('notification_settings_created_by_fkey', 'notification_settings', 'user', ['created_by'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.create_index('ix_crm_dev_notification_settings_last_updated', 'notification_settings', ['last_updated'], unique=False, schema='crm_dev')
    op.create_index('ix_crm_dev_notification_settings_is_sensitive', 'notification_settings', ['is_sensitive'], unique=False, schema='crm_dev')
    op.create_index('ix_crm_dev_notification_settings_created_by', 'notification_settings', ['created_by'], unique=False, schema='crm_dev')
    op.create_index('ix_crm_dev_notification_settings_created_at', 'notification_settings', ['created_at'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###