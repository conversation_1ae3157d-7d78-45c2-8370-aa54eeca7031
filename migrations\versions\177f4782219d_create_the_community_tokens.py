"""Create the community tokens

Revision ID: 177f4782219d
Revises: a9ab8601cc60
Create Date: 2024-09-20 10:57:00.803706

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '177f4782219d'
down_revision = 'a9ab8601cc60'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('community_tokens',
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('tokens', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.PrimaryKeyConstraint('organization_id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_community_tokens_organization_id'), 'community_tokens', ['organization_id'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_test_community_tokens_organization_id'), table_name='community_tokens', schema='crm_test')
    op.drop_table('community_tokens', schema='crm_test')
    # ### end Alembic commands ###