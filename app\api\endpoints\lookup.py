from typing import Any

import requests

from app import models
from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from app.api import deps
try:
    from ...config import settings
except:
    from app.config import settings

from app.utils import get_async_response



router = APIRouter()

@router.get("/players/lookup/{name}")
async def get_player_by_name(
    name: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve players by name.
    """
    name = name.replace(' ', '%20')
    # resp = requests.get(
    #         f"{settings.LOOKUP_API_URL}/players/lookup/{name}?qry_type=shortname_only&combine_short_full_name=True",
    #         auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
    #     )

    resp = await get_async_response(f"{settings.LOOKUP_API_URL}/players/lookup/{name}?qry_type=shortname_only&combine_short_full_name=True",
                                    auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS))
    
    return JSONResponse(status_code=resp.status_code, content=resp.json())

@router.get("/staff/lookup/{name}")
async def get_staff_by_name(
    name: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve staff by name.
    """
    
    resp = await get_async_response(f"{settings.LOOKUP_API_URL}/staff/lookup/{name}",
                                    auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS))
    
    return JSONResponse(status_code=resp.status_code, content=resp.json())

@router.get("/competitions/lookup/{name}")
async def get_competition_by_name(
    name: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve competitions by name.
    """
    # resp = requests.get(
    #         f"{settings.LOOKUP_API_URL}/competitions/lookup/{name}",
    #         auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
    #     )
    
    resp = await get_async_response(f"{settings.LOOKUP_API_URL}/competitions/lookup/{name}",
                                    auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS))
    
    return JSONResponse(status_code=resp.status_code, content=resp.json())


@router.get("/teams/{teamId}")
async def get_team_by_id(
    teamId: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve teams by Id.
    """

    # resp = requests.get(
    #         f"{settings.LOOKUP_API_URL}/teams/{teamId}",
    #         auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
    #     )

    resp = await get_async_response(f"{settings.LOOKUP_API_URL}/teams/{teamId}")
    return JSONResponse(status_code=resp.status_code, content=resp.json())

@router.get("/competitions/{competitionId}")
async def get_competition_by_id(
    competitionId: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve competition by Id.
    """

    # resp = requests.get(
    #         f"{settings.LOOKUP_API_URL}/competitions/{competitionId}",
    #         auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
    #     )

    resp = await get_async_response(f"{settings.LOOKUP_API_URL}/competitions/{competitionId}",
                                    auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS))
    return JSONResponse(status_code=resp.status_code, content=resp.json())

@router.get("/players/{playerId}")
async def get_player_by_id(
    playerId: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve player by Id.
    """

    # resp = requests.get(
    #         f"{settings.LOOKUP_API_URL}/players/{playerId}",
    #         auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
    #     )
    
    resp = await get_async_response(f"{settings.LOOKUP_API_URL}/players/{playerId}",
                                    auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS))
    return JSONResponse(status_code=resp.status_code, content=resp.json())

@router.get("/players_by_tm/{tmId}")
async def get_player_by_tm_id(
    tmId: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve player by Id.
    """

    # resp = requests.get(
    #         f"{settings.LOOKUP_API_URL}/players_by_tm/{int(tmId)}",
    #         auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
    #     )

    resp = await get_async_response(f"{settings.LOOKUP_API_URL}/players_by_tm/{int(tmId)}",
                                    auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS))

    return JSONResponse(status_code=resp.status_code, content=resp.json())

@router.get("/player_tm_info/{tmId}")
async def get_player_by_tm_id(
    tmId: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve player by Id.
    """

    # resp = requests.get(
    #         f"{settings.LOOKUP_API_URL}/player_tm_info/{int(tmId)}",
    #         auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
    #     )

    resp = await get_async_response(f"{settings.LOOKUP_API_URL}/player_tm_info/{int(tmId)}",
                                    auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS))

    return JSONResponse(status_code=resp.status_code, content=resp.json())


@router.get("/matches/lookup/{label}")
async def get_mattch_by_label(
    label: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve player by Id.
    """

    # resp = requests.get(
    #         f"{settings.LOOKUP_API_URL}/matches/lookup/{label}",
    #         auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
    #     )

    resp = await get_async_response(f"{settings.LOOKUP_API_URL}/matches/lookup/{label}",
                                    auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS))
    return JSONResponse(status_code=resp.status_code, content=resp.json())

@router.get("/player_matches/lookup/{id}")
async def get_match_by_player(
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve player by Id.
    """


    # resp = requests.get(
    #         f"{settings.LOOKUP_API_URL}/player_matches/lookup/{id}?source={settings.PG_SCHEMA}",
    #         auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
    #     )

    resp = await get_async_response(f"{settings.LOOKUP_API_URL}/player_matches/lookup/{id}?source={settings.PG_SCHEMA}",
                                    auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS))
    return JSONResponse(status_code=resp.status_code, content=resp.json())

#Greedy route, needs to be last
@router.get("/teams/lookup/{name:path}")
async def get_team_by_name(
    name: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve teams by name.
    """
    # resp = requests.get(
    #         f"{settings.LOOKUP_API_URL}/teams/lookup/{name}",
    #         auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
    #     )
    
    resp = await get_async_response(f"{settings.LOOKUP_API_URL}/teams/lookup/{name}",
                                    auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS))
    
    return JSONResponse(status_code=resp.status_code, content=resp.json())

@router.get("/teams/updated_lookup/{name}")
async def get_teams_updated_lookup(
    name: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve teams by name using updated lookup endpoint.
    """
    
    resp = await get_async_response(f"{settings.LOOKUP_API_URL}/teams/updated_lookup/{name}",
                                    auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS))
    
    return JSONResponse(status_code=resp.status_code, content=resp.json())
