"""Notification settings table added to init

Revision ID: 83395f896b5a
Revises: 3b97fa59fb68
Create Date: 2023-07-07 12:40:01.685544

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '83395f896b5a'
down_revision = '3b97fa59fb68'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###