from typing import Any, List, Dict
from sqlalchemy.orm import Session
import sqlalchemy
from app.db.session import engine
import pandas as pd

def fetch_ex_teammates(db: Session, input_tm_player_id: int) -> List[Dict[str, Any]]:
    results = []

    with engine.connect() as conn:
        with conn.begin():
            # Step 1: Build star's team stays (TEMP TABLE)
            conn.execute(sqlalchemy.text("""
                CREATE TEMP TABLE star_team_stays  ON COMMIT DROP AS
                SELECT
                    t.tm_player_id,
                    t.joined_id AS team_id,
                    t.joined_name AS team_name,
                    t.date AS start_date,
                    LEAD(t.date) OVER (PARTITION BY t.tm_player_id ORDER BY t.date) AS next_date
                FROM transfermarkt.tm_transfers t
                WHERE t.tm_player_id = :tm_player_id
            """), {"tm_player_id": input_tm_player_id})

            # Step 2: Create temp table of all shared team_ids
            conn.execute(sqlalchemy.text("""
                CREATE TEMP TABLE shared_team_ids ON COMMIT DROP AS
                SELECT DISTINCT team_id
                FROM star_team_stays
                WHERE team_id NOT IN (75, 515, 123)
            """))

            # Step 3: Everyone's relevant team stays (but scoped to relevant teams only)
            conn.execute(sqlalchemy.text("""
                CREATE TEMP TABLE everyone_team_stays ON COMMIT DROP AS
                SELECT
                    t.tm_player_id,
                    t.joined_id AS team_id,
                    t.joined_name AS team_name,
                    t.date AS start_date,
                    LEAD(t.date) OVER (PARTITION BY t.tm_player_id ORDER BY t.date) AS next_date
                FROM transfermarkt.tm_transfers t
                WHERE t.joined_id IN (SELECT team_id FROM shared_team_ids)
                  AND t.tm_player_id != :tm_player_id
            """), {"tm_player_id": input_tm_player_id})

            # Step 4: Now run the overlap + enrichment logic
            df = pd.read_sql(sqlalchemy.text("""
                WITH all_transfers AS (
                    SELECT DISTINCT ON (tm_player_id, date, joined_id)     -- de‑dupe same‑day noise
                        tm_player_id,
                        joined_id      AS team_id,
                        joined_name    AS team_name,
                        date           AS start_date,
                        LEAD(date) OVER (PARTITION BY tm_player_id ORDER BY date) AS next_date
                    FROM transfermarkt.tm_transfers
                ),

                /* 1. The star’s stints (real end = day before the next transfer) */
                star_stays AS (
                    SELECT
                        tm_player_id,
                        team_id,
                        team_name,
                        start_date,
                        COALESCE(next_date - INTERVAL '1 day', CURRENT_DATE) AS end_date
                    FROM all_transfers
                    WHERE tm_player_id = :tm_player_id
                ),

                /* 2. Everyone else’s stints – ONLY keep them *after* end_date is right */
                everyone_stays AS (
                    SELECT
                        tm_player_id,
                        team_id,
                        team_name,
                        start_date,
                        COALESCE(next_date - INTERVAL '1 day', CURRENT_DATE) AS end_date
                    FROM all_transfers
                    WHERE team_id IN (SELECT team_id FROM shared_team_ids)
                    AND tm_player_id <> :tm_player_id
                ),

                /* 3. Genuine overlaps */
                real_overlap AS (
                    SELECT
                        o.tm_player_id                       AS ex_tm_player_id,
                        o.team_id                            AS shared_team_id,
                        o.team_name                          AS shared_team_name,
                        GREATEST(s.start_date, o.start_date) AS overlap_start,
                        LEAST  (s.end_date ,  o.end_date)    AS overlap_end
                    FROM star_stays s
                    JOIN everyone_stays o USING (team_id)
                    WHERE s.start_date <= o.end_date         -- ← canonical two‑line test
                    AND o.start_date <= s.end_date
                ),

                /* 4. Latest shared stint per ex‑player */
                picked AS (
                    SELECT DISTINCT ON (ex_tm_player_id)
                        ex_tm_player_id,
                        shared_team_id,
                        shared_team_name,
                        overlap_start,
                        overlap_end
                    FROM real_overlap
                    ORDER BY ex_tm_player_id, overlap_start DESC
                ),

                /* 5. One “current club” row per player */
                last_transfer AS (
                    SELECT DISTINCT ON (tm_player_id)
                        tm_player_id,
                        team_id     AS current_team_id,
                        team_name   AS current_team_name
                    FROM all_transfers
                    ORDER BY tm_player_id, start_date DESC
                )

                /* 6. Final result */
                SELECT
                    p.ex_tm_player_id,
                    d.name                  AS player_name,
                    d.birth_date,
                    d.agent                 AS agency,
                    p.shared_team_id,
                    p.shared_team_name,
                    p.overlap_start as ex_start_date,
                    p.overlap_end as ex_end_date,
                    lt.current_team_id,
                    lt.current_team_name,
                    COALESCE(w."teamId", -1) AS ws_team_id,
                    d.league_country
                FROM picked p
                JOIN last_transfer lt          ON lt.tm_player_id = p.ex_tm_player_id
                LEFT JOIN transfermarkt.tm_to_ws_team_ids w
                    ON w.transfermarkt_team_id = lt.current_team_id
                JOIN transfermarkt.transfermarkt_data d
                    ON d.tm_player_id = p.ex_tm_player_id
                WHERE lt.current_team_id NOT IN (75, 515, 123)
                AND lt.current_team_id NOT IN (SELECT team_id FROM star_stays);
            """), conn, params={"tm_player_id": input_tm_player_id})

            results = df.to_dict(orient="records")
    return results