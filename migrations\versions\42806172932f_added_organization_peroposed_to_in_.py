"""Added organization peroposed to in community deal

Revision ID: 42806172932f
Revises: c5cb046e4bcd
Create Date: 2024-11-06 11:21:02.484416

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '42806172932f'
down_revision = 'c5cb046e4bcd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('community_deal', sa.Column('organization_proposed_to', sa.String(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('community_deal', 'organization_proposed_to', schema='crm_test')
    # ### end Alembic commands ###