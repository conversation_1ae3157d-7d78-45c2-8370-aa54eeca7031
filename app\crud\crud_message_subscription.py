from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.message_subscription import SubscriptionCreate, SubscriptionUpdate


class CRUDMessageSubscription(
    CRUDBase[models.MessageSubscription, SubscriptionCreate, SubscriptionUpdate]
):
    def get_by_user_id(self, db, *, user_id: str):
        return db.query(models.MessageSubscription).filter(models.MessageSubscription.user_id == user_id).all()
    
    def get_by_email(self, db, *, email: str):
        return db.query(models.MessageSubscription).filter(models.MessageSubscription.email == email).all()


message_subscription = CRUDMessageSubscription(models.MessageSubscription)
