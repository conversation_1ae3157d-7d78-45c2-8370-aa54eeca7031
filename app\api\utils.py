from typing import List, Type, Optional, Callable
from fastapi import HTT<PERSON>Exception

from app import models
from app.schemas.extended_base import ExtendedBase
from typing import Union, Any
from pydantic import BaseModel
from app.schemas.user import User
from app.schemas.activity import Activity
from app.config import settings
from app import crud
from app.utils import basic_new, pro_new
import urllib
import json
from jwt.algorithms import RSAAlgorithm
import jwt
import requests
from app.db.session import session_maker
import datetime


def delete_refresh_token(user_id):
    crud.firebase.remove(db= session_maker(), id=user_id)
    

def refresh_token(user_id):
        refresh_token = crud.firebase.get(db=session_maker(), id=user_id)
        refreshed_id_token = requests.post(settings.REFRESH_TOKEN_URL, data={'key': settings.FIREBASE_API_KEY, 'grant_type':'refresh_token', 'refresh_token': str(refresh_token.refresh_token)})
        refreshed_id_token = refreshed_id_token.json()

        return refreshed_id_token


def decode_firebase_jwt(idtoken):

    # idtoken = "<id token passed to server from firebase auth>"
    target_audience = settings.TARGET_AUDIENCE
    # this line updated 
    certificate_url = 'https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>'

    response = urllib.request.urlopen(certificate_url)
    certs = response.read()
    certs = json.loads(certs)

    kid = jwt.get_unverified_header(idtoken)['kid']

    key_json = [c for c in certs['keys'] if c['kid'] == kid]
    public_key = RSAAlgorithm.from_jwk(key_json[0])

    #will throw error if not valid
    user = jwt.decode(idtoken, public_key, algorithms='RS256',audience=target_audience, leeway=datetime.timedelta(hours=24))
    return user


def can_modify(item: ExtendedBase, user: User) -> bool:
    """Owner/full user can modify anything, limited user can modify only non-sensitive"""
    if user.role.name in (
        "owner",
        "user_full",
        "basic_paid",
        "owner_self_registered",
        "free_trial",
        "basic_new",
        "pro_new",
    ):
        return True
    if user.role.name in ["user_limited", "user_elevated", "user_elevated_community", "scout"]:
        return type(item).__name__ not in settings.SENSITIVE_RESOURCES
    if user.role.name == "self_registered_basic":
        return type(item).__name__ in ["PlayerRecord", "TeamRequest"]
    return False


def can_access_all(resource: Union[Type[ExtendedBase], Type[BaseModel]], user: User) -> bool:
    """Owner and full user can access anything, limited/external can access non-sensitive, viewer can only see players/requests"""
    if user.role.name in ("owner", "user_full","basic_paid","owner_self_registered", "free_trial",):
        return True
    if (
        user.role.name in ["user_limited", "user_elevated", "user_elevated_community", "scout"]
        and resource.__name__ not in settings.SENSITIVE_RESOURCES
    ):
        return True
    if user.role.name in ("user_external") and resource.__name__ in (
        "PlayerRecord",
        "TeamRequest",
        "Contact",
        'CommunityProposal',
        'Proposal',
    ):
        return True
    if user.role.name in ("self_registered_basic") and resource.__name__ in (
        "PlayerRecord",
        "TeamRequest",
        "Contact",
        "CommunityProposal"
    ):
        return True
    if user.role.name in ("basic_new") and resource.__name__ in basic_new:
        return True
    if user.role.name in ("pro_new") and resource.__name__ in pro_new:
        return True
    return user.role.name in ("viewer") and resource.__name__ in (
        "PlayerRecord",
        "TeamRequest",
    )



def can_access_one(item: ExtendedBase, user: User) -> bool:
    item_type_name = type(item).__name__
    if user.role.name in ("owner", "user_full","basic_paid","owner_self_registered", "free_trial",):
        return True
    if (
        user.role.name  == "user_limited"
        and item_type_name not in settings.SENSITIVE_RESOURCES
        and not item.is_sensitive
    ):
        return True
    if (
        user.role.name  in ("user_elevated", "user_elevated_community", "scout")
        and item_type_name not in settings.SENSITIVE_RESOURCES
    ):
        return True
    if user.role.name == 'basic_new' and item_type_name in basic_new:
        return True
    if user.role.name == 'pro_new' and item_type_name in pro_new:
        return True
    if (
        user.role.name  == "self_registered_basic"
        and item_type_name in ("PlayerRecord", "TeamRequest", "Contact")
        and not item.is_sensitive
    ):
        return True
    return (
        user.role.name in {"viewer", "user_external", "self_registered_basic"}
        and item_type_name in ("PlayerRecord", "TeamRequest")
        and not item.is_sensitive
    )


def can_delete(item: ExtendedBase, user: User) -> bool:
    """Owner/full user can delete anything, limited user can delete only their own"""
    if user.role.name in ("owner", "user_full","basic_paid","owner_self_registered", 'self_registered_basic', "free_trial", "basic_new", "pro_new",):
        return True
    if user.role.name in ["user_limited", 'user_external', "user_elevated_community", "user_elevated", "scout"]:
        return item.created_by == user.id
    return False


def can_create(resource: Type[ExtendedBase], user: User) -> bool:
    """Owner/full user can create anything, limited user can create non-sensitive
    external user can create only requests"""
    if user.role.name in ("owner", "user_full","basic_paid","owner_self_registered", "free_trial",):
        return True
    if user.role.name in ["user_limited", "user_elevated", "user_elevated_community", "scout"]:
        return resource.__name__ not in settings.SENSITIVE_RESOURCES
    if user.role.name == "user_external":
        return resource.__name__ in ["TeamRequest", 'Proposal']
    if user.role.name == 'basic_new':
        return resource.__name__ in basic_new
    if user.role.name == 'pro_new':
        return resource.__name__ in pro_new
    if user.role.name == "self_registered_basic":
        return resource.__name__ in ["TeamRequest", "PlayerRecord"]
    return False


def can_access_sensitive(user: User) -> bool:
    return user.role.name in ("owner", "user_full", "user_elevated","basic_paid", "owner_self_registered", "self_registered_basic", "user_elevated_community", "free_trial", "basic_new", "pro_new", "scout")


def check_create(resource: Type[ExtendedBase], user: User):
    if not can_create(resource, user):
        raise HTTPException(status_code=403, detail="Not authorized")


def check_get_all(resource: Union[Type[ExtendedBase], Type[BaseModel]], user: User):
    if not can_access_all(resource, user):
        raise HTTPException(status_code=403, detail="Not authorized")
    
def check_access_admin(user: User):
    if (user.email not in settings.ADMIN_EMAILS and not user.is_superuser):
        raise HTTPException(status_code=403, detail="Not authorized")

def check_get_one(item: ExtendedBase, user: User):
    if not item:
        raise HTTPException(status_code=404, detail="Not found")
    if not can_access_one(item, user):
        raise HTTPException(status_code=403, detail="Not authorized")


def check_modify(item: ExtendedBase, user: User):
    if not item:
        raise HTTPException(status_code=404, detail="Not found")
    if not can_modify(item, user):
        raise HTTPException(status_code=403, detail="Not authorized")

def check_module_access(user: User, module_key: str):
    """
    Raises 403 if the user does not have access to the given module.
    """
    user_modules = get_user_modules(user)
    if module_key not in user_modules:
        raise HTTPException(status_code=403, detail="Not authorized for this module")

def check_delete(item: ExtendedBase, user: User):
    if not item:
        raise HTTPException(status_code=404, detail="Not found")
    if not can_delete(item, user):
        raise HTTPException(status_code=403, detail="Not authorized")


def compute_accessible_modules(role: str, org_mods: List[str]) -> List[str]:
    if role in {"self_registered_basic"}:
      return ["core", "self"]
    if role in {"owner_self_registered", "basic_paid"}:
        org_mods.append("self")
        return [x for x in org_mods if x not in settings.SENSITIVE_MODULES]
    if role in {"free_trial"}:
        org_mods.append('self')
        return org_mods
    if role in {"basic_new", "pro_new"}:
        org_mods.append('self')
        return org_mods
    if role in {"owner", "user_full"}:
        return org_mods
    if role in {"user_limited", "user_external", "user_elevated"}:
        return [x for x in org_mods if x not in settings.SENSITIVE_MODULES]
    if role in {"user_elevated_community", "scout"}:
        modules = [x for x in org_mods if x not in settings.SENSITIVE_MODULES]
        if "community_requests" in org_mods:
            modules.append('community_requests')
        return modules
    return ["core"]

def get_user_modules(user: models.User) -> List[str]:
    role = user.role.name
    org_mods = list(user.organization.modules)
    org_mods_names = [x.module.name for x in org_mods]
    return compute_accessible_modules(role, org_mods_names)

def check_same_org(item: ExtendedBase, item_s:ExtendedBase, user: User):

    if item_s:
        if str(item.organization_id) != str(user.organization_id) or str(item_s.organization_id) != str(user.organization_id):
            raise HTTPException(status_code=403, detail="Not authorized")
    elif str(item.organization_id) != str(user.organization_id):
        raise HTTPException(status_code=403, detail="Not authorized")

def is_assigned_to_user(item: Any, user: User) -> bool:
    assigned_list = getattr(item, "assigned_to_record", None) or []
    user_id_str = str(user.id)

    # If it's a string (from raw SQL JSON), parse it
    if isinstance(assigned_list, str):
        import json
        try:
            assigned_list = json.loads(assigned_list)
        except Exception as e:
            print("Failed to parse assigned_to_record JSON:", e)
            assigned_list = []

    # Now handle both dicts and objects
    for assigned in assigned_list:
        if isinstance(assigned, dict):
            contact_id = assigned.get("contact_id")
        else:  # It's likely an ORM object
            contact_id = getattr(assigned, "contact_id", None)

        if str(contact_id) == user_id_str:
            return True

    # Check created_by
    created_by = (
        item.get("created_by")
        if isinstance(item, dict)
        else getattr(item, "created_by", None)
    )
    return str(created_by) == user_id_str
    
def exclude_based_on_role(user: User) -> Optional[Callable[[ExtendedBase, User], bool]]:
    role_name = user.role.name
    if role_name in ("scout"):
        return is_assigned_to_user
    return None
