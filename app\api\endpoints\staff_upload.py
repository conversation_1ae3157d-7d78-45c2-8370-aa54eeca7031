import traceback
import uuid
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Response
from sqlalchemy.orm import Session

from app.schemas.staff_upload import StaffUpload, StaffUploadCreate
from app import crud, models
from app.api import deps, utils
from app.utils.cloud_storage import CloudStorageManager

router = APIRouter()


@router.get("/file_info/", response_model=List[StaffUpload])
def read_staff_uploads(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all files' info.
    """
    utils.check_get_all(StaffUpload, current_user)
    return crud.staff_record.get_all_w_org(
        db, current_user.organization_id, utils.can_access_sensitive(current_user)
    )


@router.get("/file_info/{id}", response_model=StaffUpload)
def get_file_info(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get file info by ID.
    """
    staff_upload = crud.staff_upload.get(db=db, id=id)
    utils.check_get_one(staff_upload, current_user)
    return staff_upload

@router.get("/files_info/{staff_id}", response_model=List[StaffUpload])
def get_files_for_a_staff(
    *,
    db: Session = Depends(deps.get_db),
    staff_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get all files for a specific staff.
    """
    staff_uploads = crud.staff_upload.get_files_for_staff(db, staff_id, current_user.organization_id)
    utils.check_get_all(staff_uploads, current_user)
    return staff_uploads

@router.get(
    "/download/{id}",
)
def download_file(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get contact by ID.
    """
    # first check if user can actually access the staff file:
    staff_upload = crud.staff_upload.get(db=db, id=id)
    utils.check_get_one(staff_upload, current_user)
    # if all is ok, download file
    cm = CloudStorageManager()
    media_type = {
        "jpg": "image/jpeg",
        "jpeg": "image/jpeg",
        "png": "image/png",
        "pdf": "application/pdf",
    }.get(id.split(".")[-1])
    return Response(cm.get_blob(id), media_type=media_type)


@router.post(
    "/bulk/{staff_id}",
    response_description="Upload multiple files at once",
    response_model=List[StaffUpload],
)
def upload_bulk_files(
    *, 
    db: Session = Depends(deps.get_db),
    staff_id: str,
    files: List[UploadFile] = File(),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    utils.check_create(StaffUpload, current_user)
    files_for_writing = {}
    for f in files:
            ext = f.filename.split(".")[-1]
            if ext not in {"pdf", "png", "jpeg", "jpg", "docx", 'xlsx', 'csv'}:
                raise HTTPException(415, "Unsupported media type")
            filename = f"{str(uuid.uuid4())}.{ext}"
            files_for_writing[filename] = (StaffUploadCreate(staff_id=staff_id, id=filename, name=f.filename.split(".")[0]), f)
    written_files = crud.staff_upload.bulk_create_with_user(
        db=db, objs_in=[x[0] for x in files_for_writing.values()], user=current_user
    )
    try:
        cm = CloudStorageManager()
        for k, v in files_for_writing.items():
            cm.upload_blob(k, v[1].file)
        return written_files
    except:
        for k in files_for_writing:
            crud.staff_upload.remove(db=db, id=k)

        raise HTTPException(500, "Failed to upload document")


@router.delete("/{id}", response_model=StaffUpload)
def delete_staff_upload(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a staff file.
    """
    staff_upload = crud.staff_upload.get(db=db, id=id)
    utils.check_delete(staff_upload, current_user)
    staff_upload_out = StaffUpload.from_orm(staff_upload)
    crud.staff_upload.remove(db=db, id=id)
    try:
        cm = CloudStorageManager()
        cm.delete_blob(staff_upload.id)
        return staff_upload_out
    except:
        staff_upload = crud.staff_upload.create_with_user(
            db=db,
            obj_in=staff_upload_out,
            user=current_user,
        )
        traceback.format_exc()
        raise HTTPException(500, "Failed to delete document")