"""add is_sensitive bool col to extendedbasemixin

Revision ID: b951aea6a61e
Revises: 5537db89b4fe
Create Date: 2022-08-17 14:03:53.074315

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b951aea6a61e'
down_revision = '5537db89b4fe'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_crm_dev_type_temp_index', table_name='type_temp', schema='crm_dev')
    op.drop_table('type_temp', schema='crm_dev')
    op.add_column('contacts', sa.Column('is_sensitive', sa.<PERSON>an(), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contacts_is_sensitive'), 'contacts', ['is_sensitive'], unique=False, schema='crm_dev')
    op.add_column('player_records', sa.Column('is_sensitive', sa.<PERSON>(), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_player_records_is_sensitive'), 'player_records', ['is_sensitive'], unique=False, schema='crm_dev')
    op.add_column('proposals', sa.Column('is_sensitive', sa.Boolean(), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_proposals_is_sensitive'), 'proposals', ['is_sensitive'], unique=False, schema='crm_dev')
    op.add_column('reports', sa.Column('is_sensitive', sa.Boolean(), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_reports_is_sensitive'), 'reports', ['is_sensitive'], unique=False, schema='crm_dev')
    op.add_column('team_requests', sa.Column('is_sensitive', sa.Boolean(), nullable=True), schema='crm_dev')
    op.alter_column('team_requests', 'type',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               type_=sa.ARRAY(sa.String()),
               existing_nullable=True,
               schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_team_requests_is_sensitive'), 'team_requests', ['is_sensitive'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_dev_team_requests_is_sensitive'), table_name='team_requests', schema='crm_dev')
    op.alter_column('team_requests', 'type',
               existing_type=sa.ARRAY(sa.String()),
               type_=postgresql.ARRAY(sa.TEXT()),
               existing_nullable=True,
               schema='crm_dev')
    op.drop_column('team_requests', 'is_sensitive', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_reports_is_sensitive'), table_name='reports', schema='crm_dev')
    op.drop_column('reports', 'is_sensitive', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_proposals_is_sensitive'), table_name='proposals', schema='crm_dev')
    op.drop_column('proposals', 'is_sensitive', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_player_records_is_sensitive'), table_name='player_records', schema='crm_dev')
    op.drop_column('player_records', 'is_sensitive', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contacts_is_sensitive'), table_name='contacts', schema='crm_dev')
    op.drop_column('contacts', 'is_sensitive', schema='crm_dev')
    op.create_table('type_temp',
    sa.Column('index', sa.BIGINT(), autoincrement=False, nullable=True),
    sa.Column('id', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('type', sa.TEXT(), autoincrement=False, nullable=True),
    schema='crm_dev'
    )
    op.create_index('ix_crm_dev_type_temp_index', 'type_temp', ['index'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###