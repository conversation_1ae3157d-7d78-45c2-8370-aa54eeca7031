"""Changed transfer_period to strategy - sell/loan/stay

Revision ID: b19144235cc5
Revises: 589b832f94c8
Create Date: 2024-08-19 14:07:14.857937

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b19144235cc5'
down_revision = '589b832f94c8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('transfer_strategy', sa.String(), nullable=True), schema='crm_test')
    op.drop_column('player_records', 'transfer_period', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('transfer_period', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True), schema='crm_test')
    op.drop_column('player_records', 'transfer_strategy', schema='crm_test')
    # ### end Alembic commands ###