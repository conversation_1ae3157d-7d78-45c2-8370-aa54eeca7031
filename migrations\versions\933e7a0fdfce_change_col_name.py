"""change col name

Revision ID: 933e7a0fdfce
Revises: 661ee4d8f19c
Create Date: 2022-09-22 11:32:42.780383

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '933e7a0fdfce'
down_revision = '661ee4d8f19c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contracts', sa.Column('contract_type', sa.String(length=50), nullable=True), schema='crm_dev')
    op.drop_column('contracts', 'type', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contracts', sa.Column('type', sa.VARCHAR(length=50), autoincrement=False, nullable=True), schema='crm_dev')
    op.drop_column('contracts', 'contract_type', schema='crm_dev')
    # ### end Alembic commands ###