from datetime import datetime
import uuid
from typing import Any
from pydantic import (
    BaseModel,
    Field,
)

from app.schemas.user import UserShort


class ChangeCreate(BaseModel):
    edit_at: datetime = Field(default_factory=datetime.now)
    edit_by: uuid.UUID
    field: str
    previous: Any
    updated: Any

    def __repr__(self):
        return f"{self.field}, {self.previous} - {self.updated}"

    class Config:
        orm_mode = True


class Change(ChangeCreate):
    id: uuid.UUID
    edit_at: datetime
    edit_by: uuid.UUID
    editor: UserShort
    field: str
    previous: Any
    updated: Any

    class Config:
        orm_mode = True
        use_cache=True