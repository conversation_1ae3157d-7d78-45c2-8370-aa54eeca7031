import uuid
from sqlalchemy import Column, Foreign<PERSON>ey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.config import settings


class SourceToRecord(Base):
    __tablename__ = "source_to_record"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    source_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.contacts.id", ondelete='CASCADE'),
        index=True,
    )
    source = relationship(
        "Contact", primaryjoin='(SourceToRecord.source_id==Contact.id)'
    )

    player_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.player_records.id"),
        index=True,
    )
    team_request_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.team_requests.id"),
        index=True,
    )
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)