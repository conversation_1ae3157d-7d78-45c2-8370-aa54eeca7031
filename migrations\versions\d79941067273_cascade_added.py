"""cascade added

Revision ID: d79941067273
Revises: 19e944544200
Create Date: 2023-06-01 11:06:46.566995

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd79941067273'
down_revision = '19e944544200'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('player_records', 'playerId',
               existing_type=sa.BIGINT(),
               type_=sa.Integer(),
               existing_nullable=True,
               schema='crm')
    op.drop_constraint('source_to_record_source_id_fkey', 'source_to_record', schema='crm', type_='foreignkey')
    op.create_foreign_key(None, 'source_to_record', 'contacts', ['source_id'], ['id'], source_schema='crm', referent_schema='crm', ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'source_to_record', schema='crm', type_='foreignkey')
    op.create_foreign_key('source_to_record_source_id_fkey', 'source_to_record', 'contacts', ['source_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.alter_column('player_records', 'playerId',
               existing_type=sa.Integer(),
               type_=sa.BIGINT(),
               existing_nullable=True,
               schema='crm')
    # ### end Alembic commands ###