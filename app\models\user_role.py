import uuid

from sqlalchemy.orm import relationship
from sqlalchemy import Column, String
from sqlalchemy.dialects.postgresql import UUID

from app.db.base_class import Base
from app.config import settings


class UserRole(Base):
    __tablename__ = "user_roles"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, unique=True)
    description = Column(String)
    users = relationship("User", back_populates="role")
    user_roles_default = relationship("UserDefaultRoles", back_populates="role")
