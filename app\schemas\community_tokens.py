import uuid
from typing import Optional
from pydantic import BaseModel
class CommunityTokensUpdate(BaseModel):
    tokens: Optional[int]

    class Config:
        orm_mode = True

class CommunityTokensCreate(CommunityTokensUpdate):
    tokens: int
    organization_id: Optional[uuid.UUID]

    class Config:
        orm_mode = True

class CommunityTokens(CommunityTokensCreate):
    ...

    class Config:
        orm_mode = True