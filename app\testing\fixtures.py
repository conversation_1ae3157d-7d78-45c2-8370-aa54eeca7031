import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.models.user import User
from app.models.user_role import UserRole
from app.api.endpoints.users.auth import current_active_user as get_current_active_user



current = User(email = "<EMAIL>",
    id = '16999f50-e977-431b-8d1e-2d8c87ce4f9c',
 role_id = '00e55bab-3688-4bb7-8ac5-85263fd769f2',
 organization_id='ebfdb98e-9af4-4ba0-a437-4ce1997132e1',
  is_enabled = True, role=UserRole(name='owner', id='00e55bab-3688-4bb7-8ac5-85263fd769f2'
  ))

chestnite = User(email = "<EMAIL>",
    id = 'da5e925b-fae1-4a97-b7bb-390f216baed3',
 role_id = 'da5e925b-fae1-4a97-b7bb-390f216baeda',
 organization_id='ebfdb98e-9af4-4ba0-a437-4ce1997132e9',
  is_enabled = True, role=UserRole(name='user_full', id='da5e925b-fae1-4a97-b7bb-390f216baeda'
  ))

@pytest.fixture(name="test_org")
def get_test_org():
    return "test_org"

@pytest.fixture(name="test_user")
def get_test_user_email():
    return "<EMAIL>"

@pytest.fixture(name="test_pass")
def get_test_user_password():
    return "test_pass"

@pytest.fixture(name="current_user_enskai_owner")  # 
def get_current_user_enskai_owner():  # 
    yield current

@pytest.fixture(name="current_user_chesnite_limited")  # 
def get_current_user_chesnite_limited():  # 
    yield chestnite

@pytest.fixture(name="client_enskai")  # 
def client_enskai(current_user_enskai_owner):  # 
    def get_user_override():
        return current_user_enskai_owner
    
    app.dependency_overrides[get_current_active_user] = get_user_override

    client = TestClient(app)  # 
    yield (client, 'enskai')  # 
    app.dependency_overrides.clear()

@pytest.fixture(name="client_chestnite")  # 
def client_chestnite(current_user_chesnite_limited):  # 
    def get_user_override():
        return current_user_chesnite_limited
    
    app.dependency_overrides[get_current_active_user] = get_user_override

    client = TestClient(app)  # 
    yield (client, 'chestnite')  # 
    app.dependency_overrides.clear()

@pytest.fixture(name="switch_between_users")
def switch_between_users(request):
    return request.getfixturevalue(request.param)

@pytest.fixture(name="unauth_client")
def unauth_client():
    """
    Return an API Client
    """
    app.dependency_overrides = {}
    return TestClient(app)

@pytest.fixture(name="auth_client")
def auth_client():
    """
    Returns an API client which skips the authentication
    """
    def skip_auth():
        yield current
    app.dependency_overrides[get_current_active_user] = skip_auth
    return TestClient(app)

@pytest.fixture(name="rank_params")
def rank_params():
    # params = dict()
    # params['rank_params'] = dict()
    # params['rank_record'] = dict()

    # # Rank params
    # params['rank_params']['target_position'] = 'light_cf'
    # params['rank_params']['comparable_positions'] = ['cf', 'ss']

    # str_vars = dict()
    # str_vars['shots_str'] = 'activity'
    
    # params['rank_params']['str_vars'] = f'{{{json.dumps(str_vars)}}}'

    # var_wts = dict()
    # var_wts['shot_assists_reg'] = 2
    # var_wts['progressive_runs_reg'] = 2
    # var_wts['shots_str'] = 4
    # var_wts['xgassists_reg'] = 2
    # var_wts['xgshots_reg'] = 4
    # params['rank_params']['var_weights'] = f'{{{json.dumps(var_wts)}}}'

    # params['rank_params']['min_num_games'] = 10
    # params['rank_params']['max_num_games'] = None
    # params['rank_params']['min_pct_played'] = 75
    # params['rank_params']['min_tag_threshold'] = 10
    # params['rank_params']['time_decay_coef'] = 1
    # params['rank_params']['window_start'] = '2021-12-21'
    # params['rank_params']['window_end'] = '2022-12-21'
    # params['rank_params']['filter_competitions'] = None
    # params['rank_params']['min_scale_log_base'] = 2
    # params['rank_params']['youth_leagues'] = False
    # params['rank_params']['game_quality_scaling'] = None
    # params['rank_params']['competition_scaling'] = 'root3'
    # params['rank_params']['game_difficulty_scaling'] = None

    # # Rank records
    # params['rank_record']['name'] = None
    # params['rank_record']['target_position'] = 'light_cf'
    # params['rank_record']['description'] = None
    # params['rank_record']['for_who'] = None
    # params['rank_record']['purpose'] = None
    # params['rank_record']['youth'] = False
    # params['rank_record']['default_quality'] = False

    rank_params = r'{"rank_params":{"target_position":"light_cf","comparable_positions":["cf"],"str_vars":"{\"shots_str\":\"activity\"}","var_weights":"{\"shot_assists_reg\":2,\"progressive_runs_reg\":2,\"shots_str\":4,\"xgassists_reg\":2,\"xgshots_reg\":4}","min_num_games":10,"max_num_games":null,"min_pct_played":75,"min_tag_threshold":10,"time_decay_coef":1,"window_start":"2021-12-21","window_end":"2022-12-21","filter_competitions":null,"min_scale_log_base":2,"youth_leagues":false,"game_quality_scaling":null,"competition_scaling":"root3","game_difficulty_scaling":null},"rank_record":{"name":null,"description":null,"for_who":null,"purpose":null,"youth":false,"target_position":"light_cf","default_quality":false}}'
    
    

    return rank_params