from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.schemas.message_subscription import SubscriptionCreate, Subscription, SubscriptionUpdate
from app import crud
from app.api import deps

router = APIRouter()
@router.post("/", response_model=Subscription)
async def create_subscription(
    *,
    db: Session = Depends(deps.get_db),
    subscription_in: SubscriptionCreate,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new subscription for a user to push notifications.
    """
    subscription_in.email = current_user.email
    subscription = crud.message_subscription.create(
        db=db,
        obj_in=subscription_in,
    )

    return subscription

@router.put("/{user_id}", response_model=Subscription)
async def update_subscription(
    *,
    user_id: str,
    db: Session = Depends(deps.get_db),
    subscription_in: SubscriptionUpdate,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update subscription for a user.
    """
    subscription_in.email = current_user.email
    subscription_old = crud.message_subscription.get_by_user_id(db=db, user_id=user_id)[0]
    subscription = crud.message_subscription.update(
        db=db,
        db_obj=subscription_old,
        obj_in=subscription_in,
    )

    return subscription

@router.get("/{user_id}", response_model=list[Subscription])
async def read_subscriptions(
    user_id: str,
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Get all subscriptions for a user.
    """
    subscriptions = crud.message_subscription.get_by_user_id(db=db, user_id=user_id)
    return subscriptions


@router.get("/{email}", response_model=list[Subscription])
async def read_subscriptions_email(
    email: str,
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Get all subscriptions for a user.
    """
    subscriptions = crud.message_subscription.get_by_email(db=db, email=email)
    return subscriptions