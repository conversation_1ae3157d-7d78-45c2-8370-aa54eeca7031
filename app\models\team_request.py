from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from app.models.extended_base_mixin import ExtendedBaseMixin
from app.db.base_class import Base
from app.config import settings


class TeamRequest(Base, ExtendedBaseMixin):
    __tablename__ = "team_requests"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    teamId = Column(
        Integer,
        ForeignKey("wyscout.team_info2.teamId"),
        index=True,
    )
    team_info = relationship("TeamInfo", viewonly=True, primaryjoin='foreign(TeamRequest.teamId)==remote(TeamInfo.teamId)',)
    position = Column(String)
    foot = Column(String)
    max_age = Column(Integer)
    max_value = Column(Integer)
    max_net_salary = Column(Integer)
    transfer_period = Column(ARRAY(String))
    source_to_record = relationship(
        "SourceToRecord", primaryjoin='TeamRequest.id==SourceToRecord.team_request_id',
        cascade="all, delete-orphan"
    )
    description = Column(String)
    proposed_players = relationship("Proposal", back_populates="team_request", cascade="all, delete-orphan", lazy="noload")
    type = Column(ARRAY(String))
    organization_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.organizations.id"),
        index=True,
    )
    is_community = Column(Boolean)
    changelog = relationship(
        "TeamRequestChange", lazy="noload"
    )
    organization = relationship("Organization", back_populates="team_requests")
    tracked_transfer = relationship("TrackedTransfers",primaryjoin='TeamRequest.id==TrackedTransfers.request_id' ,cascade="all, delete-orphan")
    status = Column(String)
    club_contact = Column(String)
    eu_passport = Column(Boolean)