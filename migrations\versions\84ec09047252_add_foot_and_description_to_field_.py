"""Add foot and description to field players

Revision ID: 84ec09047252
Revises: fbb4a0a1b083
Create Date: 2025-05-21 18:27:15.361320

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '84ec09047252'
down_revision = 'fbb4a0a1b083'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_players', sa.Column('foot', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('field_players', sa.Column('description', sa.String(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('field_players', 'description', schema='crm_dev')
    op.drop_column('field_players', 'foot', schema='crm_dev')
    # ### end Alembic commands ###