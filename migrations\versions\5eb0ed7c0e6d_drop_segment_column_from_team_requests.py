"""drop segment column from team_requests

Revision ID: 5eb0ed7c0e6d
Revises: 13ec7c522589
Create Date: 2022-09-07 13:47:22.150567

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5eb0ed7c0e6d'
down_revision = '13ec7c522589'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('team_requests', 'segment', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('team_requests', sa.Column('segment', sa.INTEGER(), autoincrement=False, nullable=True), schema='crm_dev')
    # ### end Alembic commands ###