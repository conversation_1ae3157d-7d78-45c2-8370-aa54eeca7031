import uuid
from typing import TYPE_CHECKING
from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)

if TYPE_CHECKING:
    from app.schemas.contract import Contract


class ContractUploadUpdate(ExtendedUpdateBase):
    ...


class ContractUploadCreate(ExtendedCreateBase):
    contract_id: uuid.UUID
    id: str
    name: str


class ContractUpload(ContractUploadCreate, ExtendedBase):
    ...

    class Config:
        orm_mode = True
        use_cache = True