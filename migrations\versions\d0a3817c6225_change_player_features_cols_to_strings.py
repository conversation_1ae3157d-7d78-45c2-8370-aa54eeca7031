"""change player features cols to strings

Revision ID: d0a3817c6225
Revises: 4a06e1ba9525
Create Date: 2022-10-10 15:52:11.268777

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd0a3817c6225'
down_revision = '4a06e1ba9525'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('player_features', 'speed',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'explosive',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'dynamic',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'build',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'intelligence',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'scanning',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'pressing',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'aggresive',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'leader',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'teamwork',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'education',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'entourage',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'first_touch',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'dribble',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'progress_with_the_ball',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'play_with_the_back',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'diagonal_passing',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'through_ball_passing',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'passes_to_final_third',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'pushes_inbetween_lines',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'defensive_positioning',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'attacking_positioning',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'interceptions',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'aerial_duels',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'finishing',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'heading',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'cut_inside',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'gk_1_vs_one_duels',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'defensive_1_vs_one_duels',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'closing_space',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'ball_control',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'reflexes',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'set_pieces',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('player_features', 'set_pieces',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'reflexes',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'ball_control',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'closing_space',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'defensive_1_vs_one_duels',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'gk_1_vs_one_duels',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'cut_inside',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'heading',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'finishing',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'aerial_duels',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'interceptions',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'attacking_positioning',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'defensive_positioning',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'pushes_inbetween_lines',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'passes_to_final_third',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'through_ball_passing',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'diagonal_passing',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'play_with_the_back',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'progress_with_the_ball',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'dribble',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'first_touch',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'entourage',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'education',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'teamwork',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'leader',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'aggresive',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'pressing',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'scanning',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'intelligence',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'build',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'dynamic',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'explosive',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_features', 'speed',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_dev')
    # ### end Alembic commands ###