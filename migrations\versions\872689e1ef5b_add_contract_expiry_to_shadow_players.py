"""Add contract expiry to shadow players

Revision ID: 872689e1ef5b
Revises: 837d13e899df
Create Date: 2025-05-15 17:13:51.607296

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '872689e1ef5b'
down_revision = '837d13e899df'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_players', sa.Column('contract_expiry', sa.DateTime(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('field_players', 'contract_expiry', schema='crm_dev')
    # ### end Alembic commands ###