"""Create default user roles table

Revision ID: 19fc322edac9
Revises: 0c7b31c151e3
Create Date: 2024-04-05 15:01:43.781589

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '19fc322edac9'
down_revision = '0c7b31c151e3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_roles_default',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('role_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['role_id'], ['crm_test.user_roles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['crm_test.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_roles_default', schema='crm_test')
    # ### end Alembic commands ###