from fastapi.testclient import TestClient
import json
from app.main import app
from app.testing.fixtures import (switch_between_users,
 client_chestnite,
  client_enskai,
  get_current_user_enskai_owner,
  get_current_user_chesnite_limited)
import pytest

client = TestClient(app)

contact_dict = {'contact_id_enskai': None, 'created_contact_obj_enskai': None,
'contact_id_chestnite': None, 'created_contact_obj_chestnite': None}

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_read_contacts(switch_between_users: TestClient):
    client, org = switch_between_users
    response = client.get("contacts/")
    data = response.json()
    assert data is not None, "Response returned null data"
    
    if org == 'enskai':
        first_data = data[0]
        assert "contact_organization" in first_data, "contact_organization is not in the returned contacts"
        assert "contact_type" in first_data, "contact_type is not in the returned contacts"
        assert "created_by" in first_data, "created_by is not in the returned contacts"
        assert "created_at" in first_data, "created_at is not in the returned contacts"
    else:
        assert len(data) == 0

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_create_contact(switch_between_users: TestClient):
    client, org = switch_between_users
    data = json.dumps({
  "last_updated": "2022-11-22T12:18:28.577Z",
  "notes": "test-notes",
  "is_sensitive": False,
  "created_at": "2022-11-22T12:18:28.577Z",
  "first_name": "testovo",
  "last_name": "kjhjkh",
  "contact_type": "internal",
  "title": "string",
  "email": "string",
  "contact_organization": "EnskAI",
  "phone_number": "string",
})
    response = client.post('contacts/', data=data, headers={"Content-Type": "application/json"})
    assert response.status_code == 200, f"Request failed, status code is {response.status_code}"
    reponse_obj = response.json()
    
    contact_dict[f"created_contact_obj_{org}"] = reponse_obj
    contact_dict[f"contact_id_{org}"] = reponse_obj['id']
    assert contact_dict[f"contact_id_{org}"] is not None, "Contact id is None"
    
@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_get_specific_contact(switch_between_users: TestClient):
    client, org = switch_between_users

    response = client.get(f"""contacts/{contact_dict[f"contact_id_{org}"]}""")
    assert response.status_code == 200, f"Request failed, status code is {response.status_code}"
    returned_obj = response.json()
    assert returned_obj == contact_dict[f"created_contact_obj_{org}"], "The returned object should be equal to the just created one"
    
    non_existent_id_response = client.get("contacts/9f6b7234-6a6d-11ed-a1eb-0242ac120002")
    assert non_existent_id_response.status_code == 404, "Maybe we have this id in the DB -_-"

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_edit_specific_contact(switch_between_users: TestClient):
    client, org = switch_between_users
    data = json.dumps({  "last_updated": "2022-11-22T12:18:28.577Z",
  "notes": "test-notes",
  "is_sensitive": True,
  "created_at": "2022-11-22T12:18:28.577Z",
  "first_name": "testovo",
  "last_name": "kjhjkh",
  "contact_type": "internal",
  "title": "shefa",
  "email": "<EMAIL>",
  "contact_organization": "EnskAI",
  "phone_number": "string"})

    response = client.put(f"""contacts/{contact_dict[f"contact_id_{org}"]}""", data=data, headers={"Content-Type": "application/json"})
    assert response.status_code == 200,  f"Request failed, status code is {response.status_code}"
    response_obj = response.json()
    assert response_obj != contact_dict[f"created_contact_obj_{org}"], "Put request should have made updates"
    assert response_obj['title'] == 'shefa', "Title is not changed to 'shefa'"
    assert response_obj['email'] == '<EMAIL>', "Mail is not changed, but it should be"

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_delete_specific_contact(switch_between_users: TestClient):
    client, org = switch_between_users

    response = client.delete(f"""contacts/{contact_dict[f"contact_id_{org}"]}""")
    assert response.status_code == 200,  f"Request failed, status code is {response.status_code}"
    response = client.get(f"""contacts/{contact_dict[f"contact_id_{org}"]}""")
    assert response.status_code == 404, "Id should be deleted, but apperantly it isn't"