from sqlalchemy import Column, String, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from app.db.base_class import Base
from sqlalchemy.orm import relationship
from app.config import settings
import uuid
from datetime import datetime

class Comment(Base):
    __tablename__ = "comments_activity"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    comment = Column(String)
    time = Column(DateTime, index=True, default=datetime.now)
    creator = Column(String)
    activity_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.activity.id"),
        index=True,
    )
    player_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.player_records.id"),
        index=True,
    )
    staff_id = Column(        
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.staff_records.id"),
        index=True,)
    
    player = relationship(
        "PlayerRecord", back_populates="comments", foreign_keys=[player_id]
    )
    acitvity = relationship(
        "Activity", back_populates="comments", foreign_keys=[activity_id]
    )
    staff = relationship(
        "StaffRecord", back_populates="comments", foreign_keys=[staff_id]
    )