"""Add contract expiry to shadow players

Revision ID: 837d13e899df
Revises: a9c0d044c6ac
Create Date: 2025-05-15 14:34:34.434837

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '837d13e899df'
down_revision = 'a9c0d044c6ac'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_players', sa.Column('contract_expiry', sa.DateTime(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('field_players', 'contract_expiry', schema='crm_test')
    # ### end Alembic commands ###