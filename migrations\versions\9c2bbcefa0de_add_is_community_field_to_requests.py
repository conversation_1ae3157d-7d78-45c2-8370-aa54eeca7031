"""Add is_community field to requests

Revision ID: 9c2bbcefa0de
Revises: f74042b5b4b0
Create Date: 2023-07-10 13:36:50.828898

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '9c2bbcefa0de'
down_revision = 'f74042b5b4b0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=True,
               schema='crm_dev')
    op.add_column('team_requests', sa.Column('is_community', sa.<PERSON>(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('team_requests', 'is_community', schema='crm_dev')
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    # ### end Alembic commands ###