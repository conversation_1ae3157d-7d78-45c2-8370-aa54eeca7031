"""Comment is now separate table only for activity

Revision ID: 811bcec92380
Revises: ca8af3dd0c7e
Create Date: 2024-08-12 15:45:32.963769

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '811bcec92380'
down_revision = 'ca8af3dd0c7e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('comments_activity',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('comment', sa.String(), nullable=True),
    sa.Column('time', sa.DateTime(), nullable=True),
    sa.Column('creator', sa.String(), nullable=True),
    sa.Column('activity_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['activity_id'], ['crm_test.activity.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_comments_activity_activity_id'), 'comments_activity', ['activity_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_comments_activity_time'), 'comments_activity', ['time'], unique=False, schema='crm_test')
    op.drop_index('ix_crm_test_activity_comment_id', table_name='activity', schema='crm_test')
    op.drop_constraint('activity_comment_id_fkey', 'activity', schema='crm_test', type_='foreignkey')
    op.drop_column('activity', 'comment_id', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('activity', sa.Column('comment_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm_test')
    op.create_foreign_key('activity_comment_id_fkey', 'activity', 'comments', ['comment_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    op.create_index('ix_crm_test_activity_comment_id', 'activity', ['comment_id'], unique=False, schema='crm_test')
    op.drop_index(op.f('ix_crm_test_comments_activity_time'), table_name='comments_activity', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_comments_activity_activity_id'), table_name='comments_activity', schema='crm_test')
    op.drop_table('comments_activity', schema='crm_test')
    # ### end Alembic commands ###