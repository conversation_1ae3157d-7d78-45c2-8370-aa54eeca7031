import uuid
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>loat, DateT<PERSON>, <PERSON>olean
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.extended_base_mixin import ExtendedBaseMixin
from app.config import settings
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.schema import UniqueConstraint

class NotificationSettings(Base):
    __tablename__ = 'notifications_settings'
    __table_args__ = (UniqueConstraint('user_id', 'player_id', name='_user_player_uc_new'), {"schema": settings.PG_SCHEMA})
    # __table_args__ = {"schema": settings.PG_SCHEMA}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.contacts.id", ondelete="cascade"),
        index=True,
        primary_key = True
    )

    # organization_id = Column(
    #     UUID(as_uuid=True),
    #     ForeignKey(f"{settings.PG_SCHEMA}.organizations.id"),
    #     index=True,
    #     primary_key = True,
    # )

    player_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.player_records.id", ondelete="cascade"),
        index=True,
        primary_key = True,
    )

    # user = relationship(
    #     "Contacts",
    #     # primaryjoin=f"wyscout.player_info2.playerId=={settings.PG_SCHEMA}.player_record.playerId",
    #     viewonly=True,
    #     primaryjoin='foreign(NotificationSettings.user_id)==remote(User.id)',
    # )

    contract = relationship(
        "Contract",
        # primaryjoin=f"wyscout.player_info2.playerId=={settings.PG_SCHEMA}.player_record.playerId",
        viewonly=True,
        primaryjoin='foreign(NotificationSettings.player_id)==remote(Contract.player_id)', uselist = True,
    )

    contract_notifications = Column(Boolean, default = False)
    player_notifications = Column(Boolean, default = False)

    

