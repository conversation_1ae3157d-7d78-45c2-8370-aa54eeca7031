"""contracts on delete set null for fks

Revision ID: 9c738ef0fe97
Revises: 933e7a0fdfce
Create Date: 2022-09-26 08:03:28.117007

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9c738ef0fe97'
down_revision = '933e7a0fdfce'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('contracts_agent_id_fkey', 'contracts', schema='crm_dev', type_='foreignkey')
    op.drop_constraint('contracts_player_id_fkey', 'contracts', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key(None, 'contracts', 'contacts', ['agent_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', ondelete='SET NULL')
    op.create_foreign_key(None, 'contracts', 'player_records', ['player_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', ondelete='SET NULL')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'contracts', schema='crm_dev', type_='foreignkey')
    op.drop_constraint(None, 'contracts', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key('contracts_player_id_fkey', 'contracts', 'player_records', ['player_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.create_foreign_key('contracts_agent_id_fkey', 'contracts', 'contacts', ['agent_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    # ### end Alembic commands ###