"""Master migrations

Revision ID: 806b8ecbe96c
Revises: c1384cb446b0
Create Date: 2025-05-09 13:58:04.670988

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '806b8ecbe96c'
down_revision = 'c1384cb446b0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_players', sa.Column('x_cordinate', sa.Float(), nullable=True), schema='crm')
    op.add_column('field_players', sa.Column('y_cordinate', sa.Float(), nullable=True), schema='crm')
    op.add_column('field_requests', sa.Column('request_id', sa.UUID(), nullable=True), schema='crm')
    op.add_column('field_requests', sa.Column('x_cordinate', sa.Float(), nullable=True), schema='crm')
    op.add_column('field_requests', sa.Column('y_cordinate', sa.Float(), nullable=True), schema='crm')
    op.drop_index('ix_crm_field_requests_teamId', table_name='field_requests', schema='crm')
    op.create_index(op.f('ix_crm_field_requests_request_id'), 'field_requests', ['request_id'], unique=False, schema='crm')
    op.create_foreign_key(None, 'field_requests', 'team_requests', ['request_id'], ['id'], source_schema='crm', referent_schema='crm', ondelete='CASCADE')
    op.drop_column('field_requests', 'teamId', schema='crm')
    op.add_column('organizations', sa.Column('agency_logo_url', sa.String(), nullable=True), schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organizations', 'agency_logo_url', schema='crm')
    op.add_column('field_requests', sa.Column('teamId', sa.INTEGER(), autoincrement=False, nullable=True), schema='crm')
    op.drop_constraint(None, 'field_requests', schema='crm', type_='foreignkey')
    op.drop_index(op.f('ix_crm_field_requests_request_id'), table_name='field_requests', schema='crm')
    op.create_index('ix_crm_field_requests_teamId', 'field_requests', ['teamId'], unique=False, schema='crm')
    op.drop_column('field_requests', 'y_cordinate', schema='crm')
    op.drop_column('field_requests', 'x_cordinate', schema='crm')
    op.drop_column('field_requests', 'request_id', schema='crm')
    op.drop_column('field_players', 'y_cordinate', schema='crm')
    op.drop_column('field_players', 'x_cordinate', schema='crm')
    # ### end Alembic commands ###