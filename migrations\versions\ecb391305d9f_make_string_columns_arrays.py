"""make string columns arrays

Revision ID: ecb391305d9f
Revises: 0c5593a02d1a
Create Date: 2022-08-10 06:19:39.425615

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ecb391305d9f'
down_revision = '0c5593a02d1a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('player_records', 'position',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               type_=sa.ARRAY(sa.String()),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_records', 'transfer_period',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               type_=sa.ARRAY(sa.String()),
               existing_nullable=True,
               schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('player_records', 'transfer_period',
               existing_type=sa.ARRAY(sa.String()),
               type_=postgresql.ARRAY(sa.TEXT()),
               existing_nullable=True,
               schema='crm_dev')
    op.alter_column('player_records', 'position',
               existing_type=sa.ARRAY(sa.String()),
               type_=postgresql.ARRAY(sa.TEXT()),
               existing_nullable=True,
               schema='crm_dev')
    # ### end Alembic commands ###