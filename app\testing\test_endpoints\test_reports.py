from fastapi.testclient import Test<PERSON>lient
import json
from app.main import app
from app.testing.fixtures import (switch_between_users,
 client_chestnite,
  client_enskai,
  get_current_user_enskai_owner,
  get_current_user_chesnite_limited)
import pytest

client = TestClient(app)

reports_dict = {'report_id_enskai': None, 'created_report_obj_enskai': None,
'report_id_chestnite': None, 'created_report_obj_chestnite': None}

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_read_reports(switch_between_users: TestClient):
    client, org = switch_between_users
    response = client.get("reports/")
    data = response.json()
    assert data is not None, "Response returned null data"

    if org == 'enskai':
        first_data = data[0]
        assert "strengths" in first_data, "strengths is not in the returned contacts"
        assert "weaknesses" in first_data, "weaknesses is not in the returned contacts"
        assert "created_by" in first_data, "created_by is not in the returned contacts"
        assert "created_at" in first_data, "created_at is not in the returned contacts"
        assert "current_ability" in first_data,"current_ability is not in the returned contacts"
    else:
        assert len(data) == 0

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_create_report(switch_between_users: TestClient):
    client, org = switch_between_users

    data = json.dumps({
    "player_id": "c9f33cf4-b563-4ebe-995b-0db9ceff9051",
    "strengths": "Can shoot",
    "weaknesses": "Cant pass",
    "model_ranks": "Top ranks",
    "current_ability": 1,
    "lookalike": "Messi",
    "conclusion": "Top choice",
    "notes": "Good connection with club",
    "report_type": []
    })

    response = client.post('reports/', data=data, headers={"Content-Type": "application/json"})
    assert response.status_code == 200, f"Request failed, status code is {response.status_code}"
    reponse_obj = response.json()

    reports_dict[f"created_report_obj_{org}"] = reponse_obj
    reports_dict[f"report_id_{org}"] = reponse_obj['id']
    assert reports_dict[f"report_id_{org}"] is not None, "Contact id is None"

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_get_specific_report(switch_between_users: TestClient):
    client, org = switch_between_users

    response = client.get(f"""reports/{reports_dict[f"report_id_{org}"]}""")
    assert response.status_code == 200, f"Request failed, status code is {response.status_code}"
    returned_obj = response.json()

    del returned_obj['player']
    del reports_dict[f"created_report_obj_{org}"]['player']

    assert returned_obj == reports_dict[f"created_report_obj_{org}"], "The returned object should be equal to the just created one"
    
    non_existent_id_response = client.get("player_records/9f6b7234-6a6d-11ed-a1eb-0242ac120002")
    assert non_existent_id_response.status_code == 404, "Maybe we have this id in the DB -_-"

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_edit_specific_report(switch_between_users: TestClient):
    client, org = switch_between_users

    data = json.dumps({
    "player_id": "c9f33cf4-b563-4ebe-995b-0db9ceff9051",
    "strengths": "Can shoot",
    "weaknesses": "Can pass",
    "model_ranks": "Low ranks",
    "current_ability": 5,
    "lookalike": "Messi",
    "conclusion": "Top choice",
    "notes": "Good connection with club",
    "report_type": []
    })

    response = client.put(f"""reports/{reports_dict[f"report_id_{org}"]}""", data=data, headers={"Content-Type": "application/json"})
    assert response.status_code == 200,  f"Request failed, status code is {response.status_code}"
    response_obj = response.json()
    assert response_obj != reports_dict[f"created_report_obj_{org}"], "Put request should have made updates"
    assert response_obj['weaknesses'] == 'Can pass', "weaknessesr is not changed to 'Can pass'"
    assert response_obj['model_ranks'] == 'Low ranks', "model ranks is not changed, but it should be"
    assert response_obj['current_ability'] == 5, 'current_ability should be 5'

@pytest.mark.parametrize('switch_between_users', ['client_chestnite', 'client_enskai'], indirect=True)
def test_delete_specific_report(switch_between_users: TestClient):
    client, org = switch_between_users

    response = client.delete(f"""reports/{reports_dict[f"report_id_{org}"]}""")
    assert response.status_code == 200,  f"Request failed, status code is {response.status_code}"
    response = client.get(f"""reports/{reports_dict[f"report_id_{org}"]}""")
    assert response.status_code == 404, "Id should be deleted, but apperantly it isn't"