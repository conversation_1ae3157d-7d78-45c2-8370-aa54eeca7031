"""Platform notifications - community deal instead of proposal

Revision ID: eb4de28aa78d
Revises: b6f912ef102b
Create Date: 2024-11-12 14:33:08.189854

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'eb4de28aa78d'
down_revision = 'b6f912ef102b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('platform_notifications', sa.Column('community_deal_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_test')
    op.drop_constraint('platform_notifications_community_proposal_id_fkey', 'platform_notifications', schema='crm_test', type_='foreignkey')
    op.create_foreign_key(None, 'platform_notifications', 'community_deal', ['community_deal_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    op.drop_column('platform_notifications', 'community_proposal_id', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('platform_notifications', sa.Column('community_proposal_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm_test')
    op.drop_constraint(None, 'platform_notifications', schema='crm_test', type_='foreignkey')
    op.create_foreign_key('platform_notifications_community_proposal_id_fkey', 'platform_notifications', 'community_proposals', ['community_proposal_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    op.drop_column('platform_notifications', 'community_deal_id', schema='crm_test')
    # ### end Alembic commands ###