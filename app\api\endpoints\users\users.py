from fastapi import Depends, APIRouter, Body

from app.models.user import User
from app.schemas.user import UserCreate, UserRead, UserUpdate
from app.schemas.user import UserRead as UserSchema
from app.api.endpoints.users.auth import (
    auth_backend,
    current_active_user,
    fastapi_users,
)
from typing import Any
from app.api.utils import refresh_token
from app.utils import mail
from fastapi import APIRouter, Depends, HTTPException, Request, BackgroundTasks
from datetime import datetime, timezone
import json
from sqlalchemy.orm import Session
from app.api import deps, utils
from app import crud
import jwt
from app.utils.admin_logging import log_admin_action

router = APIRouter()

router.include_router(
    fastapi_users.get_auth_router(auth_backend, True),
    prefix="/auth/jwt",
    tags=["users"],
)
router.include_router(
    fastapi_users.get_register_router(UserRead, UserCreate),
    prefix="/auth",
    tags=["users"],
)
router.include_router(
    fastapi_users.get_reset_password_router(),
    prefix="/auth",
    tags=["users"],
)
router.include_router(
    fastapi_users.get_verify_router(UserRead),
    prefix="/auth",
    tags=["users"],
)
router.include_router(
    fastapi_users.get_users_router(UserRead, UserUpdate),
    prefix="/auth",
    tags=["users"],
)


@router.get("/authenticated-route")
async def authenticated_route(user: User = Depends(current_active_user)):
    return {"message": f"Hello {user.email}!"}


@router.post("/refresh_token")
async def authenticated_route(request: Request, body=Body(...)):
    body = json.loads(await request.body())
    remember_me = body["remember_me"]
    token = body.get("token")

    if token:
        try:
            # Extract claims without verifying
            claims = jwt.decode(token, options={"verify_signature": False})
            exp = claims.get("exp")
            current_timestamp = datetime.now(timezone.utc).timestamp()

            # Check if the token has expired
            if exp and exp <= current_timestamp:
                if remember_me:
                    # Refresh the token if `remember_me` is set
                    user_id = claims.get("sub")
                    new_token = refresh_token(user_id)["id_token"]
                    print("new_token", new_token)
                    return new_token
                else:
                    # Token expired, and no `remember_me` - return None or an error
                    return {"error": "Token expired and remember_me is not set"}
            else:
                # Token is still valid
                return token
        except Exception as e:
            print(e)
            return {"error": "Token decoding error"}
    else:
        return {"error": "Token not provided"}


@router.get("/delete-acc-email/{id}")
async def send_email_for_account_deletion(
    *,
    id: str,
    current_active_user: User = Depends(current_active_user),
    background_tasks: BackgroundTasks,
) -> Any:
    """
    Send an email to our support about a user who wants
    to delete its account
    """
    if str(current_active_user.id) != str(id):
        raise HTTPException(status_code=403, detail="Not authorized")
    else:
        background_tasks.add_task(mail.send_support_del_acc_mail, current_active_user)


@router.get("/get-user-by-email/{email}")
async def get_user_by_email_homemade(
    *,
    email: str,
    db: Session = Depends(deps.get_db),
    current_active_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve a user by email, ensuring the requesting user belongs to the same organization.
    """
    # Get the user by email
    user = crud.user.get_by_email(db, email=email)

    # Check if the user exists
    if not user:
        return None

    return {
        "id": user.id,
        "organization_id": user.organization_id,
        "organization": user.organization.name,
    }


@router.patch("/stop-account/{id}", response_model=bool)
async def stop_account(
    id: str,
    db: Session = Depends(deps.get_db),
    current_active_user: User = Depends(deps.get_current_active_user),
):
    """
    Stop a user account. Needs to be admin + superuser.
    """
    # Check if the current user has the required access
    utils.check_access_admin(current_active_user)
    if current_active_user.is_superuser:
        user = crud.user.stop_user(db, id)
        if user:
            # Log the account stop action
            log_admin_action(
                db=db,
                user=current_active_user,
                action_type="stop_account",
                target_type="user",
                target_id=id,
                details=f"Stopped account for user {user.email}",
            )
            return True
        return False  # User not found
    return False  # User does not have permission
