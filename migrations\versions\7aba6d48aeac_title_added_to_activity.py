"""Title added to activity

Revision ID: 7aba6d48aeac
Revises: 40836527a65e
Create Date: 2024-06-27 14:46:58.540767

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7aba6d48aeac'
down_revision = '40836527a65e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('activity', sa.Column('title', sa.String(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('activity', 'title', schema='crm_test')
    # ### end Alembic commands ###