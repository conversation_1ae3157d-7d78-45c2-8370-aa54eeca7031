"""Title added to activity

Revision ID: c865a2417676
Revises: 7aba6d48aeac
Create Date: 2024-07-07 09:48:17.134326

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy
# revision identifiers, used by Alembic.
revision = 'c865a2417676'
down_revision = '7aba6d48aeac'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('activity_change',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('edit_at', sa.DateTime(), nullable=True),
    sa.Column('field', sa.String(), nullable=True),
    sa.Column('previous', sa.String(), nullable=True),
    sa.Column('updated', sa.String(), nullable=True),
    sa.Column('activity_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('edit_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.ForeignKeyConstraint(['activity_id'], ['crm_dev.activity.id'], ),
    sa.ForeignKeyConstraint(['edit_by'], ['crm_dev.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_activity_change_activity_id'), 'activity_change', ['activity_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_activity_change_edit_at'), 'activity_change', ['edit_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_activity_change_edit_by'), 'activity_change', ['edit_by'], unique=False, schema='crm_dev')
    op.add_column('activity', sa.Column('title', sa.String(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('activity', 'title', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_activity_change_edit_by'), table_name='activity_change', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_activity_change_edit_at'), table_name='activity_change', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_activity_change_activity_id'), table_name='activity_change', schema='crm_dev')
    op.drop_table('activity_change', schema='crm_dev')
    # ### end Alembic commands ###