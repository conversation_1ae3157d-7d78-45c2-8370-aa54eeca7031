from sqlalchemy import Column, String, <PERSON><PERSON><PERSON>, DateTime, Boolean, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
import uuid
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.config import settings


class TrackedTransfers(Base):
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    __tablename__ = "tracked_transfers"
    __table_args__ = (UniqueConstraint('request_id', 'player_name', 'date', name='uix_request_id_player_name_date'),
                      {"schema": settings.PG_SCHEMA})

    request_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.team_requests.id", ondelete='CASCADE'),
        index=True,
    )
    position = Column(String)
    player_name = Column(String)
    left_name = Column(String)
    fee = Column(String)
    date = Column(DateTime)
    player_url = Column(String)
    active = Column(Boolean)