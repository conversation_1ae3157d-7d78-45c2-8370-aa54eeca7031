from typing import Optional
from datetime import datetime
import uuid
from pydantic import BaseModel, Field, validator
from app.schemas.user import UserShort
from app.schemas.organization import OrganizationShort


def empty_str_to_none(v):
    return None if v == "" else v


class ExtendedUpdateBase(BaseModel):
    last_updated: Optional[datetime] = Field(default_factory=datetime.now)
    notes: Optional[str] = None
    is_sensitive: Optional[bool] = False

    _empty_str_to_none = validator("*", allow_reuse=True, pre=True)(empty_str_to_none)


# no id in create model, also created_at and created_by are not passed when updating,
# creator is not passed since this is retrieved only when reading
class ExtendedCreateBase(ExtendedUpdateBase):
    created_at: datetime = Field(default_factory=datetime.now)
    # created_by: uuid.UUID
    # organization_id: uuid.UUID


class ExtendedBase(ExtendedCreateBase):
    id: uuid.UUID
    creator: UserShort
    created_by: uuid.UUID
    organization_id: uuid.UUID
    organization: OrganizationShort

    class Config:
        orm_mode = True

class ExtendedCommunityBase(ExtendedCreateBase):
    id: uuid.UUID
    organization_id: uuid.UUID

    class Config:
        orm_mode = True