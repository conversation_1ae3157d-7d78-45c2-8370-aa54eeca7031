"""Agent field is string staging

Revision ID: b40e188851f1
Revises: e1c39ad0051b
Create Date: 2023-08-14 13:33:39.375943

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b40e188851f1'
down_revision = 'e1c39ad0051b'
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column('community_proposals', 'are_you_the_agent',
               existing_type=sa.<PERSON>(),
               type_=sa.VARCHAR(),
               existing_nullable=True,
               schema='crm_test')


def downgrade():
    op.alter_column('community_proposals', 'are_you_the_agent',
               existing_type=sa.VARCHAR(),
               type_=sa.<PERSON>(),
               existing_nullable=True,
               schema='crm_test')