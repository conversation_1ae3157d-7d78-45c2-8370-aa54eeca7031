from typing import Optional, List
from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)
import uuid
from app.schemas.enums import Foot, Position, ControlLevel

from app.schemas.player_record import PlayerRecord, PlayerRecordShort
from app.schemas.team_request import TeamRequestShortEmail

class CommunityProposalUpdate(ExtendedUpdateBase):
    description: Optional[str]
    club_asking_price: Optional[float]
    expected_salary: Optional[float]
    are_you_the_agent: Optional[ControlLevel]
    foot: Optional[Foot]
    position: Optional[List[Position]]
    player_id: Optional[uuid.UUID]
    request_id: Optional[uuid.UUID]
    request_creator: Optional[List[uuid.UUID]]

class CommunityProposalCreate(CommunityProposalUpdate, ExtendedCreateBase):
    description: Optional[str]
    club_asking_price: Optional[float]
    expected_salary: Optional[float]
    are_you_the_agent: Optional[ControlLevel]
    foot: Optional[Foot]
    position: Optional[List[Position]]
    player_id: uuid.UUID
    request_id: uuid.UUID
    request_creator: List[uuid.UUID]
    video_link: Optional[str]
    phone_number: Optional[str]

class CommunityProposal(CommunityProposalCreate, ExtendedBase):
    player_records: PlayerRecordShort
    team_requests: TeamRequestShortEmail

    class Config:
        orm_mode = True
        use_cache=True