from datetime import datetime
import uuid

from sqlalchemy import Column, String, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import declared_attr, relationship

from app.config import settings


class ChangeBaseMixin(object):

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    edit_at = Column(DateTime, index=True, default=datetime.now)
    field = Column(String)
    @declared_attr
    def edit_by(cls):
        return Column("edit_by",  ForeignKey(f"{settings.PG_SCHEMA}.user.id"), index=True,)

    @declared_attr
    def editor(cls):
        return relationship("User")

    previous = Column(String)
    updated = Column(String)
