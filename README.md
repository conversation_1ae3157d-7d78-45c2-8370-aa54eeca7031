![Build Status](https://github.com/Ensk-ai/shadow-eleven-backend/workflows/pytesting/badge.svg)
![Coverage](../badges/.badges/testing/coverage.svg?raw=true)


## Installation

- Install into your currently active [Python environment](https://docs.python.org/3/tutorial/venv.html) with:

```bash
python3 -m pip install -r requirements.txt
```

- The project depends on `shadow-eleven-utils` which must be installed using the installation instructions on the [Wiki](https://dev.azure.com/Enskai/Arion/_wiki/wikis/Arion%20Technical%20Documentation/158/Python-Packaging-with-Artifact-Repository?anchor=downloading-and-installing-the-package). The user must follow the instructions regarding downloading and installing the package, building and uploading are not relevant for making this repo operational.

## Configuration

You'll need to set the following environment variables before running the project. (If you don't have the credentials ask the project manager):

```bash
    # The following will work on Linux & OSX:
    export DEBUG_MODE=True
    export DB_URL="mongodb+srv://<username>:<password>@<url>/farmstack?retryWrites=true&w=majority"
    export JWT_SECRET_KEY="<secret value>"
    export REALM_APP_ID="<realm id>"
```

## Run It

Run the code in dev mode with the following command:

```
venv/bin/activate
python -m uvicorn app.main:app --host 127.0.0.1 --port 6960 --reload
```

To run the matching service in dev mode too, in a separate console run:
```
source ./shadow-eleven-backend/venv/bin/activate
cd ../shadchan
uvicorn app.main:app --host 127.0.0.1 --port 6962 --reload
```

TODO: Change README to reflect the current state