import orjson
from typing import Any, Optional, List
import requests
from fastapi import APIRout<PERSON>, Depends, BackgroundTasks, HTTPException, Body
from sqlalchemy.orm import Session
from app.api.endpoints.platform_notifications import create_in_app_notification
from app.api.endpoints.notifications import update_notifications
from app.schemas.player_record import (
    PlayerR<PERSON><PERSON>,
    PlayerRecordUpdate,
    PlayerRecordCreate,
    PlayerRecordCreateLookup,
    ManuallyCreatedPlayer,
    WhatsAppPlayerRecordCreate,
    WhatsAppPlayerRecordUpdate,
    WhatsAppPlayerCheckResponse,
)
from app.schemas.comment import CommentCreate
from app.utils.validate_whatsapp_request import validate_whatsapp_user
from app.schemas.notifications import NotificationsUpdate
from app import crud, models
from app.api import deps, utils
import uuid

try:
    from ...config import settings
except:
    from app.config import settings
from app.api.endpoints.contacts import read_contact
from app.utils import (
    mail,
    caching,
    get_async_response,
    positionMappingWS,
    community_tokens,
)
from app.api.endpoints.message_subscription import read_subscriptions
from fastapi.params import Query
from app.schemas.community_tokens import CommunityTokensUpdate

router = APIRouter()


@router.get("/")
async def read_player_records(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    teamId: int = None,
    active: Optional[bool] = Query(None),
    control_stage: Optional[str] = Query(None),
) -> Any:
    """
    Retrieve Player Records.
    """

    can_access_sense = utils.can_access_sensitive(current_user)
    org_id = current_user.organization_id
    exclude = utils.exclude_based_on_role(current_user)

    utils.check_get_all(PlayerRecord, current_user)

    control_stage_list = control_stage.split(",") if control_stage else None
    filters = {
        "control_stage": control_stage_list,
        "active": active,
    }
    players = []
    if teamId is None:
        players = crud.player_record.get_all_with_filters(
            db, org_id, can_access_sense, filters
        )
    else:
        players = crud.suit_score_joined.get_team_suitability(
            db, org_id, teamId, can_access_sense
        )
    if teamId is None:
        if exclude:
            return_players = [
                PlayerRecord.from_orm(x) for x in players if exclude(x, current_user)
            ]
        else:
            return_players = [PlayerRecord.from_orm(x) for x in players]
    else:
        if exclude:
            return_players = [
                {
                    **PlayerRecord.from_orm(x["PlayerRecord"]),
                    "suitability_score": x["suitability_score"],
                }
                for x in players
                if exclude(x, current_user)
            ]
        else:
            return_players = [
                {
                    **PlayerRecord.from_orm(x["PlayerRecord"]),
                    "suitability_score": x["suitability_score"],
                }
                for x in return_players
            ]

    return return_players


@router.get(
    "/check_player/{playerId}",
)
def check_player(
    *,
    db: Session = Depends(deps.get_db),
    playerId: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get PlayerRecord by wyscout ID.
    """

    player_record = crud.player_record.get_with_wyscout(
        db=db, wyId=playerId, org_id=current_user.organization_id
    )
    if player_record:
        return True
    return False


@router.get("/wyscout/{playerId}", response_model=PlayerRecord)
def get_player_by_wyscout_id(
    *,
    db: Session = Depends(deps.get_db),
    playerId: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get PlayerRecord by wyscout ID.
    """
    if player_record := crud.player_record.get_with_wyscout(
        db=db, wyId=playerId, org_id=current_user.organization_id
    ):
        return PlayerRecord.from_orm(player_record)
    return False


@router.post("/", response_model=PlayerRecord)
async def create_player_record(
    *,
    db: Session = Depends(deps.get_db),
    lookup_player: PlayerRecordCreateLookup,
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(deps.get_current_active_user),
    cache=Depends(caching.get_cache),
) -> Any:
    """
    Create a new Player Record.
    """
    utils.check_create(PlayerRecord, current_user)
    # Check if player
    if lookup_player.playerId:
        wyscout_player = crud.player_record.get_wyscout_player_data_playerId(
            db, lookup_player.playerId
        )
    elif lookup_player.tm_player_id:
        wyscout_player = crud.player_record.get_wyscout_player_data_tmId(
            db, lookup_player.tm_player_id
        )
        if not wyscout_player:
            raise HTTPException(status_code=409, detail="tm_data_not_proccessed")
    else:
        raise HTTPException(status_code=404, detail="other_error")
    positions = []
    if wyscout_player.primary_ws_position:
        positions.append(positionMappingWS[wyscout_player.primary_ws_position])
    if wyscout_player.secondary_ws_position:
        if positionMappingWS[wyscout_player.secondary_ws_position] not in positions:
            positions.append(positionMappingWS[wyscout_player.secondary_ws_position])
    player_record_in = PlayerRecordCreate(
        playerId=wyscout_player.playerId, position=positions
    )
    # Check for existing player:
    existing_player = crud.player_record.get_with_wyscout(
        db=db, wyId=player_record_in.playerId, org_id=current_user.organization_id
    )
    if existing_player:
        raise HTTPException(status_code=409, detail="player_already_exists")

    player_record = crud.player_record.create_with_user(
        db=db,
        obj_in=player_record_in,
        user=current_user,
    )
    # Stopped emails when creating a player. No longer needed
    # for assigned_id in player_record_in.assigned_to_record:
    #    background_tasks.add_task(
    #        mail.send_mail_new_player,
    #        player_record,
    #        current_user=current_user,
    #        db=db,
    #        read_contact_func=read_contact,
    #        read_subscription_func=read_subscriptions,
    #        create_in_app_notification=create_in_app_notification,
    #        assigned_id=assigned_id,
    #    )
    # await caching.reset_cache_for_everyone(
    #    cache, "player_records", current_user.organization_id
    # )
    return player_record


@router.post(
    "/whatsapp",
    response_description="Add player record from whatsapp",
)
async def create_whatsapp_bulk_requests(
    phone_number: str,
    db: Session = Depends(deps.get_db),
    player_record_create: WhatsAppPlayerRecordCreate = Body(...),
    cache=Depends(caching.get_cache),
):
    phone_number = f"+{phone_number.strip()}"
    current_user = await validate_whatsapp_user(phone_number, db)
    utils.check_create(PlayerRecord, current_user)

    wyscout_player = crud.player_record.get_wyscout_player_data_tmId(
        db, player_record_create.tm_player_id
    )
    if not wyscout_player:
        raise HTTPException(status_code=409, detail="tm_data_not_proccessed")

    positions = []
    if wyscout_player.primary_ws_position:
        positions.append(positionMappingWS[wyscout_player.primary_ws_position])
    if wyscout_player.secondary_ws_position:
        if positionMappingWS[wyscout_player.secondary_ws_position] not in positions:
            positions.append(positionMappingWS[wyscout_player.secondary_ws_position])
    player_record_in = PlayerRecordCreate(
        playerId=wyscout_player.playerId,
        position=positions,
        assigned_to_record=[current_user.id],
        video_link=player_record_create.video_link,
        club_asking_price=player_record_create.club_asking_price,
        expected_net_salary=player_record_create.expected_net_salary,
        control_stage=player_record_create.control_stage,
    )

    existing_player = crud.player_record.get_with_wyscout(
        db=db, wyId=player_record_in.playerId, org_id=current_user.organization_id
    )
    if existing_player:
        raise HTTPException(status_code=409, detail="player_already_exists")

    tokens_left = crud.community_tokens.get_for_org(
        db=db, organization_id=current_user.organization_id
    )
    if community_tokens["player_whatsapp"] > tokens_left.tokens:
        raise HTTPException(status_code=402, detail="Not enough tokens")
    else:
        tokens_in = tokens_left
        tokens_in.tokens -= community_tokens["player_whatsapp"]
        crud.community_tokens.update(
            db=db,
            db_obj=tokens_left,
            obj_in=CommunityTokensUpdate.from_orm(tokens_in),
        )
    player_record = crud.player_record.create_with_user(
        db=db,
        obj_in=player_record_in,
        user=current_user,
    )
    comment = crud.comment.create(
        db=db,
        obj_in=CommentCreate(
            comment=player_record_create.description,
            creator=current_user.email,
            player_id=player_record.id,
        ),
    )
    # await caching.reset_cache_for_everyone(
    #    cache, "player_records", current_user.organization_id
    # )

    return {"tokens_left": tokens_left.tokens}


@router.get("/whatsapp/check_player", response_model=WhatsAppPlayerCheckResponse)
async def check_whatsapp_player(
    phone_number: str,
    player_name: str,
    db: Session = Depends(deps.get_db),
):
    """
    WhatsApp endpoint to find the most similar player from the user's organization.
    Authenticates user by phone_number and searches for player by name similarity.
    Returns the player with highest similarity score, preferring signed players if tied.
    """
    # Authenticate user with phone number
    phone_number = f"{phone_number.strip()}"
    current_user = await validate_whatsapp_user(phone_number, db)

    # Query to find most similar player using f_unaccent and similarity
    from sqlalchemy import text

    query = text(
        """
        SELECT
            pr.id,
            pr."playerId",
            pi."firstName" || ' ' || pi."lastName" AS "fullName",
            pr.control_stage,
            (1.0 - (levenshtein(
                lower(unaccent(pi."firstName" || ' ' || pi."lastName")),
                lower(unaccent(:player_name))
            )::float / GREATEST(
                length(pi."firstName" || ' ' || pi."lastName"),
                length(:player_name),
                1
            ))) AS similarity_score
        FROM {schema}.player_records pr
        JOIN wyscout.player_info2 pi ON pr."playerId" = pi."playerId"
        WHERE pr.organization_id = :org_id
        ORDER BY
            similarity_score DESC,
            CASE WHEN pr.control_stage = 'signed' THEN 1 ELSE 0 END DESC
        LIMIT 6
    """.format(
            schema=settings.PG_SCHEMA
        )
    )

    results = (
        db.execute(
            query, {"player_name": player_name, "org_id": current_user.organization_id}
        )
        .mappings()
        .all()
    )

    if not results:
        raise HTTPException(status_code=404, detail="No players found in organization")

    # First result is the best match
    best_match = results[0]

    # Create top matches list (excluding the best match)
    top_matches = []
    for result in results[1:]:
        top_matches.append(
            {
                "player_id": str(result["id"]),
                "playerId": result["playerId"],
                "fullName": result["fullName"],
                "similarity_score": float(result["similarity_score"]),
            }
        )

    return {
        "player_id": str(best_match["id"]),
        "playerId": best_match["playerId"],
        "fullName": best_match["fullName"],
        "similarity_score": float(best_match["similarity_score"]),
        "top_matches": top_matches,
    }


@router.put("/whatsapp/update", response_model=PlayerRecord)
async def update_whatsapp_player_record(
    *,
    db: Session = Depends(deps.get_db),
    phone_number: str,
    id: str,
    player_record_in: WhatsAppPlayerRecordUpdate,
    background_tasks: BackgroundTasks,
):
    """
    WhatsApp endpoint to update a player record with selective field updates.
    Authenticates user by phone_number and updates only the explicitly provided fields.
    Player record ID is provided as a query parameter.

    This endpoint uses selective updates - only fields that are explicitly provided
    in the request will be updated. If a field is not provided, it remains unchanged.
    If a field is provided with null value, it will be set to null/empty.

    Special handling:
    - 'comment' field: Creates a new comment with "Updated through WhatsApp Bot: " prefix
    - 'assigned_to_record' field: Sends email notifications to newly assigned users
    """
    # Authenticate user with phone number
    phone_number = f"{phone_number.strip()}"
    current_user = await validate_whatsapp_user(phone_number, db)

    # Get player record by ID from query parameter
    player_record = crud.player_record.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )

    if not player_record:
        raise HTTPException(status_code=404, detail="Player record not found")

    utils.check_modify(player_record, current_user)

    # Determine which fields were explicitly provided in the request
    # We use the __fields_set__ attribute from Pydantic to get only the fields that were set
    provided_fields = (
        list(player_record_in.__fields_set__)
        if hasattr(player_record_in, "__fields_set__")
        else []
    )

    # Handle comment field separately - extract it and remove from update data
    comment_text = None
    if "comment" in provided_fields and player_record_in.comment:
        comment_text = f"Updated through WhatsApp Bot: {player_record_in.comment}"
        provided_fields.remove(
            "comment"
        )  # Remove comment from fields to update on player record

    # Handle assigned_to_record updates and send notifications only if it was provided
    if "assigned_to_record" in provided_fields and player_record_in.assigned_to_record:
        for assigned_id in player_record_in.assigned_to_record:
            if str(assigned_id) not in [
                str(i.contact_id) for i in player_record.assigned_to_record
            ]:
                background_tasks.add_task(
                    mail.send_mail_new_player,
                    player_record,
                    current_user=current_user,
                    db=db,
                    read_contact_func=read_contact,
                    read_subscription_func=read_subscriptions,
                    create_in_app_notification=create_in_app_notification,
                    assigned_id=assigned_id,
                )

    # Update the player record with selective changelog - only provided fields
    player_record = crud.player_record.update_selective_with_changelog(
        db=db,
        record_id=id,
        db_obj=player_record,
        obj_in=player_record_in,
        change_model=models.PlayerRecordChange,
        user=current_user,
        record_type="player",
        provided_fields=provided_fields,
    )

    # Create comment if provided
    if comment_text:
        crud.comment.create(
            db=db,
            obj_in=CommentCreate(
                comment=comment_text,
                creator=current_user.email,
                player_id=player_record.id,
            ),
        )

    return player_record


@router.put("/{id}", response_model=PlayerRecord)
async def update_player_record(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    player_record_in: PlayerRecordUpdate,
    notifyBirthday: bool = Query(...),
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(deps.get_current_active_user),
    cache=Depends(caching.get_cache),
) -> Any:
    """
    Update an Player Record.
    """
    player_record = crud.player_record.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )
    utils.check_modify(player_record, current_user)
    for assigned_id in player_record_in.assigned_to_record:
        if str(assigned_id) not in [
            str(i.contact_id) for i in player_record.assigned_to_record
        ]:
            background_tasks.add_task(
                mail.send_mail_new_player,
                player_record,
                current_user=current_user,
                db=db,
                read_contact_func=read_contact,
                read_subscription_func=read_subscriptions,
                create_in_app_notification=create_in_app_notification,
                assigned_id=assigned_id,
            )
        if notifyBirthday or (player_record.notifications and not notifyBirthday):
            notif_object = None
            print(player_record.notifications)
            if player_record.notifications:
                notif_object = NotificationsUpdate(
                    id=player_record.notifications.id,
                    user_id=assigned_id,
                    player_id=player_record.id,
                    player_notifications=notifyBirthday,
                )
            else:
                notif_object = NotificationsUpdate(
                    id=uuid.uuid4(),
                    user_id=assigned_id,
                    player_id=player_record.id,
                    player_notifications=notifyBirthday,
                )
            await update_notifications(
                db=db, obj_in=notif_object, current_user=current_user
            )
    player_record = crud.player_record.update_with_changelog(
        db=db,
        record_id=id,
        db_obj=player_record,
        obj_in=player_record_in,
        change_model=models.PlayerRecordChange,
        user=current_user,
        record_type="player",
    )
    # await caching.reset_cache_for_everyone(
    #    cache, "player_records", current_user.organization_id
    # )
    return player_record


@router.get(
    "/{id}",
)
def read_player_record(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get PlayerRecord by ID.
    """

    player_record = crud.player_record.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )
    utils.check_get_one(player_record, current_user)
    exclude = (
        {}
        if utils.can_access_sensitive(current_user)
        else settings.PLAYER_SENSITIVE_FIELDS
    )
    return PlayerRecord.from_orm(player_record).dict(exclude=exclude)


@router.delete("/bulk-delete", response_model=List[PlayerRecord])
async def delete_multiple_player_records(
    *,
    db: Session = Depends(deps.get_db),
    ids: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_active_user),
    cache=Depends(caching.get_cache),
) -> Any:
    """
    Delete multiple Player Records.
    """
    if ids is None:
        raise HTTPException(status_code=400, detail="No ids provided")

    ids_list = ids.split(",")
    player_records_out = []
    for id in ids_list:
        player_record = crud.player_record.get_by_org(
            db=db, id=id, org_id=current_user.organization_id
        )
        utils.check_delete(player_record, current_user)
        player_record_out = PlayerRecord.from_orm(player_record)
        player_records_out.append(player_record_out)
        crud.player_record.remove(db=db, id=id)
    # await caching.reset_cache_for_everyone(
    #    cache, "player_records", current_user.organization_id
    # )
    return player_records_out


@router.delete("/{id}", response_model=PlayerRecord)
async def delete_player_record(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
    cache=Depends(caching.get_cache),
) -> Any:
    """
    Delete a Player Record.
    """
    player_record = crud.player_record.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )
    utils.check_delete(player_record, current_user)
    player_record_out = PlayerRecord.from_orm(player_record)
    crud.player_record.remove(db=db, id=id)
    # await caching.reset_cache_for_everyone(
    #    cache, "player_records", current_user.organization_id
    # )
    return player_record_out


@router.get(
    "/image/{playerId}",
)
async def get_player_image(
    *,
    playerId: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get Wyscout Image by wyscout player ID.
    """

    # wyscout = requests.get(
    #         f"https://apirest.wyscout.com/v2/players/{playerId}",
    #         auth=(settings.WYSCOUT_USR, settings.WYSCOUT_PASS),
    #         params={"imageDataURL": True},
    #     )

    wyscout = await get_async_response(
        f"https://apirest.wyscout.com/v2/players/{playerId}",
        auth=(settings.WYSCOUT_USR, settings.WYSCOUT_PASS),
        params={"imageDataURL": True},
    )

    return wyscout.json()["imageDataURL"]


@router.get(
    "/stats/{playerId}",
)
async def get_stats_for_player(
    *,
    db: Session = Depends(deps.get_db),
    playerId: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get Wyscout stats by wyscout player ID.
    """

    player_stats = crud.player_record.get_stats_for_playerId(db=db, playerId=playerId)
    if player_stats:
        return player_stats
    return False


@router.get("/transfers/{playerId}")
async def get_transfers_for_player(
    *,
    db: Session = Depends(deps.get_db),
    playerId: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get Wyscout transfers by wyscout player ID.
    """

    player_transfers = crud.player_record.get_transfers_for_playerId(
        db=db, playerId=playerId
    )

    return player_transfers


@router.post("/manually_create_player")
async def send_email_to_create_custom_player(
    *,
    player: ManuallyCreatedPlayer,
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    background_tasks.add_task(
        mail.send_support_mail,
        player,
        current_user=current_user,
    )
    return True
