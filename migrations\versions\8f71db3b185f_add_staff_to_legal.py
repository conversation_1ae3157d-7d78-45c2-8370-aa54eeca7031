"""Add staff to Legal

Revision ID: 8f71db3b185f
Revises: 924aa0e596e0
Create Date: 2025-01-31 10:31:47.535527

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8f71db3b185f'
down_revision = '924aa0e596e0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contracts', sa.Column('staff_id', sa.UUID(), nullable=True), schema='crm_test')
    op.create_index(op.f('ix_crm_test_contracts_staff_id'), 'contracts', ['staff_id'], unique=False, schema='crm_test')
    op.create_foreign_key(None, 'contracts', 'staff_records', ['staff_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='SET NULL')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'staff_records', schema='crm_test', type_='foreignkey')
    op.drop_constraint(None, 'contracts', schema='crm_test', type_='foreignkey')
    op.drop_index(op.f('ix_crm_test_contracts_staff_id'), table_name='contracts', schema='crm_test')
    op.drop_column('contracts', 'staff_id', schema='crm_test')
    op.drop_constraint(None, 'activity', schema='crm_test', type_='foreignkey')
    # ### end Alembic commands ###