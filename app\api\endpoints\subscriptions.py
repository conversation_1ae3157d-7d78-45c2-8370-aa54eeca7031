from typing import Any

from fastapi import status, <PERSON>Router, Depends, Request, HTTPException
from sqlalchemy.orm import Session
import os

from app.schemas.purchase import PurchaseCreate 
from app import crud, models
from app.api import deps, utils
from app.config import settings
import pandas as pd
from app.api.endpoints.users.auth import get_user_manager
from fastapi_users.manager import BaseUserManager
from app.schemas.user import UserUpdate, UserRoleDefaultInput
from app.utils import get_async_post_response, get_async_response
from app.api.utils import delete_refresh_token
import datetime
from fastapi.security import HTTPBasicCredentials, HTTPBasic
import secrets
from typing import Annotated


security = HTTPBasic()
router = APIRouter()

# THIS IS NOW DISCONTINUED !!!
# We are no longer letting users buy their subscriptions by themselves
# When a user registers the set role is free_trial - 1 week trail
# After a week trial or after he stops paying his account is locked

def check_stripe_user_auth(credentials: Annotated[HTTPBasicCredentials, Depends(security)],):
    current_username_bytes = credentials.username.encode("utf8")
    correct_username_bytes = settings.STRIPE_API_USER
    is_correct_username = secrets.compare_digest(
        current_username_bytes, correct_username_bytes
    )
    current_password_bytes = credentials.password.encode("utf8")
    correct_password_bytes = settings.STRIPE_API_KEY
    is_correct_password = secrets.compare_digest(
        current_password_bytes, correct_password_bytes
    )
    if not (is_correct_username and is_correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username

@router.get("/get_product_list")
async def get_product_list(
    # current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve Default Comparable Positions.
    """
    # utils.check_get_all(ComparablePositions, current_user)
    # return crud.ranking_comparable_positions.get_all(db)
    
    # resp = requests.get(
    #         os.path.join( settings.STRIPE_CLIENT_URL, 'get_product_list'),
    #         auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY),
    #     )

    resp = await get_async_response(
            os.path.join( settings.STRIPE_CLIENT_URL, 'get_product_list'),
            auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY),
    )
    
    return resp.json()

@router.post("/create-checkout-session")
async def create_checkout_session(body: Request,
                                  current_user: models.User = Depends(deps.get_current_active_user),
                                  ):
    
    referer_url = body.headers.get("referer")
    stripe_input = await body.json()
    stripe_input['email'] = current_user.email
    stripe_input['referrer_url'] = referer_url

    
    # resp = requests.post(
    #         os.path.join( settings.STRIPE_CLIENT_URL, 'create-checkout-session'),
    #         auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY), json=stripe_input
    #     )
    
    resp = await get_async_post_response(
            os.path.join( settings.STRIPE_CLIENT_URL, 'create-checkout-session'),
            auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY), json=stripe_input
        )
    
    return resp.json()


@router.get('/get-session-details/{session_id}')
async def get_session_details(session_id,
                              db: Session = Depends(deps.get_db),
                              user_manager: BaseUserManager = Depends(get_user_manager)):
    # resp = requests.get(
    #         os.path.join( settings.STRIPE_CLIENT_URL, 'get-session-details',session_id),
    #         auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY)
    #     )
    
    resp = await get_async_response(
            os.path.join( settings.STRIPE_CLIENT_URL, 'get-session-details',session_id),
            auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY)
        )
    
    out = resp.json()

    #TODO check customer's current subscription and update role in db and firebase

    # if out['customer']['created']:

    #     uid = db.execute(f"select id from {settings.PG_SCHEMA}.user where email='{out['customer']['email']}'").all()[0][0]

    #     user = await user_manager.get(uid)
    #     user = await user_manager.update(
    #            UserUpdate(**{"is_active": True}), user
    #        )
        
        

    return out

@router.get('/set-user-role-by-email/{email}')
async def set_user_role_by_email(
            email:str,
            db: Session = Depends(deps.get_db),
            current_user: models.User = Depends(deps.get_current_active_user),
            user_manager: BaseUserManager = Depends(get_user_manager)):
    
    user = await user_manager.get_by_email(email)
    
    resp = await get_async_response(
            os.path.join( settings.STRIPE_CLIENT_URL, 'get-customer',current_user.email),
            auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY)
        )
    
    customer = resp.json()

    if not customer['subscriptions']:
        # TODO set user to free
        role = db.execute(
            f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'self_registered_basic' """
            ).all()[0]
    else:

        customer_product = customer['subscriptions']['data'][0]['plan']['product']

        resp = await get_async_response(
                os.path.join( settings.STRIPE_CLIENT_URL, 'get_product_list'),
                auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY),
        )

        products = resp.json()
        product_name = [p['name'] for p in products['monthly'] if p['id'] == customer_product] + [p['name'] for p in products['yearly'] if p['id'] == customer_product]

        if customer['subscriptions']['data'][0]['pause_collection'] is not None:
            print(f"User will cancel at: {customer['subscriptions']['data'][0]['current_period_end']}")
            if customer['subscriptions']['data'][0]['current_period_end'] < datetime.datetime.now().timestamp():
                # TODO set user to free 
                role = db.execute(
                f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'self_registered_basic' """
                ).all()[0]
            else:
                if 'Basic' in product_name[0]:
                    role = db.execute(
                        f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'basic_paid' """
                        ).all()[0]
                    
                    # pass
                elif 'Pro' in product_name[0]:
                    role = db.execute(
                        f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'owner_self_registered' """
                        ).all()[0]


                else:
                    # TODO set to free and return None
                    role = db.execute(
                        f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'self_registered_basic' """
                        ).all()[0]
                    
            if customer['subscriptions']['data'][0]['pause_collection']['resumes_at'] is not None:
                if customer['subscriptions']['data'][0]['pause_collection']['resumes_at'] < datetime.datetime.now().timestamp():
                    if 'Basic' in product_name[0]:
                        role = db.execute(
                            f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'basic_paid' """
                            ).all()[0]
                        
                        # pass
                    elif 'Pro' in product_name[0]:
                        role = db.execute(
                            f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'owner_self_registered' """
                            ).all()[0]


                    else:
                        # TODO set to free and return None
                        role = db.execute(
                            f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'self_registered_basic' """
                            ).all()[0]

        else:
            if 'Basic' in product_name[0]:
                    role = db.execute(
                        f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'basic_paid' """
                        ).all()[0]
                    
                    # pass
            elif 'Pro' in product_name[0]:
                role = db.execute(
                    f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'owner_self_registered' """
                    ).all()[0]


            else:
                # TODO set to free and return None
                role = db.execute(
                    f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'self_registered_basic' """
                    ).all()[0]

    if user.role_id != role.id:
        
        user = await user_manager.update(
            UserUpdate(**{'role_id':role.id}), user
        )

        delete_refresh_token(user.id)

    return user

    

@router.get('/set-user-role')
async def set_user_role(
                        db: Session = Depends(deps.get_db),
                        current_user: models.User = Depends(deps.get_current_active_user),
                        user_manager: BaseUserManager = Depends(get_user_manager)):
    
    user = await user_manager.get(current_user.id)

    resp = await get_async_response(
            os.path.join( settings.STRIPE_CLIENT_URL, 'get-customer',current_user.email),
            auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY)
        )
    
    if current_user.role.name not in ['self_registered_basic', 'basic_paid', 'owner_self_registered']:
        return None
    
    customer = resp.json()

    if not customer['subscriptions']:
        # TODO set user to free
        role = db.execute(
            f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'self_registered_basic' """
            ).all()[0]
    else:

        customer_product = customer['subscriptions']['data'][0]['plan']['product']

        resp = await get_async_response(
                os.path.join( settings.STRIPE_CLIENT_URL, 'get_product_list'),
                auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY),
        )

        products = resp.json()
        product_name = [p['name'] for p in products['monthly'] if p['id'] == customer_product] + [p['name'] for p in products['yearly'] if p['id'] == customer_product]

        if customer['subscriptions']['data'][0]['pause_collection'] is not None:
            print(f"User will cancel at: {customer['subscriptions']['data'][0]['current_period_end']}")
            if customer['subscriptions']['data'][0]['current_period_end'] < (datetime.datetime.now() + datetime.timedelta(days=3).timestamp()):
                # TODO set user to free 
                role = db.execute(
                f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'self_registered_basic' """
                ).all()[0]
            else:
                if 'Basic' in product_name[0]:
                    role = db.execute(
                        f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'basic_paid' """
                        ).all()[0]
                    
                    # pass
                elif 'Pro' in product_name[0]:
                    role = db.execute(
                        f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'owner_self_registered' """
                        ).all()[0]


                else:
                    # TODO set to free and return None
                    role = db.execute(
                        f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'self_registered_basic' """
                        ).all()[0]
                    
            if customer['subscriptions']['data'][0]['pause_collection']['resumes_at'] is not None:
                if customer['subscriptions']['data'][0]['pause_collection']['resumes_at'] < datetime.datetime.now().timestamp():
                    if 'Basic' in product_name[0]:
                        role = db.execute(
                            f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'basic_paid' """
                            ).all()[0]
                        
                        # pass
                    elif 'Pro' in product_name[0]:
                        role = db.execute(
                            f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'owner_self_registered' """
                            ).all()[0]


                    else:
                        # TODO set to free and return None
                        role = db.execute(
                            f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'self_registered_basic' """
                            ).all()[0]

        else:
            if 'Basic' in product_name[0]:
                    role = db.execute(
                        f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'basic_paid' """
                        ).all()[0]
                    
                    # pass
            elif 'Pro' in product_name[0]:
                role = db.execute(
                    f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'owner_self_registered' """
                    ).all()[0]


            else:
                # TODO set to free and return None
                role = db.execute(
                    f"""select id, name from {settings.PG_SCHEMA}.user_roles where name = 'self_registered_basic' """
                    ).all()[0]

    if user.role_id != role.id:
        
        user = await user_manager.update(
            UserUpdate(**{'role_id':role.id}), user
        )

        raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail='User Role Changed. Re-login required.'
            )
    else:
        print('User just changed payment, payment period or paused the subscription and will have the same role until the current period expires')
        return None
    
@router.get('/get-sub-expiry/{email}')
async def get_sub_expiry(
    email:str = None,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user)):

    # utils.check_access_admin(current_user)

    if not email:
        user_org = crud.organization.get(db, current_user.organization_id)
        if user_org.billing_email:
            email = user_org.billing_email
        else:
            email = current_user.email

    resp = await get_async_response(
            os.path.join( settings.STRIPE_CLIENT_URL, 'get-customer',email),
            auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY)
        )
    
    data = resp.json()

    if 'subscriptions' in data.keys():
        if data['subscriptions'] is not None:
            if data['subscriptions']['status'] == 'active':
                return data['subscriptions']['data'][0]['cancel_at']
            else:
                return data['subscriptions']['data'][0]['latest_invoice']['due_date']

    
    return None
    
    

@router.get('/get-customer-data')
async def get_customer_data(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user)):

    def set_product_management(customer_interval, products, customer_plan_id):
        if customer_interval == 'month':
            for i in range(len(products['monthly'])):
                if products['monthly'][i]['id'] == customer_plan_id:
                    products['monthly'][i]['editable'] = True
                    customer_plan_name = products['monthly'][i]['name']

                if products['monthly'][i]['price'] > customer_price:
                    products['monthly'][i]['activatable'] = True
            for i in range(len(products['yearly'])):
                if products['yearly'][i]['price'] >= customer_price * 10:
                    products['yearly'][i]['activatable'] = True
        else:
            for i in range(len(products['yearly'])):
                if products['yearly'][i]['id'] == customer_plan_id:
                    products['yearly'][i]['editable'] = True
                    customer_plan_name = products['yearly'][i]['name']

                if products['yearly'][i]['price'] > customer_price:
                    products['yearly'][i]['activatable'] = True

            for i in range(len(products['monthly'])):
                if products['monthly'][i]['price'] >= customer_price / 10:
                    products['monthly'][i]['activatable'] = True

        return products, customer_plan_name
    # resp = requests.get(
    #         os.path.join( settings.STRIPE_CLIENT_URL, 'get-customer',email),
    #         auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY)
    #     )

    
    
    # return ''
    user_org = crud.organization.get(db, current_user.organization_id)
    if user_org.billing_email:
        email = user_org.billing_email
    else:
        email = current_user.email

    resp = await get_async_response(
            os.path.join( settings.STRIPE_CLIENT_URL, 'get-customer',email),
            auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY)
        )
    
    customer = resp.json()

    resp = await get_async_response(
            os.path.join( settings.STRIPE_CLIENT_URL, 'get_product_list'),
            auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY),
    )

    products = resp.json()
    products['monthly'] = [{**p, 'activatable': False, 'editable': False} for p in products['monthly']]
    products['yearly'] = [{**p, 'activatable': False, 'editable': False} for p in products['yearly']]

    # Default for non-subscribed users
    customer_plan_id = 'free'
    customer_interval = 'month'
    customer_price = 0
    customer_plan_name = 'Free'

    # TODO add logic to determine the customer plan for higher users


    if current_user.role.name not in ['self_registered_basic', 'basic_paid', 'owner_self_registered']:
        return {'customer': customer, 'products': products, 'current_plan': customer_plan_id, 'customer_plan_name':customer_plan_name, 'email':current_user.email, 'customer_type': 'enskai_registered'}

    if not customer['subscriptions']:
        products['monthly'] = [{**p, 'activatable': True, 'editable': False} for p in products['monthly']]
        products['yearly'] = [{**p, 'activatable': True, 'editable': False} for p in products['yearly']]
        return {'customer': customer, 'products': products, 'current_plan': customer_plan_id, 'customer_plan_name':customer_plan_name, 'email':current_user.email, 'customer_type': 'self_registered'}
    
    try:
        customer_plan_id = customer['subscriptions']['data'][0]['plan']['product']
        customer_interval = customer['subscriptions']['data'][0]['plan']['interval']
        customer_price = customer['subscriptions']['data'][0]['plan']['amount'] / 100
    except:
        products['monthly'] = [{**p, 'activatable': True, 'editable': False} for p in products['monthly']]
        products['yearly'] = [{**p, 'activatable': True, 'editable': False} for p in products['yearly']]
        return {'customer': customer, 'products': products, 'current_plan': customer_plan_id, 'customer_plan_name':customer_plan_name, 'email':current_user.email, 'customer_type': 'self_registered'}

    if customer['subscriptions']['data'][0]['pause_collection'] is None:
        products, customer_plan_name = set_product_management(customer_interval, products, customer_plan_id)

    elif customer['subscriptions']['data'][0]['pause_collection']['resumes_at'] is not None:
        if customer['subscriptions']['data'][0]['pause_collection']['resumes_at'] < datetime.datetime.now().timestamp():
            products, customer_plan_name = set_product_management(customer_interval, products, customer_plan_id)
                    
    elif customer['subscriptions']['data'][0]['current_period_end'] > (datetime.datetime.now() - datetime.timedelta(days=3)).timestamp():
        products, customer_plan_name = set_product_management(customer_interval, products, customer_plan_id)

    else:
        customer_plan_id = 'free'
        customer_interval = 'month'
        customer_price = 0
        customer_plan_name = 'Free'
    

    # print(products)


    return {'customer': customer, 'products': products, 'current_plan': customer_plan_id, 'customer_plan_name':customer_plan_name, 'email':current_user.email, 'customer_type': 'self_registered'}


@router.post("/create-portal-session")
async def create_portal_session(body: Request,
                                current_user: models.User = Depends(deps.get_current_active_user),
                                  ):
    
    referer_url = body.headers.get("referer")

    stripe_input = await body.json()
    

    stripe_input['email'] = current_user.email
    stripe_input['referrer_url'] = referer_url

    resp = await get_async_response(
            os.path.join( settings.STRIPE_CLIENT_URL, 'get-customer',current_user.email),
            auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY)
        )
    
    customer = resp.json()

    if customer['subscriptions']:
        if customer['subscriptions']['data'][0]['canceled_at'] is None:
            stripe_input['subscription_id'] = customer['subscriptions']['data'][0]['id']
            
            resp = await get_async_post_response(
                    os.path.join( settings.STRIPE_CLIENT_URL, 'create-portal-session'),
                    auth=(settings.STRIPE_API_USER, settings.STRIPE_API_KEY), json=stripe_input
                )
            try:
                portel_session_data = resp.json()
            except:
                return None
            
            return portel_session_data['url']
        else:
            return 'Cancelled'
    else:
        return None
    

@router.post("/assign-organizational-roles")
async def assign_organizational_roles(*,
                                      db: Session = Depends(deps.get_db),
                                      obj_in:UserRoleDefaultInput, 
                                      credentials: HTTPBasicCredentials = Depends(HTTPBasic())):
    
    check_stripe_user_auth(credentials)
    
    # TODO Get users for the organization by billing email
    org_users = db.execute(f"""select id from {settings.PG_SCHEMA}.organizations where billing_email = '{obj_in.billing_email}'""").all()

    if len(org_users):

        users_prev_roles = db.execute(f"""select u.id, u.email, ur.name as role_name from {settings.PG_SCHEMA}.user_roles ur, {settings.PG_SCHEMA}.user u, {settings.PG_SCHEMA}.user_roles_default urd
                                    where ur.id = urd.role_id and u.id = urd.user_id
                                    and u.organization_id = '{str(org_users[0].id)}' """).all()
        

        access_levels = {
            'viewer': 0,
            'self_registered_basic': 1,
            'user_limited': 2, 
            'user_elevated': 3,
            'user_full': 4,
            'user_external': 0,
            'basic_paid': 2,
            'owner': 5,
            'owner_self_registered': 5
        }

        requested_role = obj_in.role_name

        # if 'Basic' in obj_in.product_name:
        #     requested_access_level = access_levels['basic_paid']
        #     requested_role = 'basic_paid'
        # elif 'Pro' in obj_in.product_name:
        #     requested_access_level = access_levels['owner_self_registered']
        #     requested_role = 'owner_self_registered'
        # elif 'Elite' in obj_in.product_name:
        #     requested_access_level = access_levels['owner']
        #     requested_role = 'owner'
        # else:
        #     requested_access_level = access_levels['self_registered_basic']
        #     requested_role = 'self_registered_basic'

        if len(users_prev_roles) > 0:
            for user_p_role in users_prev_roles:
                user_p_role.email
                uid = user_p_role.id
                u_current_role = user_p_role.role_name
                u_access_level = access_levels[u_current_role]

                if u_current_role != 'self_registered_basic':
                    if requested_access_level > u_access_level:
                        requested_role = u_current_role

                
                requested_role_id = str(db.execute(f"""select id from {settings.PG_SCHEMA}.user_roles where name = '{requested_role}'""").all()[0])
                print(f"""update {settings.PG_SCHEMA}.user set role_id = '{requested_role_id}' where id = '{str(uid)}' """)
        
    
