"""Add cascade delete to source_to_record

Revision ID: fa4babb8e9c6
Revises: 69cdfd32ebd4
Create Date: 2023-05-29 17:49:58.949956

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fa4babb8e9c6'
down_revision = '69cdfd32ebd4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('player_records', 'playerId',
               existing_type=sa.BIGINT(),
               type_=sa.Integer(),
               existing_nullable=True,
               schema='crm_dev')
    op.drop_constraint('source_to_record_source_id_fkey', 'source_to_record', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key(None, 'source_to_record', 'contacts', ['source_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'source_to_record', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key('source_to_record_source_id_fkey', 'source_to_record', 'contacts', ['source_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.alter_column('player_records', 'playerId',
               existing_type=sa.Integer(),
               type_=sa.BIGINT(),
               existing_nullable=True,
               schema='crm_dev')
    # ### end Alembic commands ###