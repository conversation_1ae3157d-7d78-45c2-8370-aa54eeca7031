# from app.crud.crud_base import CRUDBase
# from app import models

# from sqlalchemy import desc
# from sqlalchemy.orm import Session, selectinload, lazyload, Load, joinedload
# from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
# from app.db.base_class import Base
# from sqlalchemy import desc
# from app.models import PlayerRecord, PlayerInfo

# ModelType = TypeVar("ModelType", bound=Base)


# class CRUDSuitability(
#     CRUDBase
# ):
#     def get_w_team(self, db: Session, team_id: Any, org_id: Any) -> Optional[ModelType]:

#         return db.query(self.model.playerId, PlayerInfo.fullName, PlayerInfo.shortName, PlayerInfo.primary_ws_position, PlayerInfo.secondary_ws_position, PlayerInfo.birth_date, PlayerInfo.team_name, PlayerInfo.passport, PlayerInfo.tm_link, self.model.hiring_team_id, self.model.suitability_score_w_min_coalesce, PlayerInfo.current_value.label('tm_value')
#                         , PlayerRecord.club_asking_price, PlayerRecord.current_gross_salary,
#                         ).join(PlayerRecord, self.model.playerId==PlayerRecord.playerId).join(PlayerInfo).options(
#             Load(self.model).selectinload("*"),
#             lazyload("*"),).filter((self.model.hiring_team_id == team_id) & (PlayerRecord.organization_id == org_id) & (PlayerInfo.currentTeamId != self.model.hiring_team_id)).order_by(desc(self.model.suitability_score_w_min_coalesce)).limit(100).all()
    
#     def get_player_suitability(self, db:Session, team_id:Any, player_id:Any)-> Optional[ModelType]:
#         return db.query(self.model.playerId, self.model.suitability_score_w_min_coalesce).options(
#             Load(self.model).selectinload("*"),
#             lazyload("*"),).filter((self.model.hiring_team_id == team_id) & (self.model.playerId == player_id)).all()

# suit_score = CRUDSuitability(models.SuitScores)
