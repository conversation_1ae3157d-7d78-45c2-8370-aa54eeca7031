"""Added required terms fields to user model - new

Revision ID: 7797ddeb123e
Revises: 3f37013022cf
Create Date: 2024-02-27 14:34:21.884420

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7797ddeb123e'
down_revision = '3f37013022cf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.<PERSON>umn('accept_terms', sa.<PERSON>(), nullable=True), schema='crm_test')
    op.add_column('user', sa.Column('accept_marketing_emails', sa.<PERSON>(), nullable=True), schema='crm_test')
    op.add_column('user', sa.Column('date_created', sa.DateTime(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'date_created', schema='crm_test')
    op.drop_column('user', 'accept_marketing_emails', schema='crm_test')
    op.drop_column('user', 'accept_terms', schema='crm_test')
    # ### end Alembic commands ###