from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship, declared_attr
from sqlalchemy.dialects.postgresql import UUID

from app.models.extended_base_mixin import ExtendedBaseMixin
from app.db.base_class import Base
from app.config import settings


class PlatformNotification(Base, ExtendedBaseMixin):
    __tablename__ = "platform_notifications"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    active = Column(Boolean, index=True)
    type = Column(String)
    
    activity_id = Column(UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.activity.id", ondelete="CASCADE"), nullable=True)
    team_request_id = Column(UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.team_requests.id", ondelete="CASCADE"), nullable=True)
    player_record_id = Column(UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.player_records.id", ondelete="CASCADE"), nullable=True)
    community_deal_id = Column(UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.community_deal.id", ondelete="CASCADE"), nullable=True)

    @declared_attr
    def created_for(cls):
        return Column(
            "created_for", ForeignKey(f"{settings.PG_SCHEMA}.user.id"), index=True
        )

    @declared_attr
    def created_for_user(cls):
        return relationship("User", foreign_keys=cls.created_for)

     # Relationships for each foreign key
    activity = relationship("Activity", foreign_keys=activity_id)
    team_request = relationship("TeamRequest", foreign_keys=team_request_id)
    player_record = relationship("PlayerRecord", foreign_keys=player_record_id)
    community_deal = relationship("CommunityDeal", foreign_keys=community_deal_id)
