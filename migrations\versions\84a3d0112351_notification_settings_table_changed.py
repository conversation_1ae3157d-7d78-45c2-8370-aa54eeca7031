"""Notification settings table changed

Revision ID: 84a3d0112351
Revises: 83395f896b5a
Create Date: 2023-07-07 12:47:17.942416

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '84a3d0112351'
down_revision = '83395f896b5a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('notification_settings',
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('contract_notifications', sa.Boolean(), nullable=True),
    sa.Column('player_notifications', sa.Boolean(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_dev.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_dev.organizations.id'], ),
    sa.ForeignKeyConstraint(['player_id'], ['crm_dev.player_records.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['crm_dev.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_notification_settings_created_at'), 'notification_settings', ['created_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_notification_settings_created_by'), 'notification_settings', ['created_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_notification_settings_is_sensitive'), 'notification_settings', ['is_sensitive'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_notification_settings_last_updated'), 'notification_settings', ['last_updated'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_notification_settings_organization_id'), 'notification_settings', ['organization_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_notification_settings_player_id'), 'notification_settings', ['player_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_notification_settings_user_id'), 'notification_settings', ['user_id'], unique=False, schema='crm_dev')
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('reports', 'player_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_notification_settings_user_id'), table_name='notification_settings', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_notification_settings_player_id'), table_name='notification_settings', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_notification_settings_organization_id'), table_name='notification_settings', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_notification_settings_last_updated'), table_name='notification_settings', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_notification_settings_is_sensitive'), table_name='notification_settings', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_notification_settings_created_by'), table_name='notification_settings', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_notification_settings_created_at'), table_name='notification_settings', schema='crm_dev')
    op.drop_table('notification_settings', schema='crm_dev')
    # ### end Alembic commands ###