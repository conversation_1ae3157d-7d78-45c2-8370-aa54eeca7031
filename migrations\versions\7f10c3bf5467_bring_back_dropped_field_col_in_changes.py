"""bring back dropped field col in changes

Revision ID: 7f10c3bf5467
Revises: 7059e9afe705
Create Date: 2022-08-05 08:03:32.372106

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7f10c3bf5467'
down_revision = '7059e9afe705'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_record_changes', sa.Column('field', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('team_request_changes', sa.Column('field', sa.String(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('team_request_changes', 'field', schema='crm_dev')
    op.drop_column('player_record_changes', 'field', schema='crm_dev')
    # ### end Alembic commands ###