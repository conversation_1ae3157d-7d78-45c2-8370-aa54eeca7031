"""Date deal created + new stages

Revision ID: e0999b48ec3a
Revises: 92a0e392431d
Create Date: 2024-12-06 14:44:35.472595

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e0999b48ec3a'
down_revision = '92a0e392431d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('activity', sa.Column('date_deal_created', sa.DateTime(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('activity', 'date_deal_created', schema='crm_dev')
    # ### end Alembic commands ###