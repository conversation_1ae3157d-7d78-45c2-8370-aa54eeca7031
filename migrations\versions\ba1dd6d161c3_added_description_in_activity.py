"""Added description in activity

Revision ID: ba1dd6d161c3
Revises: f94094d0d57e
Create Date: 2024-08-08 11:51:19.546779

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ba1dd6d161c3'
down_revision = 'f94094d0d57e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('activity', sa.Column('description', sa.String(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('activity', 'description', schema='crm_test')
    # ### end Alembic commands ###