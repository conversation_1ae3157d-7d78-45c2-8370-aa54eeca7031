import uuid
from typing import Optional, List
from pydantic import BaseModel
from app.schemas.user import UserShort
from app.schemas.contract import ContractShort

class NotificationsUpdate(BaseModel):
    id: uuid.UUID
    user_id: uuid.UUID
    player_id: uuid.UUID
    contract_notifications: Optional[bool]
    player_notifications: Optional[bool]

    class Config:
        orm_mode = True

class NotificationsRead(BaseModel):
    id: Optional[uuid.UUID]
    user_id: Optional[uuid.UUID]
    player_id: Optional[uuid.UUID]
    contract_notifications: Optional[bool]
    player_notifications: Optional[bool]

    class Config:
        orm_mode = True


class NotificationsCreate(NotificationsUpdate):
    id: Optional[uuid.UUID]

class Notifications(NotificationsUpdate):
    user: UserShort
    contract: List[ContractShort]

    class Config:
        orm_mode = True