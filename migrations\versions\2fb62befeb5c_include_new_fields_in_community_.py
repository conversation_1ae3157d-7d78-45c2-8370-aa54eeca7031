"""Include new fields in community requests tabel

Revision ID: 2fb62befeb5c
Revises: b9caab3d5760
Create Date: 2023-08-03 17:13:17.208008

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '2fb62befeb5c'
down_revision = 'b9caab3d5760'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('community_proposals', sa.Column('position', postgresql.ARRAY(sa.String()), nullable=True), schema='crm_test')
    op.add_column('community_proposals', sa.Column('foot', sa.String(), nullable=True), schema='crm_test')
    op.alter_column('community_proposals', 'are_you_the_agent',
               existing_type=sa.BOOLEAN(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('community_proposals', 'are_you_the_agent',
               existing_type=sa.String(),
               type_=sa.BOOLEAN(),
               existing_nullable=True,
               schema='crm_test')
    op.drop_column('community_proposals', 'foot', schema='crm_test')
    op.drop_column('community_proposals', 'position', schema='crm_test')
    # ### end Alembic commands ###