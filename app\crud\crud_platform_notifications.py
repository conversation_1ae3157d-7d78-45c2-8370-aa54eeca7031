from typing import List
from sqlalchemy.orm import Session
from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.platform_notifications import PlatformNotificationCreate, PlatformNotificationUpdate
from sqlalchemy import desc

class CRUDPlatformNotification(CRUDBase[models.PlatformNotification, PlatformNotificationCreate, PlatformNotificationUpdate]):
    def get_all_by_user_id_in_app(self, db: Session, user_id: str) -> List[models.PlatformNotification]:
        return db.query(self.model).filter(self.model.created_for == user_id).order_by(desc(self.model.created_at)).all()

    def count_by_user_id_in_app(self, db: Session, user_id: str) -> int:
        return db.query(self.model).filter(self.model.created_for == user_id).count()

    def create_bulk_notifications(
        self, 
        db: Session, 
        obj_in_list: List[PlatformNotificationCreate], 
        user_id: str,
        current_user
    ) -> List[models.PlatformNotification]:
        """
        Bulk create platform notifications.
        """
        # Convert PlatformNotificationCreate objects to dictionaries
        notifications_data = [
            {**obj_in.dict(), 'created_for': user_id, 'created_by': current_user.id, 'organization_id': current_user.organization_id}
            for obj_in in obj_in_list
        ]
        
        # Use bulk_insert_mappings for efficient bulk insertion
        db.bulk_insert_mappings(self.model, notifications_data)
        db.commit()
        
        # Optionally, you can return the inserted data or the inserted IDs
        return True

platform_notification = CRUDPlatformNotification(models.PlatformNotification)