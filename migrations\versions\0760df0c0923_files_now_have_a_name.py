"""files now have a name

Revision ID: 0760df0c0923
Revises: 43ee5e7082bc
Create Date: 2024-10-24 11:58:32.986751

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0760df0c0923'
down_revision = '43ee5e7082bc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contract_uploads', sa.Column('name', sa.String(), nullable=True), schema='crm_test')
    op.add_column('player_uploads', sa.Column('name', sa.String(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('player_uploads', 'name', schema='crm_test')
    op.drop_column('contract_uploads', 'name', schema='crm_test')
    # ### end Alembic commands ###