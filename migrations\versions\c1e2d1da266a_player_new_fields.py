"""Player new fields

Revision ID: c1e2d1da266a
Revises: c932e7bc27e1
Create Date: 2024-10-21 15:16:00.154384

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c1e2d1da266a'
down_revision = 'c932e7bc27e1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('comments_activity', sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_test')
    op.create_index(op.f('ix_crm_test_comments_activity_player_id'), 'comments_activity', ['player_id'], unique=False, schema='crm_test')
    op.create_foreign_key(None, 'comments_activity', 'player_records', ['player_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    op.add_column('player_records', sa.Column('expected_net_salary', sa.Float(), nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('video_link', sa.String(), nullable=True), schema='crm_test')
    op.drop_column('player_records', 'proactively_scouted', schema='crm_test')
    op.drop_column('player_records', 'agency', schema='crm_test')
    op.drop_column('player_records', 'priority_player', schema='crm_test')
    op.drop_column('player_records', 'contact_channel', schema='crm_test')
    op.drop_column('player_records', 'birth_date', schema='crm_test')
    op.drop_column('player_records', 'phone_number', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('phone_number', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('birth_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('contact_channel', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('priority_player', sa.BOOLEAN(), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('agency', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('proactively_scouted', sa.BOOLEAN(), autoincrement=False, nullable=True), schema='crm_test')
    op.drop_column('player_records', 'video_link', schema='crm_test')
    op.drop_column('player_records', 'expected_net_salary', schema='crm_test')
    op.drop_constraint(None, 'comments_activity', schema='crm_test', type_='foreignkey')
    op.drop_index(op.f('ix_crm_test_comments_activity_player_id'), table_name='comments_activity', schema='crm_test')
    op.drop_column('comments_activity', 'player_id', schema='crm_test')
    # ### end Alembic commands ###