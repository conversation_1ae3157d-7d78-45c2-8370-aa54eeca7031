"""add matchId to the model

Revision ID: 1df8e6a520ff
Revises: d07555b65a71
Create Date: 2023-04-26 14:45:47.494697

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '1df8e6a520ff'
down_revision = 'd07555b65a71'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reports', sa.Column('match_label', sa.ARRAY(sa.String()), nullable=True), schema='crm_dev')
    op.drop_column('reports', 'matchId', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reports', sa.Column('matchId', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True), schema='crm_dev')
    op.drop_column('reports', 'match_label', schema='crm_dev')
    # ### end Alembic commands ###