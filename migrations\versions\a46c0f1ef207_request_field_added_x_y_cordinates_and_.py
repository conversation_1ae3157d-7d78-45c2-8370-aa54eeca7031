"""Request field added x y cordinates and changed any team for requests only

Revision ID: a46c0f1ef207
Revises: fcfc026c1a9b
Create Date: 2025-04-24 15:01:32.240972

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a46c0f1ef207'
down_revision = 'fcfc026c1a9b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_requests', sa.Column('request_id', sa.UUID(), nullable=True), schema='crm_test')
    op.add_column('field_requests', sa.Column('x_cordinate', sa.Integer(), nullable=True), schema='crm_test')
    op.add_column('field_requests', sa.Column('y_cordinate', sa.Integer(), nullable=True), schema='crm_test')
    op.drop_index('ix_crm_test_field_requests_teamId', table_name='field_requests', schema='crm_test')
    op.create_index(op.f('ix_crm_test_field_requests_request_id'), 'field_requests', ['request_id'], unique=False, schema='crm_test')
    op.create_foreign_key(None, 'field_requests', 'team_requests', ['request_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    op.drop_column('field_requests', 'teamId', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_requests', sa.Column('teamId', sa.INTEGER(), autoincrement=False, nullable=True), schema='crm_test')
    op.drop_constraint(None, 'field_requests', schema='crm_test', type_='foreignkey')
    op.drop_index(op.f('ix_crm_test_field_requests_request_id'), table_name='field_requests', schema='crm_test')
    op.create_index('ix_crm_test_field_requests_teamId', 'field_requests', ['teamId'], unique=False, schema='crm_test')
    op.drop_column('field_requests', 'y_cordinate', schema='crm_test')
    op.drop_column('field_requests', 'x_cordinate', schema='crm_test')
    op.drop_column('field_requests', 'request_id', schema='crm_test')
    # ### end Alembic commands ###