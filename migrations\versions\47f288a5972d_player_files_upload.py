"""Player files upload

Revision ID: 47f288a5972d
Revises: ea97df333032
Create Date: 2024-10-18 16:09:50.073939

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '47f288a5972d'
down_revision = 'ea97df333032'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('player_uploads',
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.<PERSON>an(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.ForeignKeyConstraint(['player_id'], ['crm_test.player_records.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_player_uploads_created_at'), 'player_uploads', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_uploads_created_by'), 'player_uploads', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_uploads_is_sensitive'), 'player_uploads', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_uploads_last_updated'), 'player_uploads', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_uploads_organization_id'), 'player_uploads', ['organization_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_player_uploads_player_id'), 'player_uploads', ['player_id'], unique=False, schema='crm_test')
    op.drop_column('player_records', 'xgross_salary_low', schema='crm_test')
    op.drop_column('player_records', 'xtransfer_high', schema='crm_test')
    op.drop_column('player_records', 'xgross_salary_next_high', schema='crm_test')
    op.drop_column('player_records', 'rec_max_investment', schema='crm_test')
    op.drop_column('player_records', 'xtransfer_next_low', schema='crm_test')
    op.drop_column('player_records', 'xgross_salary_next_low', schema='crm_test')
    op.drop_column('player_records', 'xgross_salary_high', schema='crm_test')
    op.drop_column('player_records', 'xtransfer_next_high', schema='crm_test')
    op.drop_column('player_records', 'xtransfer_low', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('xtransfer_low', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('xtransfer_next_high', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('xgross_salary_high', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('xgross_salary_next_low', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('xtransfer_next_low', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('rec_max_investment', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('xgross_salary_next_high', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('xtransfer_high', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('player_records', sa.Column('xgross_salary_low', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True), schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_uploads_player_id'), table_name='player_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_uploads_organization_id'), table_name='player_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_uploads_last_updated'), table_name='player_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_uploads_is_sensitive'), table_name='player_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_uploads_created_by'), table_name='player_uploads', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_player_uploads_created_at'), table_name='player_uploads', schema='crm_test')
    op.drop_table('player_uploads', schema='crm_test')
    # ### end Alembic commands ###