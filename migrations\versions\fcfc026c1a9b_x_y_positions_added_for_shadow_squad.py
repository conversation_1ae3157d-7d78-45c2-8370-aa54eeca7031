"""X - Y positions added for Shadow Squad

Revision ID: fcfc026c1a9b
Revises: 5bf81585240a
Create Date: 2025-04-22 14:16:22.586931

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fcfc026c1a9b'
down_revision = '5bf81585240a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_players', sa.Column('x_cordinate', sa.Integer(), nullable=True), schema='crm_test')
    op.add_column('field_players', sa.Column('y_cordinate', sa.Integer(), nullable=True), schema='crm_test')
    op.drop_index('organizations_agency_id_idx', table_name='organizations', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('organizations_agency_id_idx', 'organizations', ['agency_id'], unique=False, schema='crm_test')
    op.drop_column('field_players', 'y_cordinate', schema='crm_test')
    op.drop_column('field_players', 'x_cordinate', schema='crm_test')
    # ### end Alembic commands ###