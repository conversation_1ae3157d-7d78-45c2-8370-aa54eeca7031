import uuid
from pydantic import (
    BaseModel,
)
from app.schemas.player_info import PlayerInfoExtended as PlayerInfo
from app.schemas.player_aggregate_stats import PlayerAggregateStats
from app.schemas.rank_record import RankRecordShort2
from typing import Optional

class RankOutputUpdate(BaseModel):
    player_rating: Optional[float]
    player_rank: Optional[int]
    feature_json: Optional[str]

class RankOutputCreate(RankOutputUpdate):
    id: uuid.UUID
    playerId: int
    player_rating: float
    player_rank: int 
    feature_json: str


class RankOutput(RankOutputCreate):
    player_info: PlayerInfo
    player_agg_stats: PlayerAggregateStats
    rank_record: RankRecordShort2
    class Config:
        orm_mode = True



class RankOutputShort(BaseModel):
    id: uuid.UUID
    playerId:int
    player_rating: float
    feature_json: str

    class Config:
        orm_mode = True


