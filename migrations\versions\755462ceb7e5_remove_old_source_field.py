"""Remove old source field

Revision ID: 755462ceb7e5
Revises: 3e436b8f903f
Create Date: 2022-11-04 11:40:13.223824

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '755462ceb7e5'
down_revision = '3e436b8f903f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_crm_dev_player_records_source_id', table_name='player_records', schema='crm_dev')
    op.drop_constraint('player_records_source_id_fkey', 'player_records', schema='crm_dev', type_='foreignkey')
    op.drop_column('player_records', 'source_id', schema='crm_dev')
    op.drop_constraint('team_requests_source_id_fkey', 'team_requests', schema='crm_dev', type_='foreignkey')
    op.drop_column('team_requests', 'source_id', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('team_requests', sa.Column('source_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm_dev')
    op.create_foreign_key('team_requests_source_id_fkey', 'team_requests', 'contacts', ['source_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.add_column('player_records', sa.Column('source_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm_dev')
    op.create_foreign_key('player_records_source_id_fkey', 'player_records', 'contacts', ['source_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.create_index('ix_crm_dev_player_records_source_id', 'player_records', ['source_id'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###