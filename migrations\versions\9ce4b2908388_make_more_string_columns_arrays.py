"""make more string columns arrays

Revision ID: 9ce4b2908388
Revises: ecb391305d9f
Create Date: 2022-08-10 07:20:20.393846

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '9ce4b2908388'
down_revision = 'ecb391305d9f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('team_requests', 'transfer_period',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               type_=sa.ARRAY(sa.String()),
               existing_nullable=True,
               schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('team_requests', 'transfer_period',
               existing_type=sa.ARRAY(sa.String()),
               type_=postgresql.ARRAY(sa.TEXT()),
               existing_nullable=True,
               schema='crm_dev')
    # ### end Alembic commands ###