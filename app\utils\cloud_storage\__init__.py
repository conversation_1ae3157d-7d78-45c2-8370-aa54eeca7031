from google.cloud import storage


from app.config import settings

class CloudStorageManager:
    def __init__(self, bucket_name: str = settings.CLOUD_STORAGE_BUCKET_NAME):
        c = storage.Client()
        self.bucket = c.bucket(bucket_name)

    def upload_blob(self, file_name: str, file: bytes) -> None:
        self.bucket.blob(file_name).upload_from_file(file)

    def get_blob(self, file_name: str) -> bytes:
        return self.bucket.blob(file_name).download_as_bytes()

    def delete_blob(self, file_name: str) -> None:
        return self.bucket.blob(file_name).delete()
    
    def check_exists(self, file_name: str) -> bool:
        return self.bucket.blob(file_name).exists()
