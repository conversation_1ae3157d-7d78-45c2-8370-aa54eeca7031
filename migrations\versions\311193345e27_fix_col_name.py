"""fix col name

Revision ID: 311193345e27
Revises: 87c0b4129638
Create Date: 2022-09-20 16:38:03.415135

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '311193345e27'
down_revision = '87c0b4129638'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contracts', sa.Column('active_status', sa.<PERSON>(), nullable=True), schema='crm_dev')
    op.drop_index('ix_crm_dev_contracts_status', table_name='contracts', schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contracts_active_status'), 'contracts', ['active_status'], unique=False, schema='crm_dev')
    op.drop_column('contracts', 'status', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contracts', sa.Column('status', sa.BOOLEAN(), autoincrement=False, nullable=True), schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contracts_active_status'), table_name='contracts', schema='crm_dev')
    op.create_index('ix_crm_dev_contracts_status', 'contracts', ['status'], unique=False, schema='crm_dev')
    op.drop_column('contracts', 'active_status', schema='crm_dev')
    # ### end Alembic commands ###