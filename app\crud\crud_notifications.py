from app.crud.crud_base import CRUDBase, ModelType
from typing import Any, Optional
from app import models
from app.schemas.notifications import NotificationsCreate, NotificationsUpdate

from sqlalchemy.orm import Session, selectinload, lazyload, Load
from typing import Any, Optional, TypeVar
from app.db.base_class import Base

ModelType = TypeVar("ModelType", bound=Base)


class CRUDNotifications(CRUDBase[models.NotificationSettings, NotificationsCreate, NotificationsUpdate]):
    ...
    def get(self, db: Session, player_id: Any) -> Optional[ModelType]:

        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).filter((self.model.player_id == player_id)).first()


notifications = CRUDNotifications(models.NotificationSettings)