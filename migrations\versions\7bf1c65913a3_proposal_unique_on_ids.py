"""proposal unique on ids

Revision ID: 7bf1c65913a3
Revises: 9c2bbcefa0de
Create Date: 2023-07-25 11:35:15.980822

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7bf1c65913a3'
down_revision = '9c2bbcefa0de'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('_request_player_org_uc_new', 'proposals', ['request_id', 'player_id', 'organization_id'], schema='crm_dev')
    # op.drop_column('team_requests', 'is_community', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('team_requests', sa.Column('is_community', sa.BOOLEAN(), autoincrement=False, nullable=True), schema='crm_dev')
    op.drop_constraint('_request_player_org_uc_new', 'proposals', schema='crm_dev', type_='unique')
    # ### end Alembic commands ###