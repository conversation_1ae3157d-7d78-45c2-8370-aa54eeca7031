from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.community_proposal import (
    CommunityProposalCreate,
    CommunityProposalUpdate,
)
from typing import List, TypeVar
from sqlalchemy.orm import Session, lazyload, Load, joinedload
from app.db.base_class import Base
from app.config import settings
from sqlalchemy import text

ModelType = TypeVar("ModelType", bound=Base)


class CRUDCommunityProposal(
    CRUDBase[
        models.CommunityProposal,
        CommunityProposalCreate,
        CommunityProposalUpdate,
    ]
):
    def get_all_proposals_to_a_request(
        self,
        db: Session,
        request_id: str,
    ) -> List[ModelType]:
        return (
            db.query(self.model)
            .options(
                joinedload(self.model.player_records).selectinload("*"),
                joinedload(self.model.creator),
                lazyload("*"),
            )
            .filter(
                self.model.request_id == request_id,
            )
            .all()
        )
    def get_all_proposals_to_a_player(
        self,
        db: Session,
        player_id: str,
    ) -> List[ModelType]:
        return (
            db.query(self.model)
            .options(
                joinedload(self.model.team_requests).selectinload("*"),
                lazyload("*"),
            )
            .filter(
                self.model.player_id == player_id,
            )
            .all()
        )
    def get_all_proposals_between_team_and_player(
        self,
        db: Session,
        request_id: str,
        player_id: str,
    ) -> List[ModelType]:
        qry = text(f"""select p.id from {settings.PG_SCHEMA}.community_proposals p
            where p.player_id= :player_id and p.request_id = :request_id""")

        result = db.execute(qry, {'player_id': player_id, 'request_id': request_id}).mappings().fetchone() 
        return bool(result)  # Return True if a result exists, otherwise False



community_proposal = CRUDCommunityProposal(models.CommunityProposal)
