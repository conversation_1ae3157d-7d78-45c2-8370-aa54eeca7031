from typing import Optional
import uuid
from pydantic import BaseModel, root_validator
import hashlib

class OrganizationUpdate(BaseModel):
    name: Optional[str]
    password: Optional[str]
    billing_email: Optional[str]
    agency_id: Optional[str]


class OrganizationCreate(OrganizationUpdate):
    name: str
    password: Optional[str]
    agency_id: Optional[str]


class Organization(OrganizationCreate):
    id: uuid.UUID

    # class Config:
    #     orm_mode = True

class OrganizationShort(BaseModel):
    id: uuid.UUID
    name: str
    password: Optional[str] = None

    class Config:
        orm_mode = True
    
    @root_validator
    def hash_password(cls, values):
        if values.get("password"):
            values['password'] = hashlib.sha512(values.get("password").encode()).hexdigest()

        return values