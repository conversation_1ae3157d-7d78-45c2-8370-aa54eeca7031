import uuid
from typing import Optional, List, Dict, Any, Literal
from datetime import datetime
from pydantic import BaseModel
from app.schemas.enums import ContractType


class ContractExtractionRequest(BaseModel):
    contract_type: Optional[ContractType] = None
    scan_mode: Literal["extract_only", "extract_and_save"] = "extract_only"
    player_id_override: Optional[uuid.UUID] = None


class ContractExtractionResult(BaseModel):
    extraction_id: str
    status: Literal["processing", "completed", "failed", "partial"]
    contract_data: Optional[Dict[str, Any]] = None
    confidence_score: Optional[float] = None
    partial_results: Optional[Dict[str, Any]] = None
    missing_fields: Optional[List[str]] = None
    error_message: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    progress: int = 0  # 0-100


class ContractExtractionStatus(BaseModel):
    extraction_id: str
    status: Literal["processing", "completed", "failed", "partial"]
    progress: int
    message: Optional[str] = None
    estimated_completion: Optional[datetime] = None


class ExtractedContractData(BaseModel):
    # Basic contract fields
    contract_type: ContractType
    start_date: Optional[str] = None
    end_date: Optional[str] = None

    # Player/Agent information (original extracted names)
    player_name: Optional[str] = None
    agent_name: Optional[str] = None

    # Entity matching results
    suggested_player_id: Optional[str] = None
    suggested_player_name: Optional[str] = None
    player_match_confidence: Optional[float] = None

    suggested_contact_id: Optional[str] = None
    suggested_contact_name: Optional[str] = None
    agent_match_confidence: Optional[float] = None

    suggested_team_id: Optional[str] = None
    suggested_team_name: Optional[str] = None
    team_match_confidence: Optional[float] = None

    # Financial information
    gross_salary: Optional[float] = None
    signing_fee: Optional[float] = None
    commission_percentage: Optional[float] = None

    # Contract specific fields
    option_years: Optional[int] = None
    coverage_region: Optional[List[str]] = None
    club_name: Optional[str] = None

    # Commission agreement specific
    commission_amounts: Optional[List[Dict[str, Any]]] = None
    installments: Optional[List[Dict[str, Any]]] = None

    # Additional fields
    exclusive: Optional[bool] = None
    termination_fee: Optional[float] = None

    # Extracted notes and summary
    notes: Optional[str] = None

    # Confidence scores per field
    field_confidence: Dict[str, float] = {}


class ContractCreationFromExtraction(BaseModel):
    extraction_id: str
    user_modifications: Optional[Dict[str, Any]] = None
    force_create: bool = False  # Create even with low confidence scores
