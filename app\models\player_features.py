from sqlalchemy import Column, ForeignKey, String
from app.db.base_class import Base
from app.config import settings
from sqlalchemy.dialects.postgresql import UUID


class PlayerFeatures(Base):
    __tablename__ = "player_features"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    player_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.player_records.id"),
        primary_key=True,
        index=True,
    )
    speed = Column(String)
    explosive = Column(String)
    dynamic = Column(String)
    build = Column(String)
    intelligence = Column(String)
    scanning = Column(String)
    pressing = Column(String)
    aggresive = Column(String)
    leader = Column(String)
    teamwork = Column(String)
    education = Column(String)
    entourage = Column(String)
    first_touch = Column(String)
    dribble = Column(String)
    progress_with_the_ball = Column(String)
    play_with_the_back = Column(String)
    diagonal_passing = Column(String)
    through_ball_passing = Column(String)
    passes_to_final_third = Column(String)
    pushes_inbetween_lines = Column(String)
    defensive_positioning = Column(String)
    attacking_positioning = Column(String)
    interceptions = Column(String)
    aerial_duels = Column(String)
    finishing = Column(String)
    heading = Column(String)
    cut_inside = Column(String)
    gk_1_vs_one_duels = Column(String)
    defensive_1_vs_one_duels = Column(String)
    closing_space = Column(String)
    ball_control = Column(String)
    reflexes = Column(String)
    set_pieces = Column(String)
