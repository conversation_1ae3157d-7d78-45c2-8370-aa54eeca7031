import uuid
from sqlalchemy import Column, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.config import settings


class AssignedToRecord(Base):
    __tablename__ = "assigned_to_record"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    contact_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.contacts.id", ondelete="CASCADE"),
        index=True,
    )
    contact = relationship(
        "Contact", primaryjoin="(AssignedToRecord.contact_id==Contact.id)"
    )

    staff_record_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.staff_records.id"),
        index=True,
    )
    player_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.player_records.id"),
        index=True,
    )
    activity_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.activity.id"),
        index=True,
    )
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
