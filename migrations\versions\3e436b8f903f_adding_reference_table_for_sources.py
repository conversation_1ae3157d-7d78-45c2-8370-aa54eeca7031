"""adding reference table for sources 

Revision ID: 3e436b8f903f
Revises: 324125c22418
Create Date: 2022-11-01 16:18:55.429997

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3e436b8f903f'
down_revision = '324125c22418'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('source_to_record',
    sa.Column('source_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('team_request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['player_id'], ['crm_dev.player_records.id'], ),
    sa.ForeignKeyConstraint(['source_id'], ['crm_dev.contacts.id'], ),
    sa.ForeignKeyConstraint(['team_request_id'], ['crm_dev.team_requests.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_source_to_record_player_id'), 'source_to_record', ['player_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_source_to_record_source_id'), 'source_to_record', ['source_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_source_to_record_team_request_id'), 'source_to_record', ['team_request_id'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_dev_source_to_record_team_request_id'), table_name='source_to_record', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_source_to_record_source_id'), table_name='source_to_record', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_source_to_record_player_id'), table_name='source_to_record', schema='crm_dev')
    op.drop_table('source_to_record', schema='crm_dev')
    # ### end Alembic commands ###