"""Add hide field param to football field

Revision ID: 14fb06a836f9
Revises: 5340594caad1
Create Date: 2025-05-27 14:48:25.604670

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '14fb06a836f9'
down_revision = '5340594caad1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('share_tokens', sa.Column('hide_field_view', sa.<PERSON>()), schema='crm_test')
    op.drop_index('share_tokens_resource_id_idx', table_name='share_tokens', schema='crm_test')
    op.drop_index('share_tokens_resource_type_idx', table_name='share_tokens', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('share_tokens_resource_type_idx', 'share_tokens', ['resource_type'], unique=False, schema='crm_test')
    op.create_index('share_tokens_resource_id_idx', 'share_tokens', ['resource_id'], unique=False, schema='crm_test')
    op.drop_column('share_tokens', 'hide_field_view', schema='crm_test')
    # ### end Alembic commands ###