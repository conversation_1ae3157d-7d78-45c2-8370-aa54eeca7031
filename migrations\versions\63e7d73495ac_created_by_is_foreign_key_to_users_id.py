"""created_by is foreign key to users.id

Revision ID: 63e7d73495ac
Revises: 29eff94a390c
Create Date: 2022-08-03 16:07:24.244180

"""
from alembic import op
import sqlalchemy as sa
from fastapi_users_db_sqlalchemy.generics import GUID


# revision identifiers, used by Alembic.
revision = "63e7d73495ac"
down_revision = "29eff94a390c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "contacts",
        "created_by",
        existing_type=sa.VARCHAR(),
        type_=GUID(),
        existing_nullable=True,
        schema="crm_dev",
    )
    op.drop_index(
        "ix_crm_dev_contacts_created_by", table_name="contacts", schema="crm_dev"
    )
    op.create_foreign_key(
        None,
        "contacts",
        "user",
        ["created_by"],
        ["id"],
        source_schema="crm_dev",
        referent_schema="crm_dev",
    )
    op.alter_column(
        "player_records",
        "created_by",
        existing_type=sa.VARCHAR(),
        type_=GUID(),
        existing_nullable=True,
        schema="crm_dev",
    )
    op.drop_index(
        "ix_crm_dev_player_records_created_by",
        table_name="player_records",
        schema="crm_dev",
    )
    op.create_foreign_key(
        None,
        "player_records",
        "user",
        ["created_by"],
        ["id"],
        source_schema="crm_dev",
        referent_schema="crm_dev",
    )
    op.alter_column(
        "proposals",
        "created_by",
        existing_type=sa.VARCHAR(),
        type_=GUID(),
        existing_nullable=True,
        schema="crm_dev",
    )
    op.drop_index(
        "ix_crm_dev_proposals_created_by", table_name="proposals", schema="crm_dev"
    )
    op.create_foreign_key(
        None,
        "proposals",
        "user",
        ["created_by"],
        ["id"],
        source_schema="crm_dev",
        referent_schema="crm_dev",
    )
    op.alter_column(
        "reports",
        "created_by",
        existing_type=sa.VARCHAR(),
        type_=GUID(),
        existing_nullable=True,
        schema="crm_dev",
    )
    op.drop_index(
        "ix_crm_dev_reports_created_by", table_name="reports", schema="crm_dev"
    )
    op.create_foreign_key(
        None,
        "reports",
        "user",
        ["created_by"],
        ["id"],
        source_schema="crm_dev",
        referent_schema="crm_dev",
    )
    op.alter_column(
        "team_requests",
        "created_by",
        existing_type=sa.VARCHAR(),
        type_=GUID(),
        existing_nullable=True,
        schema="crm_dev",
    )
    op.drop_index(
        "ix_crm_dev_team_requests_created_by",
        table_name="team_requests",
        schema="crm_dev",
    )
    op.create_foreign_key(
        None,
        "team_requests",
        "user",
        ["created_by"],
        ["id"],
        source_schema="crm_dev",
        referent_schema="crm_dev",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "team_requests", schema="crm_dev", type_="foreignkey")
    op.create_index(
        "ix_crm_dev_team_requests_created_by",
        "team_requests",
        ["created_by"],
        unique=False,
        schema="crm_dev",
    )
    op.alter_column(
        "team_requests",
        "created_by",
        existing_type=GUID(),
        type_=sa.VARCHAR(),
        existing_nullable=True,
        schema="crm_dev",
    )
    op.drop_constraint(None, "reports", schema="crm_dev", type_="foreignkey")
    op.create_index(
        "ix_crm_dev_reports_created_by",
        "reports",
        ["created_by"],
        unique=False,
        schema="crm_dev",
    )
    op.alter_column(
        "reports",
        "created_by",
        existing_type=GUID(),
        type_=sa.VARCHAR(),
        existing_nullable=True,
        schema="crm_dev",
    )
    op.drop_constraint(None, "proposals", schema="crm_dev", type_="foreignkey")
    op.create_index(
        "ix_crm_dev_proposals_created_by",
        "proposals",
        ["created_by"],
        unique=False,
        schema="crm_dev",
    )
    op.alter_column(
        "proposals",
        "created_by",
        existing_type=GUID(),
        type_=sa.VARCHAR(),
        existing_nullable=True,
        schema="crm_dev",
    )
    op.drop_constraint(None, "player_records", schema="crm_dev", type_="foreignkey")
    op.create_index(
        "ix_crm_dev_player_records_created_by",
        "player_records",
        ["created_by"],
        unique=False,
        schema="crm_dev",
    )
    op.alter_column(
        "player_records",
        "created_by",
        existing_type=GUID(),
        type_=sa.VARCHAR(),
        existing_nullable=True,
        schema="crm_dev",
    )
    op.drop_constraint(None, "contacts", schema="crm_dev", type_="foreignkey")
    op.create_index(
        "ix_crm_dev_contacts_created_by",
        "contacts",
        ["created_by"],
        unique=False,
        schema="crm_dev",
    )
    op.alter_column(
        "contacts",
        "created_by",
        existing_type=GUID(),
        type_=sa.VARCHAR(),
        existing_nullable=True,
        schema="crm_dev",
    )
    # ### end Alembic commands ###