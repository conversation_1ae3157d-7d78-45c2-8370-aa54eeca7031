from typing import List
from sqlalchemy.orm import Session, Load, lazyload

from fastapi import <PERSON><PERSON><PERSON>, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse

from app.models.team_request import TeamRequest
from app.schemas.team_request import TeamRequestExport
from app.api import deps
from .config import settings

from app.api.api import api_router
from app.api.endpoints.users.auth import fastapi_users
from fastapi_cache import close_caches
from app.api.endpoints.users.auth import fastapi_users
from firebase_admin import initialize_app, credentials
from fastapi import Request
import time

app = FastAPI(docs_url=settings.DOCS_URL, redoc_url=settings.REDOC_URL)
cred = credentials.Certificate(settings.FIREBASE_CREDS)
try:
    initialize_app(credential=cred)
except:
    pass


import logging

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)
localhost = "http://localhost:3000" if settings.TEST else None
origins = [
    settings.FRONTEND_URL_PROD,
    settings.FRONTEND_URL_DEV,
    settings.FRONTEND_URL_STAGING,
    settings.FRONTEND_URL_TESTING,
    localhost
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def maintenance_middleware(request: Request, call_next):
    if settings.MAINTENANCE_MODE:
        return HTMLResponse(content="""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Maintenance Mode</title>
                <style>
                    body { text-align: center; padding: 50px; font-family: Arial, sans-serif; }
                    h1 { font-size: 50px; }
                    p { font-size: 20px; color: #555; }
                </style>
            </head>
            <body>
                <h1>🚧 We’re Under Maintenance 🚧</h1>
                <p>Sorry for the inconvenience, but we’ll be back shortly!</p>
            </body>
            </html>
        """, status_code=503)
    return await call_next(request)

@app.on_event("startup")
async def configure_db_and_routes():

    app.fastapi_users = fastapi_users


@app.get(
    "/team_requests/export/{organization_id}",
    response_description="List read-only view of requests with filtered fields",
    response_model=List[TeamRequestExport],
)
def list_team_requests(
    organization_id: str,
    db: Session = Depends(deps.get_db),
):
    return (
        db.query(TeamRequest)
        .options(Load(TeamRequest).selectinload("*"), lazyload("*"))
        .filter(TeamRequest.organization_id == organization_id)
        .all()
    )

    # "proposed_players",
    # "teamId",
    ## "id",
    # "changelog",
    # "created_at",
    ## "last_updated",
    # "created_by",
    # "reason_for_outcome",


app.include_router(api_router)


@app.on_event("shutdown")
async def on_shutdown() -> None:
    await close_caches()
