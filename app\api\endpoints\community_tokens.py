from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.schemas.community_tokens import (
    CommunityTokens,
    CommunityTokensCreate,
    CommunityTokensUpdate,
)
from app import crud, models
from app.api import deps
from app.config import settings
from sqlalchemy import text

router = APIRouter()


@router.get("/", response_model=CommunityTokens)
def get_tokens_by_organization(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Get amount of community request tokens by organization ID.
    """
    query = text(
        f"""
        SELECT *
        FROM {settings.PG_SCHEMA}.community_tokens
        WHERE organization_id = :organization_id
        """
    )
    tokens = (
        db.execute(query, {"organization_id": current_user.organization_id})
        .mappings()
        .first()
    )
    if tokens is None:
        return {"tokens": 0}
    return tokens


@router.get("/organizations")
def get_all_organizations_with_tokens(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    if current_user.is_superuser:
        query = text(
            f"""
            SELECT o.id, o.name, c.tokens
            FROM {settings.PG_SCHEMA}.organizations o
            JOIN {settings.PG_SCHEMA}.community_tokens c ON c.organization_id = o.id
            """
        )
        results = db.execute(query).mappings().all()
        return results
    else:
        raise HTTPException(
            status_code=401, detail="You don't have permissions to view tokens"
        )


@router.get("/new_organizations")
def get_all_organizations_without_tokens(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    if current_user.is_superuser:
        query = text(
            f"""
            SELECT o.id, o.name
            FROM {settings.PG_SCHEMA}.organizations o
            WHERE o.id NOT IN (
                SELECT organization_id
                FROM {settings.PG_SCHEMA}.community_tokens
            )
            """
        )
        results = db.execute(query).mappings().all()
        return results
    else:
        raise HTTPException(
            status_code=401, detail="You don't have permissions to view tokens"
        )


@router.post("/{organization_id}", response_model=CommunityTokens)
def upsert_community_tokens_for_organization(
    *,
    community_tokens_in: CommunityTokensCreate,
    db: Session = Depends(deps.get_db),
    organization_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
):
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=401, detail="You don't have permissions to modify tokens"
        )

    existing_tokens = crud.community_tokens.get_for_org(
        db=db, organization_id=organization_id
    )

    if existing_tokens:
        # Update existing tokens with the new absolute value
        updated_tokens = crud.community_tokens.update(
            db=db,
            db_obj=existing_tokens,
            obj_in=community_tokens_in,
        )
        return updated_tokens
    else:
        # Create new tokens record
        new_tokens = crud.community_tokens.create(
            db=db,
            obj_in=CommunityTokensCreate(
                organization_id=organization_id, tokens=community_tokens_in.tokens
            ),
        )
        return new_tokens
