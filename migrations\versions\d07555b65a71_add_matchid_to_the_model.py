"""add matchId to the model

Revision ID: d07555b65a71
Revises: 8bc0c09a6bf7
Create Date: 2023-04-26 14:44:32.126712

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd07555b65a71'
down_revision = '8bc0c09a6bf7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reports', sa.Column('matchId', sa.ARRAY(sa.Integer()), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('reports', 'matchId', schema='crm_dev')
    # ### end Alembic commands ###