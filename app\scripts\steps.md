## Sequence

0.  Populate organization with a record for enskai

0. Move contacts collection to contacts table, this should be before players and requests so that foreign keys can point to the right records

1. Move players from mongo players collection to crm_dev.player_records. id field needs to be reassigned to a uuid that is different from wyscout playerId, so that we can potentially have multiple records for the same player. Fields that we are now getting from wyscout should be ommitted. These include: 

    firstName: str
    lastName: str
    birth_area: Optional[str] = None
    passport: Optional[str] = None
    foot: Optional[Foot]
    birth_date: Optional[date] = None
    tm_link: Optional[str] = None
    tm_value: Optional[float] = None
    teamId: Optional[int] = None
    team_name: Optional[str] = None


The changelog, assigned_to, source, deals_with_player should be skipped as well since they are now relations rather than embeded docs. Assigned_to_id and source_id should be created as columns and populated with the ids of the embedded docs. Add an organization id field that points to enskai's record

3. For each player, the changelog needs to be move to player_changes, where each item in the changelog array is a row in the table. Each change should get a playerId field to link to the player and and id field that is uuid to serve as index. 

4. Move team_requests in a similar way, skip columns on team info: 
    teamId: int
    name: str
    area_name: str
    division_level: int

    Skip proposed_players, changelog, players, source since those are relations now. Add an organization id field that points to enskai's record
5. Team_request_changes same sa the player ones

6. Reports move to reports table, create player_id column based on player_id (the one that has been created as new)

7. Proposals - take either player.deals_with_player or team_request.proposed_players (should be indetical) and for each item in the array create a new row in the proposals table

