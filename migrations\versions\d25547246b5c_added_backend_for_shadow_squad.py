"""Added backend for shadow squad

Revision ID: d25547246b5c
Revises: 3d40b0c3da0a
Create Date: 2023-10-12 16:27:24.801484

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = 'd25547246b5c'
down_revision = '3d40b0c3da0a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('football_fields',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('club_name', sa.String(), nullable=True),
    sa.Column('for_date', sa.DateTime(), nullable=True),
    sa.Column('formation', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_football_fields_created_at'), 'football_fields', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_football_fields_created_by'), 'football_fields', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_football_fields_is_sensitive'), 'football_fields', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_football_fields_last_updated'), 'football_fields', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_football_fields_name'), 'football_fields', ['name'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_football_fields_organization_id'), 'football_fields', ['organization_id'], unique=False, schema='crm_test')
    op.create_table('field_players',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('field_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('playerId', sa.Integer(), nullable=True),
    sa.Column('suitability_score', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['field_id'], ['crm_test.football_fields.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_field_players_field_id'), 'field_players', ['field_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_field_players_playerId'), 'field_players', ['playerId'], unique=False, schema='crm_test')
    op.drop_constraint('contracts_agent_id_fkey', 'contracts', schema='crm_test', type_='foreignkey')
    op.create_foreign_key(None, 'contracts', 'contacts', ['agent_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='SET NULL')
    op.drop_constraint('notifications_settings_user_id_fkey', 'notifications_settings', schema='crm_test', type_='foreignkey')
    op.create_foreign_key(None, 'notifications_settings', 'contacts', ['user_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='cascade')
    op.drop_constraint('player_records_assigned_to_id_fkey', 'player_records', schema='crm_test', type_='foreignkey')
    op.create_foreign_key(None, 'player_records', 'contacts', ['assigned_to_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    op.drop_constraint('source_to_record_source_id_fkey', 'source_to_record', schema='crm_test', type_='foreignkey')
    op.create_foreign_key(None, 'source_to_record', 'contacts', ['source_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'source_to_record', schema='crm_test', type_='foreignkey')
    op.create_foreign_key('source_to_record_source_id_fkey', 'source_to_record', 'contacts', ['source_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_constraint(None, 'player_records', schema='crm_test', type_='foreignkey')
    op.create_foreign_key('player_records_assigned_to_id_fkey', 'player_records', 'contacts', ['assigned_to_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_constraint(None, 'notifications_settings', schema='crm_test', type_='foreignkey')
    op.create_foreign_key('notifications_settings_user_id_fkey', 'notifications_settings', 'contacts', ['user_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_constraint(None, 'contracts', schema='crm_test', type_='foreignkey')
    op.create_foreign_key('contracts_agent_id_fkey', 'contracts', 'contacts', ['agent_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_index(op.f('ix_crm_test_field_players_playerId'), table_name='field_players', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_field_players_field_id'), table_name='field_players', schema='crm_test')
    op.drop_table('field_players', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_football_fields_organization_id'), table_name='football_fields', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_football_fields_name'), table_name='football_fields', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_football_fields_last_updated'), table_name='football_fields', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_football_fields_is_sensitive'), table_name='football_fields', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_football_fields_created_by'), table_name='football_fields', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_football_fields_created_at'), table_name='football_fields', schema='crm_test')
    op.drop_table('football_fields', schema='crm_test')
    # ### end Alembic commands ###