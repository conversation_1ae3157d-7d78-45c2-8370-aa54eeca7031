"""Comment is now separate table

Revision ID: ca8af3dd0c7e
Revises: ba1dd6d161c3
Create Date: 2024-08-12 15:17:50.085816

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ca8af3dd0c7e'
down_revision = 'ba1dd6d161c3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('comments',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('comment', sa.String(), nullable=True),
    sa.Column('time', sa.DateTime(), nullable=True),
    sa.Column('creator', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_comments_time'), 'comments', ['time'], unique=False, schema='crm_test')
    op.add_column('activity', sa.Column('comment_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_test')
    op.alter_column('activity', 'notes',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_test')
    op.create_index(op.f('ix_crm_test_activity_comment_id'), 'activity', ['comment_id'], unique=False, schema='crm_test')
    op.create_foreign_key(None, 'activity', 'comments', ['comment_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'activity', schema='crm_test', type_='foreignkey')
    op.drop_index(op.f('ix_crm_test_activity_comment_id'), table_name='activity', schema='crm_test')
    op.alter_column('activity', 'notes',
               existing_type=sa.String(),
               type_=postgresql.JSON(astext_type=sa.Text()),
               existing_nullable=True,
               schema='crm_test')
    op.drop_column('activity', 'comment_id', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_comments_time'), table_name='comments', schema='crm_test')
    op.drop_table('comments', schema='crm_test')
    # ### end Alembic commands ###