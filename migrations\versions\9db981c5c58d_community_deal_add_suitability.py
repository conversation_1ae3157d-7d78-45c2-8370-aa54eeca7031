"""Community deal - add suitability

Revision ID: 9db981c5c58d
Revises: 28cb267fcb05
Create Date: 2025-04-01 14:25:17.218710

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9db981c5c58d'
down_revision = '28cb267fcb05'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('community_deal', sa.Column('suitability_score', sa.Float(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('community_deal', 'suitability_score', schema='crm_test')
    # ### end Alembic commands ###