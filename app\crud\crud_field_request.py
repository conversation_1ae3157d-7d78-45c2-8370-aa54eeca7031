from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.field_requests import FieldRequestsCreate, FieldRequestsUpdate
from sqlalchemy.orm import Session


class CRUDFieldRequests(
    CRUDBase[models.FieldRequests, FieldRequestsCreate, FieldRequestsUpdate]
):

    def delete_all_requests(self, db: Session, field_id: str):
        requests_to_delete = db.query(models.FieldRequests).filter(models.FieldRequests.field_id == field_id).all()
    
        # Delete each request
        for request in requests_to_delete:
            db.delete(request)  
       
        db.commit()

        return requests_to_delete


request_in_the_field = CRUDFieldRequests(models.FieldRequests)
