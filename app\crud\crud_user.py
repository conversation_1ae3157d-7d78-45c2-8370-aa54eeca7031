from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.user import UserCreate, UserUpdate
from sqlalchemy.orm import Session

class CRUDUser(CRUDBase[models.User,  UserCreate, UserUpdate]):
    def get_by_email(
        self, 
        db: Session, 
        email: str
    ) -> models.User:
        # Query the database for a user with the given email
        return db.query(models.User).filter(models.User.email == email).first()

    def stop_user(
        self, 
        db: Session, 
        id: int
    ) -> models.User:
        # Query the database for the user by ID
        user = db.query(models.User).filter(models.User.id == id).first()
        if user:
            # Set the required fields to False
            user.is_active = False
            user.is_enabled = False
            db.add(user)  # Mark the user object as dirty
            db.commit()   # Commit the transaction to save changes
            db.refresh(user)  # Refresh the user object to get updated values
        return user

    def update_phone_and_enable_whatsapp(
    self, 
    db: Session, 
    user_id: str, 
    phone_number: str
) -> models.User:
        user = db.query(models.User).filter(models.User.id == user_id).first()
        if user:
            user.phone_number = phone_number
            user.use_whatsapp = True
            db.add(user)
            db.commit()
            db.refresh(user)
        return user

    def disable_whatsapp(
        self, 
        db: Session, 
        user_id: str
    ) -> models.User:
        user = db.query(models.User).filter(models.User.id == user_id).first()
        if user:
            user.use_whatsapp = False
            db.add(user)
            db.commit()
            db.refresh(user)
        return user


user = CRUDUser(models.User)