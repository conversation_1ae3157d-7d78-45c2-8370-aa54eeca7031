"""migrate test to staging

Revision ID: 5853ca8b643c
Revises: 1b102ef437ed
Create Date: 2023-08-04 11:17:19.039804

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '5853ca8b643c'
down_revision = '1b102ef437ed'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('community_proposals',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('club_asking_price', sa.Float(), nullable=True),
    sa.Column('expected_salary', sa.Float(), nullable=True),
    sa.Column('are_you_the_agent', sa.Boolean(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('request_creator', sa.String(), nullable=True),
    sa.Column('player_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['crm_dev.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_dev.organizations.id'], ),
    sa.ForeignKeyConstraint(['player_id'], ['crm_dev.player_records.id'], ),
    sa.ForeignKeyConstraint(['request_id'], ['crm_dev.team_requests.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('request_id', 'player_id', 'organization_id', name='_community_request_player_org_uc_new'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_community_proposals_created_at'), 'community_proposals', ['created_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_community_proposals_created_by'), 'community_proposals', ['created_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_community_proposals_is_sensitive'), 'community_proposals', ['is_sensitive'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_community_proposals_last_updated'), 'community_proposals', ['last_updated'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_community_proposals_organization_id'), 'community_proposals', ['organization_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_community_proposals_player_id'), 'community_proposals', ['player_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_community_proposals_request_id'), 'community_proposals', ['request_id'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_dev_community_proposals_request_id'), table_name='community_proposals', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_community_proposals_player_id'), table_name='community_proposals', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_community_proposals_organization_id'), table_name='community_proposals', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_community_proposals_last_updated'), table_name='community_proposals', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_community_proposals_is_sensitive'), table_name='community_proposals', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_community_proposals_created_by'), table_name='community_proposals', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_community_proposals_created_at'), table_name='community_proposals', schema='crm_dev')
    op.drop_table('community_proposals', schema='crm_dev')
    # ### end Alembic commands ###