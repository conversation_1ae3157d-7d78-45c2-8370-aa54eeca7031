from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.football_field import (
    FootballFieldCreate,
    FootballFieldUpdate,
    FootballFieldViewAll,
)
from sqlalchemy.orm import Session
from sqlalchemy.orm import aliased
from sqlalchemy import and_, exists, select
import datetime
from typing import Union, Dict, Any
from fastapi.encoders import jsonable_encoder

class CRUDFootballField(
    CRUDBase[models.FootballField, FootballFieldCreate, FootballFieldUpdate]
):
    def get_all_fields(self, db: Session, org_id: str):
        ShareTokenAlias = aliased(models.ShareToken)
        now = datetime.datetime.utcnow()

        subq = (
            select(ShareTokenAlias.id)
            .where(
                and_(
                    ShareTokenAlias.resource_type == "football_field",
                    ShareTokenAlias.resource_id == self.model.id,
                    ShareTokenAlias.expires_at > now,
                    ShareTokenAlias.is_disabled == False,
                )
            )
            .correlate(self.model)
        )

        fields = (
            db.query(self.model, exists(subq).label("has_shared_link"))
            .filter(self.model.organization_id == org_id)
            .order_by(self.model.last_updated.desc())
            .all()
        )

        results = []
        for f, has_shared_link in fields:
            item = FootballFieldViewAll(
                id=f.id,
                players=len(f.players or []),
                name=f.name,
                last_updated=f.last_updated,
                team_name=f.team.name if f.team else "",
                created_by=f.creator.email,
                has_shared_link=has_shared_link,
            )
            results.append(item)

        return results

    def update_with_contact_fields(
        self,
        db: Session,
        *,
        db_obj: models.FootballField,
        obj_in: Union[FootballFieldUpdate, Dict[str, Any]],
    ) -> models.FootballField:
        """Special update method that handles contact fields properly"""
        update_data = jsonable_encoder(obj_in) if not isinstance(obj_in, dict) else obj_in
        
        # Handle contact fields explicitly
        contact_fields = ["contact_name", "contact_email", "contact_phone"]
        for field in contact_fields:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        
        # Use standard update for other fields
        return super().update(db=db, db_obj=db_obj, obj_in=obj_in)


football_field = CRUDFootballField(models.FootballField)
