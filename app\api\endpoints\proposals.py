from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import exc

from app.schemas.proposal import Proposal, ProposalUpdate, ProposalCreate
from app import crud, models
from app.api import deps, utils
from app.schemas.activity import ActivityCreate, Activity
from datetime import datetime

router = APIRouter()


@router.get("/", response_model=List[Proposal])
def read_proposals(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve proposals.
    """
    utils.check_get_all(Proposal, current_user)

    return crud.proposal.get_all_w_org(
        db, current_user.organization_id, utils.can_access_sensitive(current_user)
    )


@router.post("/", response_model=Optional[Activity])
def create_proposal(
    *,
    db: Session = Depends(deps.get_db),
    proposal_in: ProposalCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new proposal.
    """
    utils.check_create(ActivityCreate, current_user)

    player = crud.player_record.get(db=db, id=proposal_in.player_id)
    team_request = crud.team_request.get(db=db, id=proposal_in.request_id)
    date_deal_created = None if proposal_in.stage != 'offered' else datetime.now()
    source_ids = [s.source.id for s in team_request.source_to_record if s.source.contact_type == 'internal']
    assigned_ids = [s.contact.id for s in player.assigned_to_record if s.contact.contact_type == 'internal']
    all_ids = source_ids + assigned_ids
    utils.check_same_org(player, team_request, current_user)
    
    activity = ActivityCreate(playerId=player.playerId, teamId=team_request.teamId, stage=proposal_in.stage,
                              title=f"{player.player_info.shortName} to {team_request.team_info.name}", type='deal',
                              assigned_to_record=list(set(all_ids)), date_deal_created=date_deal_created)
    return crud.activity.create_with_user_assigne(db=db, obj_in=activity, user=current_user)
    # Old Proposel - now changed with community
    #try:
    #    proposal = crud.proposal.create_with_user(
    #        db=db,
    #        obj_in=proposal_in,
    #        user=current_user,
    #    )
    #    return proposal
    #except exc.IntegrityError as e:
    #    db.rollback()
    #    return JSONResponse(content=str(e), status_code=500)


@router.put("/{id}", response_model=Proposal)
def update_proposal(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    proposal_in: ProposalUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a proposal.
    """
    proposal = crud.proposal.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_modify(proposal, current_user)

    player = crud.player_record.get(db=db, id=proposal_in.player_id)
    team_request = crud.team_request.get(db=db, id=proposal_in.request_id)
    utils.check_same_org(player, team_request, current_user)

    proposal = crud.proposal.update(db=db, db_obj=proposal, obj_in=proposal_in)
    return proposal


@router.get("/{id}", response_model=Proposal)
def read_proposal(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get proposal by ID.
    """
    proposal = crud.proposal.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    if not proposal:
        raise HTTPException(status_code=404, detail="Proposal not found")
    utils.check_get_one(proposal, current_user)
    return proposal


@router.delete("/{id}", response_model=Proposal)
def delete_proposal(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete an proposal.
    """
    proposal = crud.proposal.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_delete(proposal, current_user)
    utils.check_same_org(item=proposal, user=current_user, item_s=None)

    proposal_out = Proposal.from_orm(proposal)
    crud.proposal.remove(db=db, id=id)
    return proposal_out
