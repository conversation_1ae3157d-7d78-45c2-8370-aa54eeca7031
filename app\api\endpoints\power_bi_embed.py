from app import models
from fastapi import APIRouter, Depends
from app.api import deps
from app.utils.power_bi.pbi_embed_service import PbiEmbedService
import json
try:
    from ...config import settings
except:
    from app.config import settings

router = APIRouter()

@router.get("/getembedinfo")
def get_embed_info(
    *,
    current_user: models.User = Depends(deps.get_current_active_user),
):
    username = current_user.email
    try:
        return PbiEmbedService().get_embed_params_for_single_report(
            settings.WORKSPACE_ID, settings.REPORT_ID, username,
        )
    except Exception as ex:
        return json.dumps({"errorMsg": str(ex)}), 500
