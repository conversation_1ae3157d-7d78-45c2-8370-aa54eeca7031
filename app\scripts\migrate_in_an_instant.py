# from lib2to3.pgen2.pgen import DFAState
# from pyparsing import col, dictOf
# from sqlalchemy import create_engine
# import sys
# import pymongo
# import uuid
# import pandas as pd
# import numpy as np

# sys.path.append("c:\\Projects\\shadow-eleven-backend")
# from app.config import settings

# pd.set_option("display.max_columns", None)


# class Migrator:
#     def __init__(self):
#         self.mongodb_client = pymongo.MongoClient(settings.DB_URL)
#         self.mongo_db = self.mongodb_client.get_default_database()
#         self.postgres_db = create_engine(settings.PG_URL)
#         self.enskai_uuid = "ebfdb98e-9af4-4ba0-a437-4ce1997132e1"
#         self.chestnite_uuid = uuid.uuid4()
#         self.ivan_id = "d1736a70-2dfe-457d-95d0-d4f1cc8c943a"
#         self.id_map = {
#             "<EMAIL>": (
#                 "0d18c1b6-cb7f-41ff-bb11-f3ec200f55bd"
#             ),
#             "<EMAIL>": (
#                 "caf039fa-09d8-41e5-a5eb-c034e9fcaf66"
#             ),
#             "<EMAIL>": "35028c0f-ff51-4988-ab97-6e690a459864",
#             "<EMAIL>": "4d7d045d-25cb-4be7-96d2-b3bb62787288",
#             "<EMAIL>": "d1736a70-2dfe-457d-95d0-d4f1cc8c943a",
#             "<EMAIL>": "460e82ff-9654-4a85-88c3-6f96074ea613",
#         }

#     def print_from_mongo(self):
#         df = pd.DataFrame(list(self.mongo_db["team_requests"].find()))
#         return df

#     def populate_with_record_for_enskai(self):
#         self.postgres_db.execute(
#             f"INSERT INTO {settings.PG_SCHEMA}.organizations (id, name,"
#             f" description) VALUES ('{self.enskai_uuid}', 'EnskAI', 'The top"
#             " Gs')"
#         )
#         self.postgres_db.execute(
#             f"INSERT INTO {settings.PG_SCHEMA}.organizations (id, name,"
#             f" description) VALUES ('{self.chestnite_uuid}', 'Chestnite',"
#             " 'These homies poor')"
#         )

#     def migrate_roles(self):
#         self.postgres_db.execute(
#             f"INSERT INTO {settings.PG_SCHEMA}.user_roles (id, name,"
#             f" description) VALUES ('{self.enskai_uuid}', 'Boss', 'The top G')"
#         )
#         self.postgres_db.execute(
#             f"INSERT INTO {settings.PG_SCHEMA}.user_roles (id, name,"
#             f" description) VALUES ('{self.enskai_uuid}', 'Peasent', 'The not"
#             " so top G')"
#         )

#     def migrate_users(self):
#         ll_dicts = list(self.mongo_db["users"].find())
#         self.ivan_id = [
#             item
#             for item in ll_dicts
#             if item["email"] == "<EMAIL>"
#         ][0]["id"]
#         df = pd.DataFrame(ll_dicts)
#         df = df.drop(columns=["_id"])
#         df["is_verified"] = True
#         df["organization_id"] = self.enskai_uuid
#         df["role_id"] = "00e55bab-3688-4bb7-8ac5-85263fd769f2"
#         df.to_sql(
#             "user",
#             self.postgres_db,
#             if_exists="append",
#             index=False,
#             schema=settings.PG_SCHEMA,
#         )

#     def migrate_contacts(self):
#         df = pd.DataFrame(self.mongo_db["contacts"].find())
#         df = df.rename(
#             columns={"_id": "id", "organization": "contact_organization"}
#         )
#         df = df.drop(columns=["changelog"])
#         df["organization_id"] = self.enskai_uuid
#         df["created_by"] = df["created_by"].apply(lambda x: self.id_map[x])
#         df.to_sql(
#             "contacts",
#             self.postgres_db,
#             if_exists="append",
#             index=False,
#             schema=settings.PG_SCHEMA,
#         )

#     def migrate_players(self):
#         df = pd.DataFrame(list(self.mongo_db["players"].find()))
#         df_copy = df.copy()
#         df = df.drop(
#             columns=[
#                 "_id",
#                 "firstName",
#                 "lastName",
#                 "birth_area",
#                 "passport",
#                 "foot",
#                 "birth_date",
#                 "tm_link",
#                 "tm_value",
#                 "teamId",
#                 "team_name",
#                 "deals_with_player",
#                 "assigned_to",
#                 "source",
#                 "scouting_reports",
#                 "player_role",
#                 "changelog",
#             ]
#         )
#         dd = {
#             name: np.nan
#             for name in (
#                 "speed",
#                 "explosive",
#                 "dynamic",
#                 "build",
#                 "intelligence",
#                 "scanning",
#                 "pressing",
#                 "aggresive",
#                 "leader",
#                 "teamwork",
#                 "education",
#                 "entourage",
#                 "first_touch",
#                 "dribble",
#                 "progress_with_the_ball",
#                 "play_with_the_back",
#                 "diagonal_passing",
#                 "through_ball_passing",
#                 "passes_to_final_third",
#                 "pushes_inbetween_lines",
#                 "defensive_positioning",
#                 "attacking_positioning",
#                 "interceptions",
#                 "aerial_duels",
#                 "finishing",
#                 "heading",
#                 "cut_inside",
#                 "gk_1_vs_one_duels",
#                 "defensive_1_vs_one_duels",
#                 "closing_space",
#                 "ball_control",
#                 "reflexes",
#                 "set_pieces",
#             )
#         }
#         ll_to_df = []
#         df["organization_id"] = self.enskai_uuid
#         df["created_by"] = df["created_by"].apply(lambda x: self.id_map[x])
#         for i, row in df_copy.iterrows():
#             p_id = uuid.uuid4()
#             assigned_to = (
#                 row["assigned_to"]["id"]
#                 if isinstance(row["assigned_to"], dict)
#                 else None
#             )
#             source_id = (
#                 row["source"]["id"] if isinstance(row["source"], dict) else None
#             )
#             df.at[i, "id"] = p_id

#             copied_dd = dd.copy()
#             copied_dd["player_id"] = p_id
#             ll_to_df.append(copied_dd)
#             df.at[i, "assigned_to_id"] = assigned_to
#             df.at[i, "source_id"] = source_id
#         self.player_df_log = pd.concat([df["id"], df_copy["changelog"]], axis=1)
#         self.player_reports = pd.concat(
#             [df["id"], df_copy["scouting_reports"]], axis=1
#         )
#         self.player_reports["is_sensitive"] = False

#         self.deals_with_player = pd.concat(
#             [df["id"], df_copy["deals_with_player"]], axis=1
#         )
#         self.deals_with_player["is_sensitive"] = False

#         features_df = pd.DataFrame(ll_to_df)

#         df["is_sensitive"] = False
#         df.to_sql(
#             "player_records",
#             self.postgres_db,
#             if_exists="append",
#             index=False,
#             schema=settings.PG_SCHEMA,
#         )

#         features_df.to_sql(
#             "player_features",
#             self.postgres_db,
#             if_exists="append",
#             index=False,
#             schema=settings.PG_SCHEMA,
#         )

#     def migrate_player_logs(self):
#         for i, row in self.player_df_log.iterrows():
#             if row["changelog"]:
#                 dd = {
#                     "id": [],
#                     "edit_at": [],
#                     "edit_by": [],
#                     "field": [],
#                     "previous": [],
#                     "updated": [],
#                     "player_id": [],
#                 }
#                 for item in row["changelog"]:
#                     dd["id"].append(uuid.uuid4())
#                     dd["edit_at"].append(item["edit_at"])
#                     dd["edit_by"].append(self.id_map[item["edit_by"]])
#                     dd["field"].append(item["field"])
#                     dd["previous"].append(str(item["previous"]))
#                     dd["updated"].append(str(item["updated"]))
#                     dd["player_id"].append(row["id"])
#                 df = pd.DataFrame(dd)
#                 df.to_sql(
#                     "player_record_changes",
#                     self.postgres_db,
#                     if_exists="append",
#                     index=False,
#                     schema=settings.PG_SCHEMA,
#                 )

#     def migrate_team_requests(self):
#         df = pd.DataFrame(list(self.mongo_db["team_requests"].find()))
#         df_copy = df.copy()
#         df = df.drop(
#             columns=[
#                 "name",
#                 "area_name",
#                 "division_level",
#                 "changelog",
#                 "source",
#                 "proposed_players",
#                 "segment",
#             ]
#         )
#         df["organization_id"] = self.enskai_uuid
#         df["created_by"] = df["created_by"].apply(lambda x: self.id_map[x])
#         df = df.rename(columns={"_id": "id"})
#         for i, row in df_copy.iterrows():
#             source_id = (
#                 row["source"]["id"] if isinstance(row["source"], dict) else None
#             )
#             df.at[i, "source_id"] = source_id
#         self.team_df_log = pd.concat([df["id"], df_copy["changelog"]], axis=1)
#         df["is_sensitive"] = False
#         df.to_sql(
#             "team_requests",
#             self.postgres_db,
#             if_exists="append",
#             index=False,
#             schema=settings.PG_SCHEMA,
#         )

#     def migrate_team_logs(self):
#         for i, row in self.team_df_log.iterrows():
#             if row["changelog"]:
#                 dd = {
#                     "id": [],
#                     "edit_at": [],
#                     "edit_by": [],
#                     "field": [],
#                     "previous": [],
#                     "updated": [],
#                     "team_request_id": [],
#                 }
#                 for item in row["changelog"]:
#                     dd["id"].append(uuid.uuid4())
#                     dd["edit_at"].append(item["edit_at"])
#                     dd["edit_by"].append(self.id_map[item["edit_by"]])
#                     dd["field"].append(item["field"])
#                     dd["previous"].append(str(item["previous"]))
#                     dd["updated"].append(str(item["updated"]))
#                     dd["team_request_id"].append(row["id"])
#                 df = pd.DataFrame(dd)
#                 df.to_sql(
#                     "team_request_changes",
#                     self.postgres_db,
#                     if_exists="append",
#                     index=False,
#                     schema=settings.PG_SCHEMA,
#                 )

#     def migrate_reports(self):
#         df = self.player_reports
#         for player_id, report in zip(df["id"], df["scouting_reports"]):
#             if report:
#                 report_df = pd.DataFrame(report)
#                 report_df = report_df.rename(columns={"_id": "id"})
#                 report_df["created_by"] = report_df["created_by"].apply(
#                     lambda x: self.id_map[x]
#                 )
#                 report_df["organization_id"] = self.enskai_uuid
#                 report_df = report_df.drop(
#                     columns=[
#                         "changelog",
#                         "playerId",
#                         "firstName",
#                         "lastName",
#                         "teamId",
#                         "team_name",
#                     ]
#                 )
#                 report_df["player_id"] = player_id
#                 report_df["is_sensitive"] = False
#                 report_df.to_sql(
#                     "reports",
#                     self.postgres_db,
#                     if_exists="append",
#                     index=False,
#                     schema=settings.PG_SCHEMA,
#                 )

#     def migrate_proposals(self):
#         df = self.deals_with_player
#         for player_id, proposal in zip(df["id"], df["deals_with_player"]):
#             dd = {
#                 "id": [],
#                 "player_id": [],
#                 "created_at": [],
#                 "created_by": [],
#                 "notes": [],
#                 "request_id": [],
#                 "last_updated": [],
#             }
#             if proposal:
#                 if len(proposal[0]) > 0:
#                     result = self.mongo_db["team_requests"].find(
#                         {"_id": proposal[0]}
#                     )
#                     if result:
#                         ll_result = list(result)
#                         if ll_result:
#                             dd["id"].append(uuid.uuid4())
#                             dd["request_id"].append(ll_result[0]["_id"])
#                             dd["player_id"].append(player_id)
#                             dd["created_at"].append(ll_result[0]["created_at"])
#                             dd["created_by"].append(
#                                 self.id_map[ll_result[0]["created_by"]]
#                             )
#                             dd["notes"].append(ll_result[0]["notes"])
#                             dd["last_updated"].append(
#                                 ll_result[0]["last_updated"]
#                             )
#                             dd["organization_id"] = self.enskai_uuid
#                             dd["is_sensitive"] = False
#                             pd.DataFrame(dd).to_sql(
#                                 "proposals",
#                                 self.postgres_db,
#                                 if_exists="append",
#                                 index=False,
#                                 schema=settings.PG_SCHEMA,
#                             )


# if __name__ == "__main__":
#     migrator = Migrator()
#     ###migrator.populate_with_record_for_enskai()
#     ###migrator.migrate_roles()
#     ###migrator.migrate_users()
#     # migrator.migrate_contacts()
#     # migrator.migrate_players()
#     # migrator.migrate_player_logs()
#     # migrator.migrate_team_requests()
#     # migrator.migrate_team_logs()
#     # migrator.migrate_reports()
#     # migrator.migrate_proposals()
#     df = migrator.print_from_mongo()
#     for i, row in df.iterrows():
        
#         if not row.source:
#             migrator.postgres_db.execute(f"""
#             update crm.team_requests
#             set source_id = NULL
#             where
#             id = '{row._id}'
#             """)
#         elif isinstance(row.source, list):
#             row.source = row.source[0]
#             row_id = row.source["id"]
#             print(row_id)
#             migrator.postgres_db.execute(
#                 f"""
#             update crm.team_requests
#             set source_id = '{row_id}'
#             where
#             id = '{row._id}'
#             """
#             )
