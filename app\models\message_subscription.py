from datetime import datetime
import uuid
from sqlalchemy import Column, Foreign<PERSON>ey, DateTime, Boolean, String
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.config import settings
from sqlalchemy.dialects.postgresql import UUID


class MessageSubscription(Base):
    __tablename__ = 'message_subscription'
    __table_args__ = {"schema": settings.PG_SCHEMA}
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.user.id"),
        index=True,
    )
    token = Column(String)
    created_at = Column(DateTime, index=True, default=datetime.now)
    approved = Column(Boolean, default=False)
    email = Column(String)
    user = relationship('User')