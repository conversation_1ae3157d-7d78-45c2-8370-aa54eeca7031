<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>New Player Created Manually</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .header {
            background-color: #f4f4f4;
            padding: 10px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
        }
        .content {
            margin-top: 20px;
        }
        .footer {
            margin-top: 20px;
            font-size: 14px;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">Manually create player</div>
        <div class="content">
            <p><strong>First Name:</strong> {{ player.first_name }}</p>
            <p><strong>Last Name:</strong> {{ player.last_name }}</p>
            {% if player.position %}
                <p><strong>Position(s):</strong> {{ ", ".join(player.position) }}</p>
            {% endif %}
            <p><strong>Date of Birth:</strong> {{ player.date_of_birth }}</p>
            <p><strong>Passport(s):</strong> {{ ", ".join(player.passport) }}</p>
            
            {% if player.current_club or player.club_name %}
                <p><strong>Current Club:</strong> {{ player.club_name if player.club_name else 'Unknown' }}</p>
            {% endif %}
            
            {% if player.tm_link %}
                <p><strong>Transfermarkt Link:</strong> <a href="{{ player.tm_link }}" target="_blank">View Profile</a></p>
            {% endif %}
        </div>
        <div class="footer">
            <p>Email sent by: {{ from }}</p>
        </div>
    </div>
</body>
</html>