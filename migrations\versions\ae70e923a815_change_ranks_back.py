"""change ranks back

Revision ID: ae70e923a815
Revises: d18cdebaf488
Create Date: 2023-04-24 16:23:03.068130

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ae70e923a815'
down_revision = 'd18cdebaf488'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reports', sa.Column('model_ranks', sa.String(), nullable=True), schema='crm')
    op.drop_column('reports', 'reported_rank', schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reports', sa.Column('reported_rank', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm')
    op.drop_column('reports', 'model_ranks', schema='crm')
    # ### end Alembic commands ###