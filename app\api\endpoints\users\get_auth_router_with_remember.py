from typing import Tuple

from fastapi import APIRouter, Depends, HTTPException, Response, status, Body
from fastapi.security import OAuth2PasswordRequestForm

from fastapi_users import models
from fastapi_users.authentication import (
    AuthenticationBackend,
    Authenticator,
    Strategy,
)
from fastapi_users.manager import BaseUserManager, UserManagerDependency
from fastapi_users.openapi import OpenAPIResponseType
from fastapi_users.router.common import ErrorCode, ErrorModel
import pyotp
from app.schemas.user import UserUpdate, UserRead
from app import crud
from firebase_admin import auth, initialize_app, delete_app, get_app, credentials
from app.config import settings
from app.db.session import session_maker
import requests
from app.api.utils import get_user_modules, decode_firebase_jwt, refresh_token, delete_refresh_token
import jwt
cred = credentials.Certificate(settings.FIREBASE_CREDS)
try:
    initialize_app(credential=cred)
except: 
    pass


def get_auth_router_with_remember(
    backend: AuthenticationBackend,
    get_user_manager: UserManagerDependency[models.UP, models.ID],
    authenticator: Authenticator,
    requires_verification: bool = False,
) -> APIRouter:
    """Generate a router with login/logout routes for an authentication backend.
    Custom made router by @ziruzavar which adds remember me functionality.
    """
    router = APIRouter()
    get_current_user_token = authenticator.current_user_token(
        active=True, verified=requires_verification
    )
    login_responses: OpenAPIResponseType = {
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorModel,
            "content": {
                "application/json": {
                    "examples": {
                        ErrorCode.LOGIN_BAD_CREDENTIALS: {
                            "summary": ("Bad credentials or the user is inactive."),
                            "value": {"detail": ErrorCode.LOGIN_BAD_CREDENTIALS},
                        },
                        ErrorCode.LOGIN_USER_NOT_VERIFIED: {
                            "summary": "The user is not verified.",
                            "value": {"detail": ErrorCode.LOGIN_USER_NOT_VERIFIED},
                        },
                    }
                }
            },
        },
        **backend.transport.get_openapi_login_responses_success(),
    }

    # @router.post('/otp/generate')
    # async def Generate_OTP(payload: UserRead):
    #     otp_base32 = pyotp.random_base32()
    #     otp_auth_url = pyotp.totp.TOTP(otp_base32).provisioning_uri(
    #         name=payload.email, issuer_name="Ensk.ai")

    #     # TODO update the user to save these

    #     return {'base32': otp_base32, "otp_url": otp_auth_url}

    @router.post(
        "/login",
        name=f"auth:{backend.name}.login",
        responses=login_responses,
    )
    async def login(
        response: Response,
        remember_me: bool = Body(default=True),
        credentials: OAuth2PasswordRequestForm = Depends(),
        user_manager: BaseUserManager[models.UP, models.ID] = Depends(get_user_manager),
        strategy: Strategy[models.UP, models.ID] = Depends(backend.get_strategy),
        grant_type: str = "",  # needed to add this for doc login to work, redundant
    ):

        user = await user_manager.authenticate(credentials)
        
        if user is None or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=ErrorCode.LOGIN_BAD_CREDENTIALS,
            )
        if requires_verification and not user.is_verified:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=ErrorCode.LOGIN_USER_NOT_VERIFIED,
            )

        user_mods = get_user_modules(user)
        
        user_info = auth.get_user(str(user.id))

        if user_info.uid != user.id:
            custom_token = auth.create_custom_token(user_info.uid)
            decoded_token = requests.post(settings.FIREBASE_API_URL, data={'token':custom_token, 'returnSecureToken': True, 'key':settings.FIREBASE_API_KEY})
            decoded_token = decoded_token.json()
        # TODO Read firebase refresh token, if doesn't exist create one
        refresh_token_str = crud.firebase.get(db=session_maker(), id=user.id)
        if not refresh_token_str:

            custom_token = auth.create_custom_token(user_info.uid)
            decoded_token = requests.post(settings.FIREBASE_API_URL, data={'token':custom_token, 'returnSecureToken': True, 'key':settings.FIREBASE_API_KEY})
            decoded_token = decoded_token.json()

            crud.firebase.create(db=session_maker(), obj_in={
                'id': user.id,
                'refresh_token':decoded_token['refreshToken']
            })
        else:
            # Post to get token to check for modules
            try:
                token = refresh_token(user.id)
            except jwt.ExpiredSignatureError:
                delete_refresh_token(user.id)
                custom_token = auth.create_custom_token(user_info.uid)
                decoded_token = requests.post(settings.FIREBASE_API_URL, data={'token':custom_token, 'returnSecureToken': True, 'key':settings.FIREBASE_API_KEY})
                decoded_token = decoded_token.json()

                crud.firebase.create(db=session_maker(), obj_in={
                    'id': user.id,
                    'refresh_token':decoded_token['refreshToken']
                })
            
        return await backend.login(strategy, user, response)

    logout_responses: OpenAPIResponseType = {
        **{
            status.HTTP_401_UNAUTHORIZED: {
                "description": "Missing token or inactive user."
            }
        },
        **backend.transport.get_openapi_logout_responses_success(),
    }

    @router.post(
        "/logout",
        name=f"auth:{backend.name}.logout",
        responses=logout_responses,
    )
    async def logout(
        response: Response,
        user_token: Tuple[models.UP, str] = Depends(get_current_user_token),
        strategy: Strategy[models.UP, models.ID] = Depends(backend.get_strategy),
    ):
        user, token = user_token
        return await backend.logout(strategy, user, token, response)

    return router
