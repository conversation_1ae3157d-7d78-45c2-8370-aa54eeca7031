from typing import Type
from fastapi_users import FastAPIUsers, schemas
from fastapi import APIRouter
from .backend_with_remember import AuthenticationBackendRemember
from .get_auth_router_with_remember import get_auth_router_with_remember
from .get_users_router import get_users_router
from .get_register_router import get_register_router
from fastapi_users import models

class FastAPIUsersUpdated(FastAPIUsers[models.UP, models.ID]):

    def get_auth_router(
        self, backend: AuthenticationBackendRemember, requires_verification: bool = False
    ) -> APIRouter:
        """
        Return an auth router for a given authentication backend.

        :param backend: The authentication backend instance.
        :param requires_verification: Whether the authentication
        require the user to be verified or not. Defaults to False.

        #ziruzavar - get_auth_router_with_remember is a custom made router
         which implements "remember me" functionality when loggin in
        """
        return get_auth_router_with_remember(
            backend,
            self.get_user_manager,
            self.authenticator,
            requires_verification,
        )
    
    def get_register_router(
        self, user_schema: Type[schemas.U], user_create_schema: Type[schemas.UC]
    ) -> APIRouter:
        """
        Return a router with a register route.

        :param user_schema: Pydantic schema of a public user.
        :param user_create_schema: Pydantic schema for creating a user.
        """
        return get_register_router(
            self.get_user_manager, user_schema, user_create_schema
        )

    def get_users_router(
            self, user_schema: Type[schemas.U], user_update_schema: Type[schemas.UC],
    requires_verification: bool = False,
    ) -> APIRouter:
        """
        Return a router with a users route.
        """
        return get_users_router(
            self.get_user_manager, user_schema, user_update_schema, self.authenticator, requires_verification
        )