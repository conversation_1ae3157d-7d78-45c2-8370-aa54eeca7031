from app.db import base
from app.db.session import session_maker
import datetime

def main(org_id: str, active_until):
    db = session_maker()
    org = db.query(base.Organization).filter(base.Organization.id == org_id).first()
    modules = db.query(base.Module).all()
    for m in modules:
        db.add(
            base.Purchase(
                organization_id=org.id,
                module_id=m.id,
                price=420,
                active_until=active_until,
                active=True
            )
        )
    db.commit()


if __name__ == '__main__':
    org_id = "ebfdb98e-9af4-4ba0-a437-4ce1997132e1"
    active_until = datetime.datetime.now() + datetime.timedelta(days=100*365)
    main(org_id, active_until)