"""drop url col

Revision ID: 661ee4d8f19c
Revises: 8da5ab37ff61
Create Date: 2022-09-21 14:56:15.889080

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '661ee4d8f19c'
down_revision = '8da5ab37ff61'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('contract_uploads', 'url', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contract_uploads', sa.Column('url', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_dev')
    # ### end Alembic commands ###