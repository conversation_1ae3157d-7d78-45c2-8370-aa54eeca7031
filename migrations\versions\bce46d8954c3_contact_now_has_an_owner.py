"""Contact now has an owner

Revision ID: bce46d8954c3
Revises: 811bcec92380
Create Date: 2024-08-14 13:22:12.483763

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'bce46d8954c3'
down_revision = '811bcec92380'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contacts', sa.Column('owner', sa.String(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('contacts', 'owner', schema='crm_test')
    # ### end Alembic commands ###