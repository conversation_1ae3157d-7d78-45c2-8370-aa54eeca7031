"""X_Y cordinates change to floats

Revision ID: 2177b8af65e0
Revises: 6a0f07b2856d
Create Date: 2025-05-09 10:11:40.355329

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2177b8af65e0'
down_revision = '6a0f07b2856d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('field_players', 'x_cordinate',
               existing_type=sa.INTEGER(),
               type_=sa.Float(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('field_players', 'y_cordinate',
               existing_type=sa.INTEGER(),
               type_=sa.Float(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('field_requests', 'x_cordinate',
               existing_type=sa.INTEGER(),
               type_=sa.Float(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('field_requests', 'y_cordinate',
               existing_type=sa.INTEGER(),
               type_=sa.Float(),
               existing_nullable=True,
               schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('field_requests', 'y_cordinate',
               existing_type=sa.Float(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('field_requests', 'x_cordinate',
               existing_type=sa.Float(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('field_players', 'y_cordinate',
               existing_type=sa.Float(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('field_players', 'x_cordinate',
               existing_type=sa.Float(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               schema='crm_test')
    # ### end Alembic commands ###