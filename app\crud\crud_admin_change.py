from typing import Any, Dict, List, Optional, Type, TypeVar, Union
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.admin_change import AdminChangeCreate, AdminChangeUpdate
from app.db.base_class import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDAdminChange(
    CRUDBase[models.AdminChange, AdminChangeCreate, AdminChangeUpdate]
):
    def create_with_user(
        self,
        db: Session,
        *,
        obj_in: AdminChangeCreate,
        user: models.User,
    ) -> models.AdminChange:
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(
            **obj_in_data,
            edit_by=user.id,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_target(
        self,
        db: Session,
        *,
        target_type: str,
        target_id: str,
    ) -> List[models.AdminChange]:
        return (
            db.query(self.model)
            .filter(
                self.model.target_type == target_type,
                self.model.target_id == target_id,
            )
            .order_by(self.model.edit_at.desc())
            .all()
        )

    def get_by_admin(
        self,
        db: Session,
        *,
        admin_id: str,
    ) -> List[models.AdminChange]:
        return (
            db.query(self.model)
            .filter(self.model.edit_by == admin_id)
            .order_by(self.model.edit_at.desc())
            .all()
        )

    def get_multi(
        self,
        db: Session,
    ) -> List[models.AdminChange]:
        return db.query(self.model).order_by(self.model.edit_at.desc()).all()


admin_change = CRUDAdminChange(models.AdminChange)
