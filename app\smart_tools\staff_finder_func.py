from typing import List
from app import models
import sqlalchemy
import pandas as pd
from app.db.session import engine
from datetime import datetime
from sqlalchemy.orm import Session

def fetch_staff_finder_data(db: Session, tm_player_id: str, current_user: models.User) -> List[dict]:
    staff_finder_data = []

    with engine.connect() as conn:
        # Start a transaction so the temp table sticks around
        with conn.begin():
            # Step 1: Create TEMP TABLE (filtered + optimized)
            conn.execute(sqlalchemy.text("""
                CREATE TEMP TABLE transfers_expanded_fast ON COMMIT DROP AS
                SELECT *,
                    LAG(left_id)   OVER (PARTITION BY tm_player_id ORDER BY date) AS prev_left_id,
                    LAG(date)      OVER (PARTITION BY tm_player_id ORDER BY date) AS joined_date,
                    LAG(joined_id) OVER (PARTITION BY tm_player_id ORDER BY date) AS prev_joined_id,
                    LEAD(date)     OVER (PARTITION BY tm_player_id ORDER BY date) AS date_left
                FROM transfermarkt.tm_transfers
                WHERE tm_player_id = :tm_player_id
            """), {"tm_player_id": tm_player_id})

            conn.execute(sqlalchemy.text("INSERT INTO public.staff_finder_logs values (:email, :org_id, :tm_id, :timestamp)")
                       , {"email": current_user.email, "org_id": current_user.organization_id, "tm_id": tm_player_id, "timestamp": datetime.now()})

            # Step 2: Run your big query (UPDATE your SQL to use `transfers_expanded_fast`)
            result_df = pd.read_sql(sqlalchemy.text("""
                WITH parent_teams_children AS (
                    SELECT DISTINCT ON (st2.team_id)
                        st1.team_name AS parent_team_name,
                        st1.league_tier AS parent_league_tier,
                        st2.*,
                        st1.team_id AS parent_team_id,
                        levenshtein(st2.team_name, st1.team_name) AS str_dist
                    FROM transfermarkt.tm_teams st1
                    FULL JOIN transfermarkt.tm_teams st2
                       ON st1.league_country = st2.league_country
                      AND st1.league_name != st2.league_name
                    WHERE st2.team_name LIKE '%%' || st1.team_name || '%%'
                      AND st1.league_tier <= st2.league_tier
                    ORDER BY st2.team_id, st1.league_tier
                ),
                parent_teams_main AS (
                    SELECT DISTINCT ON (st2.team_id)
                        st1.team_name AS parent_team_name,
                        st1.league_tier AS parent_league_tier,
                        st2.*,
                        st1.team_id AS parent_team_id,
                        levenshtein(st2.team_name, st1.team_name) AS str_dist
                    FROM transfermarkt.tm_teams st1
                    FULL JOIN transfermarkt.tm_teams st2
                       ON st1.league_country = st2.league_country
                      AND st1.league_name = st2.league_name
                    WHERE st2.team_name LIKE '%%' || st1.team_name || '%%'
                      AND st1.league_tier = st2.league_tier
                    ORDER BY st2.team_id, st1.league_tier
                ),
                parent_teams_all AS (
                    SELECT * FROM parent_teams_children
                    UNION
                    SELECT * FROM parent_teams_main
                ),
                parent_teams AS (
                    SELECT DISTINCT ON (team_id) *
                    FROM parent_teams_all
                    ORDER BY team_id, parent_league_tier
                ),
                staff_teams AS (
                    SELECT
                       tt.team_id,
                       tt.team_name,
                       tt.parent_team_name,
                       tt.league_name,
                       tt.league_country,
                       tt.league_tier,
                       tt.parent_team_id,
                       sd.*
                    FROM parent_teams tt
                    JOIN transfermarkt.staff_data sd
                       ON sd.current_team_id = tt.team_id
                ),
                all_common_tenure AS (
                    SELECT *, te.joined_date AS date_of_transfer
                    FROM staff_teams sd, transfers_expanded_fast te
                    WHERE (sd.current_team_id = te.left_id OR sd.parent_team_id = te.left_id)
                      AND (
                        (te.joined_date::text BETWEEN appointed AND in_charge_until)
                        OR (appointed BETWEEN te.joined_date::text AND te.date::text)
                        OR (in_charge_until BETWEEN te.joined_date::text AND te.date::text)
                      )
                    UNION
                    SELECT *, te.date AS date_of_transfer
                    FROM staff_teams sd,
                    (
                        SELECT DISTINCT ON (tm_player_id) *
                        FROM transfers_expanded_fast
                        ORDER BY tm_player_id, date DESC
                    ) te
                    WHERE (sd.current_team_id = te.joined_id OR sd.parent_team_id = te.joined_id)
                      AND te.date::text < in_charge_until
                )
                SELECT DISTINCT ON (ac.staff_id, tt.team_id)
                       ac.team_name AS prev_team,
                       ac.current_team_id as prev_team_id,
                       ac.league_country AS prev_team_country,
                       ac.role AS prev_role,
                       ac.name,
                       tt.team_name AS curr_team,
                       tt.team_id   AS curr_team_id,
                       tt.league_country AS curr_team_country,
                       sd.role AS new_role,
                       TO_CHAR(
                            GREATEST(ac.appointed::date, ac.joined_date)::date,
                            'MM/YYYY'
                        )
                        || ' - '
                        || TO_CHAR(
                            LEAST(
                            COALESCE(ac.in_charge_until::date, ac.date_left),
                            ac.date_left
                            )::date,
                            'MM/YYYY'
                        ) AS time_together
                FROM all_common_tenure ac
                JOIN transfermarkt.staff_data sd 
                     ON ac.staff_id = sd.staff_id
                    AND sd.in_charge_until IS NULL
                JOIN transfermarkt.tm_teams tt
                     ON tt.team_id = sd.current_team_id
                JOIN transfermarkt.transfermarkt_data tmd
                     ON tmd.tm_player_id = ac.tm_player_id
                WHERE tmd.tm_player_id = :tm_player_id
                  AND tt.team_id != tmd.current_club_id
                ORDER BY ac.staff_id, tt.team_id, date_of_transfer DESC
            """), conn, params={"tm_player_id": tm_player_id})
            
            staff_finder_data = result_df.to_dict(orient="records")

    return staff_finder_data