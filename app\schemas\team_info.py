from pydantic import BaseModel, Field
from typing import Optional

class TeamInfo(BaseModel):
    teamId: int
    name: str
    area_name: str
    divisionLevel: int = Field(None, alias="divisionLevel")
    league_name: Optional[str]
    segment: int
    imageDataURL: str

    class Config:
        orm_mode = True
        use_cache=True

class CommunityTeamInfo(BaseModel):
    teamId: int
    area_name: str
    divisionLevel: int = Field(None, alias="divisionLevel")
    league_name: Optional[str]
    segment: int

    class Config:
        orm_mode = True
        use_cache=True