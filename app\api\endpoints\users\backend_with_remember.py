from typing import Any
from fastapi import Response
from fastapi_users import models
from fastapi_users.authentication import (
    AuthenticationBackend,
    Strategy,
)

class AuthenticationBackendRemember(AuthenticationBackend):
    async def login(
    self,
    strategy: Strategy[models.UP, models.ID],
    user: models.UP,
    response: Response,
    ) -> Any:
        """
        Passing the "remember me" bool, which will extend
        the life of the jwt token if true
        """
        token = await strategy.write_token(user)
        resp = await self.transport.get_login_response(token)
        return resp
    