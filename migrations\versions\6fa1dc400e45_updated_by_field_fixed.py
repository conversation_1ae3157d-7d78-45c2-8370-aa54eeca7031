"""Updated by field fixed

Revision ID: 6fa1dc400e45
Revises: 57d0ccf06ab4
Create Date: 2024-06-03 15:14:34.681731

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6fa1dc400e45'
down_revision = '57d0ccf06ab4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('activity', sa.Column('updated_by_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_test')
    op.drop_index('ix_crm_test_activity_updated_to_id', table_name='activity', schema='crm_test')
    op.create_index(op.f('ix_crm_test_activity_updated_by_id'), 'activity', ['updated_by_id'], unique=False, schema='crm_test')
    op.drop_constraint('activity_updated_to_id_fkey', 'activity', schema='crm_test', type_='foreignkey')
    op.create_foreign_key(None, 'activity', 'contacts', ['updated_by_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    op.drop_column('activity', 'updated_to_id', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('activity', sa.Column('updated_to_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm_test')
    op.drop_constraint(None, 'activity', schema='crm_test', type_='foreignkey')
    op.create_foreign_key('activity_updated_to_id_fkey', 'activity', 'contacts', ['updated_to_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    op.drop_index(op.f('ix_crm_test_activity_updated_by_id'), table_name='activity', schema='crm_test')
    op.create_index('ix_crm_test_activity_updated_to_id', 'activity', ['updated_to_id'], unique=False, schema='crm_test')
    op.drop_column('activity', 'updated_by_id', schema='crm_test')
    # ### end Alembic commands ###