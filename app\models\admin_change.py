from sqlalchemy import Column, ForeignKey, String
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from app.db.base_class import Base
from app.models.change_base_mixin import ChangeBaseMixin
from app.config import settings


class AdminChange(Base, ChangeBaseMixin):
    __tablename__ = "admin_changes"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    # Additional fields specific to admin changes
    action_type = Column(
        String, nullable=False
    )  # e.g. "create_user", "delete_organization", etc.
    target_type = Column(String, nullable=False)  # e.g. "user", "organization", etc.
    target_id = Column(UUID(as_uuid=True), nullable=False)  # ID of the affected entity
    details = Column(String, nullable=True)  # Additional details about the change
