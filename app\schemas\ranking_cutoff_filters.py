from pydantic import BaseModel
from typing import Optional


class CutoffFilters(BaseModel):
    time_interval: int
    relevant_competitions: Optional[str]
    min_num_games: int
    min_tagged: int
    min_pct: int
    minute_scaling_log_base: Optional[int]
    competition_scaling: Optional[str]
    difficulty_scaling: Optional[str]
    quality_scaling: Optional[str]
    activity_vs_accuracy: Optional[str]
    time_decay_coef: Optional[float]

    class Config:
        orm_mode = True