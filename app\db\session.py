from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

from app.config import settings

async_engine = create_async_engine(settings.PG_URL_ASYNC, pool_use_lifo=True, pool_pre_ping=True)
engine = create_engine(settings.PG_URL,
    use_insertmanyvalues='insertmanyvalues',
    insertmanyvalues_page_size=1000,
    pool_use_lifo=True, pool_pre_ping=True)
async_session_maker = sessionmaker(async_engine, class_=AsyncSession, expire_on_commit=False)
session_maker = sessionmaker(autocommit=False, autoflush=False, bind=engine)