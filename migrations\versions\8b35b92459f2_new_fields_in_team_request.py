"""New fields in team_request

Revision ID: 8b35b92459f2
Revises: ef5124a47f5a
Create Date: 2024-11-19 13:05:53.321447

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8b35b92459f2'
down_revision = 'ef5124a47f5a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('team_requests', sa.Column('club_contact', sa.String(), nullable=True), schema='crm_test')
    op.add_column('team_requests', sa.Column('eu_passport', sa.<PERSON>(), nullable=True), schema='crm_test')
    op.drop_column('team_requests', 'stage', schema='crm_test')
    op.drop_column('team_requests', 'reason_for_outcome', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('team_requests', sa.Column('reason_for_outcome', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_test')
    op.add_column('team_requests', sa.Column('stage', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_test')
    op.drop_column('team_requests', 'eu_passport', schema='crm_test')
    op.drop_column('team_requests', 'club_contact', schema='crm_test')
    # ### end Alembic commands ###