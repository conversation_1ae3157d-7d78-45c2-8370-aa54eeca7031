import uuid
from typing import Optional

from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)
from app.schemas.activity import Activity
from app.schemas.player_record import PlayerRecord
from app.schemas.team_request import TeamRequestShortEmail
from app.schemas.community_deal import CommunityDeal
from app.schemas.user import UserShort
from app.schemas.enums import InAppNotificationType

class PlatformNotificationUpdate(ExtendedUpdateBase):
    active: Optional[str]
    type: Optional[InAppNotificationType]
    activity_id: Optional[uuid.UUID]
    team_request_id: Optional[uuid.UUID]
    player_record_id: Optional[uuid.UUID]
    community_deal_id: Optional[uuid.UUID]
    created_for: Optional[uuid.UUID]

class PlatformNotificationCreate(PlatformNotificationUpdate,ExtendedCreateBase):
    active: bool
    type: InAppNotificationType
    created_for: uuid.UUID

class PlatformNotification(PlatformNotificationCreate, ExtendedBase):
    created_for_user: UserShort
    activity: Optional[Activity]
    team_request: Optional[TeamRequestShortEmail]
    player_record: Optional[PlayerRecord]
    community_deal: Optional[CommunityDeal]