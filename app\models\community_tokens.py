from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer

from app.db.base_class import Base
from app.config import settings
from sqlalchemy.dialects.postgresql import UUID

class CommunityTokens(Base):
    __tablename__ = 'community_tokens'
    __table_args__ = {"schema": settings.PG_SCHEMA}

    organization_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.organizations.id"),
        index=True,
        primary_key = True
    )

    tokens = Column(Integer)
    