from typing import Any, List

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.schemas.report import Report, ReportCreate, ReportUpdate
from app import crud, models
from app.api import deps, utils
from app.utils import caching

router = APIRouter()


@router.get("/", response_model=List[Report])
def read_reports(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve reports.
    """
    utils.check_get_all(Report, current_user)
    return crud.report.get_all_w_org(
        db, current_user.organization_id, utils.can_access_sensitive(current_user)
    )


@router.post("/", response_model=Report)
async def create_report(
    *,
    db: Session = Depends(deps.get_db),
    report_in: ReportCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
    cache=Depends(caching.get_cache),
) -> Any:
    """
    Create new report.
    """
    utils.check_create(Report, current_user)

    report = crud.report.create_with_user(
        db=db,
        obj_in=report_in,
        user=current_user,
    )

    #await caching.reset_cache_for_everyone(
    #    cache, "player_records", current_user.organization_id
    #)
    return report


@router.put("/{id}", response_model=Report)
async def update_report(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    report_in: ReportUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
    cache=Depends(caching.get_cache),
) -> Any:
    """
    Update an report.
    """
    report = crud.report.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_modify(report, current_user)

    report = crud.report.update(db=db, db_obj=report, obj_in=report_in)
    #await caching.reset_cache_for_everyone(
    #    cache, "player_records", current_user.organization_id
    #)
    return report


@router.get("/{id}", response_model=Report)
def read_report(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get report by ID.
    """
    report = crud.report.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_get_one(report, current_user)

    return report


@router.delete("/{id}", response_model=Report)
async def delete_report(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
    cache=Depends(caching.get_cache),
) -> Any:
    """
    Delete a report.
    """
    report = crud.report.get(db=db, id=id)
    utils.check_delete(report, current_user)

    report_out = Report.from_orm(report)
    crud.report.remove(db=db, id=id)
    #await caching.reset_cache_for_everyone(
    #    cache, "player_records", current_user.organization_id
    #)
    return report_out