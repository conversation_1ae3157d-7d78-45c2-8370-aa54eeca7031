from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel

from app.schemas.enums import Foot
from datetime import date


class StaffInfo(BaseModel):
    staff_id: int
    name: str
    role: str
    passport: Optional[str] = None
    birth_date: Optional[str] = None
    preferred_formation: Optional[str] = None
    coaching_licence: Optional[str] = None
    team_name: Optional[str] = None
    league_country: Optional[str] = None
    league_name: Optional[str] = None
    appointed: Optional[str] = None
    in_charge_until: Optional[str] = None
    agent: Optional[str] = None
    matches: Optional[str] = None
    points_per_match: Optional[str] = None
    avg_term_as_coach: Optional[str] = None

    class Config:
        orm_mode = True
        use_cache=True