import uuid
from datetime import datetime, timedelta
from typing import Dict, Optional
from app.schemas.contract_extraction import ContractExtractionResult


class ExtractionStatusManager:
    """
    Simple in-memory status manager for contract extractions.
    Auto-cleans completed extractions after 1 hour.
    """
    
    def __init__(self):
        self._status_store: Dict[str, ContractExtractionResult] = {}
    
    def create_extraction(self, request_data: dict) -> str:
        """Create a new extraction job and return extraction_id"""
        extraction_id = str(uuid.uuid4())
        
        result = ContractExtractionResult(
            extraction_id=extraction_id,
            status="processing",
            created_at=datetime.utcnow(),
            progress=0
        )
        
        self._status_store[extraction_id] = result
        return extraction_id
    
    def update_status(
        self, 
        extraction_id: str, 
        status: str = None,
        progress: int = None,
        contract_data: dict = None,
        confidence_score: float = None,
        partial_results: dict = None,
        missing_fields: list = None,
        error_message: str = None
    ) -> bool:
        """Update extraction status"""
        if extraction_id not in self._status_store:
            return False
        
        result = self._status_store[extraction_id]
        
        if status:
            result.status = status
        if progress is not None:
            result.progress = progress
        if contract_data:
            result.contract_data = contract_data
        if confidence_score is not None:
            result.confidence_score = confidence_score
        if partial_results:
            result.partial_results = partial_results
        if missing_fields:
            result.missing_fields = missing_fields
        if error_message:
            result.error_message = error_message
        
        # Mark as completed if status is final
        if status in ["completed", "failed", "partial"] and not result.completed_at:
            result.completed_at = datetime.utcnow()
        
        return True
    
    def get_status(self, extraction_id: str) -> Optional[ContractExtractionResult]:
        """Get extraction status by ID"""
        self._cleanup_old_extractions()
        return self._status_store.get(extraction_id)
    
    def get_all_active(self) -> Dict[str, ContractExtractionResult]:
        """Get all active extractions (for debugging)"""
        self._cleanup_old_extractions()
        return {k: v for k, v in self._status_store.items() if v.status == "processing"}
    
    def _cleanup_old_extractions(self):
        """Remove completed extractions older than 1 hour"""
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        
        to_remove = []
        for extraction_id, result in self._status_store.items():
            if (result.completed_at and 
                result.completed_at < cutoff_time and 
                result.status in ["completed", "failed", "partial"]):
                to_remove.append(extraction_id)
        
        for extraction_id in to_remove:
            del self._status_store[extraction_id]
    
    def force_cleanup(self, extraction_id: str) -> bool:
        """Manually remove an extraction (for cleanup)"""
        if extraction_id in self._status_store:
            del self._status_store[extraction_id]
            return True
        return False


# Global instance
extraction_status_manager = ExtractionStatusManager()
