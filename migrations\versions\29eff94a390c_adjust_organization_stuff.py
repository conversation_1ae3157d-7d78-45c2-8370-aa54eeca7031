"""adjust organization stuff

Revision ID: 29eff94a390c
Revises: 65be954cdb2c
Create Date: 2022-08-03 14:11:20.943001

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '29eff94a390c'
down_revision = '65be954cdb2c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contacts', sa.Column('contact_organization', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('contacts', sa.Column('organization_id', postgresql.UUID(), nullable=True), schema='crm_dev')
    op.create_foreign_key(None, 'contacts', 'organizations', ['organization_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.drop_column('contacts', 'organization', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contacts', sa.Column('organization', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_dev')
    op.drop_constraint(None, 'contacts', schema='crm_dev', type_='foreignkey')
    op.drop_column('contacts', 'organization_id', schema='crm_dev')
    op.drop_column('contacts', 'contact_organization', schema='crm_dev')
    # ### end Alembic commands ###