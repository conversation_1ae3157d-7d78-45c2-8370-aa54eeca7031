"""Added an active column to tracked_transfers

Revision ID: 0c7b31c151e3
Revises: 1405358fd81f
Create Date: 2024-03-22 14:07:57.633159

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0c7b31c151e3'
down_revision = '1405358fd81f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tracked_transfers', sa.Column('active', sa.Bo<PERSON>an(), nullable=True), schema='crm_test')
    op.create_unique_constraint('uix_request_id_player_name_date', 'tracked_transfers', ['request_id', 'player_name', 'date'], schema='crm_test')
    op.drop_constraint('tracked_transfers_request_id_fkey', 'tracked_transfers', schema='crm_test', type_='foreignkey')
    op.create_foreign_key(None, 'tracked_transfers', 'team_requests', ['request_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'tracked_transfers', schema='crm_test', type_='foreignkey')
    op.create_foreign_key('tracked_transfers_request_id_fkey', 'tracked_transfers', 'team_requests', ['request_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    op.drop_constraint('uix_request_id_player_name_date', 'tracked_transfers', schema='crm_test', type_='unique')
    op.drop_column('tracked_transfers', 'active', schema='crm_test')
    # ### end Alembic commands ###