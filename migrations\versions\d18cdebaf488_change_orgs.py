"""change orgs

Revision ID: d18cdebaf488
Revises: 024f73d7a3f4
Create Date: 2023-04-24 16:21:43.720580

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd18cdebaf488'
down_revision = '024f73d7a3f4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('password', sa.String(), nullable=True), schema='crm')
    op.drop_column('organizations', 'passwd', schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('passwd', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm')
    op.drop_column('organizations', 'password', schema='crm')
    # ### end Alembic commands ###