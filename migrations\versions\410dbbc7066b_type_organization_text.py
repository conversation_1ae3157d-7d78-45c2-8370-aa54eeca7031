"""type organization text

Revision ID: 410dbbc7066b
Revises: 56da7566c072
Create Date: 2025-03-26 09:49:19.739857

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '410dbbc7066b'
down_revision = '56da7566c072'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('type', sa.String(), nullable=True), schema='crm_test')
    op.drop_column('organizations', 'paid', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('paid', sa.BOOLEAN(), autoincrement=False, nullable=True), schema='crm_test')
    op.drop_column('organizations', 'type', schema='crm_test')
    # ### end Alembic commands ###