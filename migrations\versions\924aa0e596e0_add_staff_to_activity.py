"""Add staff to activity

Revision ID: 924aa0e596e0
Revises: 2eccd975e145
Create Date: 2025-01-30 14:37:02.345030

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '924aa0e596e0'
down_revision = '2eccd975e145'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('activity', sa.Column('staff_id', sa.Integer(), nullable=True), schema='crm_test')
    op.create_index(op.f('ix_crm_test_activity_staff_id'), 'activity', ['staff_id'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'staff_records', schema='crm_test', type_='foreignkey')
    op.drop_constraint(None, 'activity', schema='crm_test', type_='foreignkey')
    op.drop_index(op.f('ix_crm_test_activity_staff_id'), table_name='activity', schema='crm_test')
    op.drop_column('activity', 'staff_id', schema='crm_test')
    # ### end Alembic commands ###