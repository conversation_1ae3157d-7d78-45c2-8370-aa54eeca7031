"""update rank outputs table

Revision ID: dc0c12b18414
Revises: 75d971b74c6f
Create Date: 2022-11-14 11:37:24.284190

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'dc0c12b18414'
down_revision = '75d971b74c6f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rank_outputs', sa.Column('feature_json', sa.String(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rank_outputs', 'feature_json', schema='crm_dev')
    # ### end Alembic commands ###