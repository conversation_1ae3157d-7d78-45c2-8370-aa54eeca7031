"""padd org to players and requests

Revision ID: 8b2351dd34fd
Revises: 93dbb9f08652
Create Date: 2022-08-03 11:19:45.598871

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8b2351dd34fd'
down_revision = '93dbb9f08652'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_crm_dev_organizations_created_at', table_name='organizations', schema='crm_dev')
    op.drop_index('ix_crm_dev_organizations_created_by', table_name='organizations', schema='crm_dev')
    op.drop_index('ix_crm_dev_organizations_last_updated', table_name='organizations', schema='crm_dev')
    op.drop_column('organizations', 'notes', schema='crm_dev')
    op.drop_column('organizations', 'last_updated', schema='crm_dev')
    op.drop_column('organizations', 'created_at', schema='crm_dev')
    op.drop_column('organizations', 'created_by', schema='crm_dev')
    op.add_column('player_records', sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_player_records_organization_id'), 'player_records', ['organization_id'], unique=False, schema='crm_dev')
    op.create_foreign_key(None, 'player_records', 'organizations', ['organization_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.add_column('team_requests', sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_team_requests_organization_id'), 'team_requests', ['organization_id'], unique=False, schema='crm_dev')
    op.create_foreign_key(None, 'team_requests', 'organizations', ['organization_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'team_requests', schema='crm_dev', type_='foreignkey')
    op.drop_index(op.f('ix_crm_dev_team_requests_organization_id'), table_name='team_requests', schema='crm_dev')
    op.drop_column('team_requests', 'organization_id', schema='crm_dev')
    op.drop_constraint(None, 'player_records', schema='crm_dev', type_='foreignkey')
    op.drop_index(op.f('ix_crm_dev_player_records_organization_id'), table_name='player_records', schema='crm_dev')
    op.drop_column('player_records', 'organization_id', schema='crm_dev')
    op.add_column('organizations', sa.Column('created_by', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_dev')
    op.add_column('organizations', sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True), schema='crm_dev')
    op.add_column('organizations', sa.Column('last_updated', postgresql.TIMESTAMP(), autoincrement=False, nullable=True), schema='crm_dev')
    op.add_column('organizations', sa.Column('notes', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm_dev')
    op.create_index('ix_crm_dev_organizations_last_updated', 'organizations', ['last_updated'], unique=False, schema='crm_dev')
    op.create_index('ix_crm_dev_organizations_created_by', 'organizations', ['created_by'], unique=False, schema='crm_dev')
    op.create_index('ix_crm_dev_organizations_created_at', 'organizations', ['created_at'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###