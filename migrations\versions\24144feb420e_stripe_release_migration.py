"""Stripe release migration

Revision ID: 24144feb420e
Revises: c8217411115d
Create Date: 2024-03-11 18:32:53.783644

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '24144feb420e'
down_revision = 'c8217411115d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('message_subscription',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('token', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('approved', sa.<PERSON>an(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['crm_dev.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_message_subscription_created_at'), 'message_subscription', ['created_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_message_subscription_user_id'), 'message_subscription', ['user_id'], unique=False, schema='crm_dev')
    op.create_table('refresh_token',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('refresh_token', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['id'], ['crm_dev.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_refresh_token_id'), 'refresh_token', ['id'], unique=False, schema='crm_dev')
    op.add_column('organizations', sa.Column('billing_email', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('user', sa.Column('accept_terms', sa.Boolean(), nullable=True), schema='crm_dev')
    op.add_column('user', sa.Column('accept_marketing_emails', sa.Boolean(), nullable=True), schema='crm_dev')
    op.add_column('user', sa.Column('date_created', sa.DateTime(), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'date_created', schema='crm_dev')
    op.drop_column('user', 'accept_marketing_emails', schema='crm_dev')
    op.drop_column('user', 'accept_terms', schema='crm_dev')
    op.drop_column('organizations', 'billing_email', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_refresh_token_id'), table_name='refresh_token', schema='crm_dev')
    op.drop_table('refresh_token', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_message_subscription_user_id'), table_name='message_subscription', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_message_subscription_created_at'), table_name='message_subscription', schema='crm_dev')
    op.drop_table('message_subscription', schema='crm_dev')
    # ### end Alembic commands ###