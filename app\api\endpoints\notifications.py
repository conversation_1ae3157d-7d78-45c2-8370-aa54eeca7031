from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.schemas.notifications import Notifications, NotificationsCreate, NotificationsUpdate
from app import crud, models
from app.api import deps, utils
from app.utils import caching
from app.utils.notifications.notifications import check_birthdate, check_contract_expiry
from app.config import settings

router = APIRouter()


@router.get("/all", response_model=List[Notifications])
def read_notificationss(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve notificationss.
    """
    utils.check_get_all(Notifications, current_user)
    return crud.notifications.get_all(
        db
    )


@router.post("/", response_model=NotificationsCreate)
async def create_notifications(
    *,
    db: Session = Depends(deps.get_db),
    obj_in: NotificationsCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
    cache=Depends(caching.get_cache),
) -> Any:
    """
    Create new notifications.
    """
    utils.check_create(Notifications, current_user)

    # print(obj_in)

    notifications = crud.notifications.create(
        db=db,
        obj_in=obj_in,
    )

    #await caching.reset_cache_for_everyone(
    #    cache, "player_records", current_user.organization_id
    #)
    return notifications


@router.put("/", response_model=NotificationsUpdate)
async def update_notifications(
    *,
    db: Session = Depends(deps.get_db),
    obj_in: NotificationsUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update an notifications.
    """
    notifications = crud.notifications.get(db=db, player_id=obj_in.player_id)
    if notifications:
        utils.check_modify(notifications, current_user)

        notifications = crud.notifications.update(db=db, db_obj=notifications, obj_in=obj_in)
        return notifications
    else:
        notifications = crud.notifications.create(
            db=db,
            obj_in=obj_in,
        )



@router.get("/", response_model=NotificationsUpdate)
def read_notifications(
    *,
    db: Session = Depends(deps.get_db),
    player_id: str,
    # current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get notifications by Player ID and User ID.
    """

    notifications = crud.notifications.get(db=db, player_id=player_id)
    # utils.check_get_one(notifications, current_user)

    return notifications


@router.delete("/{id}", response_model=NotificationsUpdate)
async def delete_notifications(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
    cache=Depends(caching.get_cache),
) -> Any:
    """
    Delete a notifications.
    """
    notifications = crud.notifications.get(db=db, id=id)
    utils.check_delete(notifications, current_user)

    notifications_out = Notifications.from_orm(notifications)
    crud.notifications.remove(db=db, id=id)
    #await caching.reset_cache_for_everyone(
    #    cache, "player_records", current_user.organization_id
    #)
    return notifications_out

@router.get("/check_birthday_notifications/")
async def check_birthday_notifications(*,
    db: Session = Depends(deps.get_db),
    threshold: int = 14):

    birthdate_notifications = check_birthdate(db, schema=settings.PG_SCHEMA, date_threshold=threshold)

    return birthdate_notifications.all()