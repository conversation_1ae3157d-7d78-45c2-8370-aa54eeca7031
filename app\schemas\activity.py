from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)
from typing import Optional, List
import uuid
from app.schemas.enums import (
    ActivityStage,
    ActivityType
)
from pydantic import BaseModel
from datetime import datetime, date
from app.schemas.player_info import <PERSON>In<PERSON>, PlayerInfoShort
from app.schemas.team_info import TeamInfo
from app.schemas.contact import ContactShort
from app.schemas.change import Change
from app.schemas.comment import Comment
from app.schemas.assigned_to_record import AssignedToRecord
from app.schemas.staff_info import StaffInfo

class ActivityUpdate(ExtendedUpdateBase):
    assigned_to_record: Optional[List[uuid.UUID]]
    updated_by_id: Optional[uuid.UUID]
    teamId: Optional[int]
    staff: Optional[str]
    stage: Optional[ActivityStage]
    next_action: Optional[date]
    due_date: Optional[date]
    title: Optional[str]
    description: Optional[str]
    date_deal_created: Optional[datetime]

class ActivityCreate(ActivityUpdate, ExtendedCreateBase):
    type: ActivityType
    title: str
    playerId: Optional[int]
    staff_id: Optional[int]

class QuickCreateActivityPlayer(BaseModel):
    teamId: int
    team_name: str
    playerId: int
    player_name: str
    stage: Optional[ActivityStage] = 'offered'
    type: ActivityType = 'deal'

class QuickCreateOpActivity(BaseModel):
    teamId: int
    team_name: str
    playerId: int
    player_name: str
    description: Optional[str]

class QuickCreateStaffActivity(BaseModel):
    teamId: int
    team_name: str
    staff_id: int
    staff_name: str
    stage: Optional[ActivityStage] = 'offered'
    type: ActivityType = 'deal'


class Activity(ActivityCreate, ExtendedBase):
    player_info: Optional[PlayerInfoShort]
    team_info: Optional[TeamInfo]
    staff_info: Optional[StaffInfo]
    #changelog: List[Change]
    #updated_by: Optional[ContactShort]
    assigned_to_record: Optional[List[AssignedToRecord]]
    comments: List[Comment]

class ActivityDeal(ExtendedBase):
    type: ActivityType
    player_info: PlayerInfo
    team_info: TeamInfo
    staff: Optional[str]
    changelog: List[Change]
    stage: ActivityStage
    next_action: Optional[date]
    updated_by: Optional[ContactShort]
    assigned_to_record: Optional[List[AssignedToRecord]]

class ActivityTask(ExtendedBase):
    type: ActivityType
    player_info: Optional[PlayerInfo]
    team_info: Optional[TeamInfo]
    due_date: Optional[date]
    stage: ActivityStage
    changelog: List[Change]
    updated_by: Optional[ContactShort]
    assigned_to_record: Optional[List[AssignedToRecord]]

    class Config:
        use_cache=True
        orm_mode = True
