"""add contact channel col to player records

Revision ID: 1b793c89d150
Revises: 879a888a7803
Create Date: 2022-10-03 12:35:13.504699

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1b793c89d150'
down_revision = '879a888a7803'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('contact_channel', sa.ARRAY(sa.String()), nullable=True), schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('player_records', 'contact_channel', schema='crm_dev')
    # ### end Alembic commands ###