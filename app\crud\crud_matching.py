from typing import List, Optional
from app.crud.crud_base import CRUDBase, ModelType
from app import models
from sqlalchemy.orm import Session, lazyload, Load
from app.schemas.enums import Position, TransferPeriod
from app.utils.matching_helpers import team_to_player_segment_map

class MatchingPlayerRecord(CRUDBase):

    def get_specific(
    self,
    db: Session,
    segment: int,
    position: Position,
    period: List[TransferPeriod],
    organization_id: str) -> Optional[ModelType]:
        segment_band = team_to_player_segment_map(segment)
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*")).filter(
                self.model.position.contains([position]),self.model.transfer_period.contains(period),
                self.model.control_stage.in_(['signed', 'mandate', 'mandate_on_demand']),
                self.model.quality <= segment_band.upper_bound,
                 self.model.quality >= segment_band.lower_bound,
                 self.model.organization_id == organization_id).all()


player_info = MatchingPlayerRecord(models.PlayerRecord)