from typing import Generator

from fastapi import Depends, HTTPException


from app import models

from app.db.session import session_maker, async_session_maker
from app.api.utils import get_user_modules

from app.api.endpoints.users.auth import current_active_user as get_current_active_user


def get_db() -> Generator:
    try:
        db = session_maker()
        yield db
    finally:
        db.close()

async def get_async_db() -> Generator:
    try:
        db = async_session_maker()
        yield db
    finally:
        await db.close()


def generate_route_auth_check_func(module: str):
    def func(user: models.User = Depends(get_current_active_user)):
        if module not in get_user_modules(user):
            raise HTTPException(status_code=403, detail="Not authorized")

    return func
