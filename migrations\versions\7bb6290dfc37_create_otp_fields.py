"""Create OTP fields

Revision ID: 7bb6290dfc37
Revises: fbca458bbdfa
Create Date: 2023-11-15 15:04:49.152109

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7bb6290dfc37'
down_revision = 'fbca458bbdfa'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user', 'otp_enabled',
               existing_type=sa.VARCHAR(),
               type_=sa.Boolean(),
               existing_nullable=True,
               schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user', 'otp_enabled',
               existing_type=sa.Boolean(),
               type_=sa.VARCHAR(),
               existing_nullable=True,
               schema='crm_test')
    # ### end Alembic commands ###