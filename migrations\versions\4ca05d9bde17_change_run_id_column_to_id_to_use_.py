"""Change run_id column to id to use developed crud funcs in rank_outputs

Revision ID: 4ca05d9bde17
Revises: 674fea64d864
Create Date: 2022-08-25 05:01:28.411974

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4ca05d9bde17'
down_revision = '674fea64d864'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rank_outputs', sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False), schema='crm_dev')
    op.drop_constraint('rank_outputs_rank_record_id_fkey', 'rank_outputs', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key(None, 'rank_outputs', 'rank_records', ['id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.drop_column('rank_outputs', 'rank_record_id', schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rank_outputs', sa.Column('rank_record_id', postgresql.UUID(), autoincrement=False, nullable=False), schema='crm_dev')
    op.drop_constraint(None, 'rank_outputs', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key('rank_outputs_rank_record_id_fkey', 'rank_outputs', 'rank_records', ['rank_record_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    op.drop_column('rank_outputs', 'id', schema='crm_dev')
    # ### end Alembic commands ###