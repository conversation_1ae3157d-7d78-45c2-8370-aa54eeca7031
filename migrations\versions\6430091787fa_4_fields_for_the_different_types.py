"""4 fields for the different types

Revision ID: 6430091787fa
Revises: 59d044eeeccd
Create Date: 2024-09-04 16:51:43.465127

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6430091787fa'
down_revision = '59d044eeeccd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('platform_notifications', sa.Column('activity_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_test')
    op.add_column('platform_notifications', sa.Column('team_request_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_test')
    op.add_column('platform_notifications', sa.Column('player_record_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_test')
    op.add_column('platform_notifications', sa.Column('community_proposal_id', postgresql.UUID(as_uuid=True), nullable=True), schema='crm_test')
    op.create_foreign_key(None, 'platform_notifications', 'community_proposals', ['community_proposal_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    op.create_foreign_key(None, 'platform_notifications', 'team_requests', ['team_request_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    op.create_foreign_key(None, 'platform_notifications', 'player_records', ['player_record_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    op.create_foreign_key(None, 'platform_notifications', 'activity', ['activity_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    op.drop_column('platform_notifications', 'foreign_id', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('platform_notifications', sa.Column('foreign_id', postgresql.UUID(), autoincrement=False, nullable=True), schema='crm_test')
    op.drop_constraint(None, 'platform_notifications', schema='crm_test', type_='foreignkey')
    op.drop_constraint(None, 'platform_notifications', schema='crm_test', type_='foreignkey')
    op.drop_constraint(None, 'platform_notifications', schema='crm_test', type_='foreignkey')
    op.drop_constraint(None, 'platform_notifications', schema='crm_test', type_='foreignkey')
    op.drop_column('platform_notifications', 'community_proposal_id', schema='crm_test')
    op.drop_column('platform_notifications', 'player_record_id', schema='crm_test')
    op.drop_column('platform_notifications', 'team_request_id', schema='crm_test')
    op.drop_column('platform_notifications', 'activity_id', schema='crm_test')
    # ### end Alembic commands ###