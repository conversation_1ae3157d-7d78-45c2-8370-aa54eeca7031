from typing import Any

from fastapi import APIRouter, Depends, Body, Request
import requests
from fastapi.responses import JSONResponse
from app import  models
from app.api import deps
try:
    from ...config import settings
except:
    from app.config import settings

router = APIRouter(
    prefix="/agents",
    tags=["agents"],
    dependencies=[Depends(deps.generate_route_auth_check_func("agent_map"))]
)

@router.get("/all")
async def get_all_agents(
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all agents.
    """
    resp = requests.get(
        f"{settings.AGENT_API_URL}/all",
        auth=(settings.AGENT_API_USR, settings.AGENT_API_PASS),
    )

    return JSONResponse(status_code=resp.status_code, content=resp.json())

@router.post("/")
async def query_agent_data(
    agent=Body(...),
    current_user: models.User = Depends(deps.get_current_active_user)
    ):
    """
        Query agent data.
    """
    resp = requests.post(
        f"{settings.AGENT_API_URL}/",
        auth=(settings.AGENT_API_USR, settings.AGENT_API_PASS),
        json=agent,
    )
    return JSONResponse(status_code=resp.status_code, content=resp.json())

    
@router.post("/specific")
async def query_specific_agents(
    agents=Body(...),
    current_user: models.User = Depends(deps.get_current_active_user)
    ):
    """
        Query specific agent/s.
    """
    resp = requests.post(
        f"{settings.AGENT_API_URL}/specific",
        auth=(settings.AGENT_API_USR, settings.AGENT_API_PASS),
        json=agents,
    )
    return JSONResponse(status_code=resp.status_code, content=resp.json())

@router.get("/player/{playerId}")
async def read_player(
    playerId: int,
    current_user: models.User = Depends(deps.get_current_active_user)
    ):
    """
        Retrieve player data.
    """
    resp = requests.get(
        f"{settings.AGENT_API_URL}/player/{playerId}",
        auth=(settings.AGENT_API_USR, settings.AGENT_API_PASS),
    )
    return JSONResponse(status_code=resp.status_code, content=resp.json())