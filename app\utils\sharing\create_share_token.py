from datetime import datetime
from sqlalchemy.orm import Session
import uuid
from typing import Optional

def create_share_token(
    db: Session,
    resource_type: str,
    resource_id: uuid.UUID,
    expires_at: datetime,
    hide_field_view: Optional[bool] = False,
) -> str:
    from app.models import ShareToken  # wherever you defined it
    
    share_token = ShareToken(
        resource_type=resource_type,
        resource_id=resource_id,
        expires_at=expires_at,
        is_disabled=False,
        hide_field_view=hide_field_view,
    )
    db.add(share_token)
    db.commit()
    db.refresh(share_token)
    
    return share_token.token
