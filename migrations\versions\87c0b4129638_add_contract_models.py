"""add contract models

Revision ID: 87c0b4129638
Revises: 69f0c90faf18
Create Date: 2022-09-20 16:11:20.614837

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy
# revision identifiers, used by Alembic.
revision = '87c0b4129638'
down_revision = '69f0c90faf18'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('contracts',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('player_id', postgresql.UUID(), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=True),
    sa.Column('end_date', sa.Date(), nullable=True),
    sa.Column('status', sa.<PERSON>(), nullable=True),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.Column('type', sa.String(length=50), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('coverage', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('agent_id', postgresql.UUID(), nullable=True),
    sa.Column('agent_alt_name', sa.String(), nullable=True),
    sa.Column('exclusive', sa.Boolean(), nullable=True),
    sa.Column('termination_fee', sa.Float(), nullable=True),
    sa.Column('pct_commission_agreed', sa.Float(), nullable=True),
    sa.Column('registered_in', sa.String(length=50), nullable=True),
    sa.Column('gross_salary', sa.JSON(), nullable=True),
    sa.Column('signing_fee', sa.Float(), nullable=True),
    sa.Column('goal_bonus', sa.Float(), nullable=True),
    sa.Column('assist_bonus', sa.Float(), nullable=True),
    sa.Column('matches_played_bonus', sa.Float(), nullable=True),
    sa.Column('minimum_fee_release_clause', sa.Float(), nullable=True),
    sa.Column('option_years', sa.Integer(), nullable=True),
    sa.Column('teamId', sa.Integer(), nullable=True),
    sa.Column('installments', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['crm_dev.contacts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['crm_dev.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_dev.organizations.id'], ),
    sa.ForeignKeyConstraint(['player_id'], ['crm_dev.player_records.id'], ),
    # sa.ForeignKeyConstraint(['teamId'], ['wyscout.team_info.teamId'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_contracts_agent_id'), 'contracts', ['agent_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contracts_created_at'), 'contracts', ['created_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contracts_created_by'), 'contracts', ['created_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contracts_end_date'), 'contracts', ['end_date'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contracts_is_sensitive'), 'contracts', ['is_sensitive'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contracts_last_updated'), 'contracts', ['last_updated'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contracts_organization_id'), 'contracts', ['organization_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contracts_player_id'), 'contracts', ['player_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contracts_start_date'), 'contracts', ['start_date'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contracts_status'), 'contracts', ['status'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_contracts_teamId'), 'contracts', ['teamId'], unique=False, schema='crm_dev')
    op.create_table('contract_uploads',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('url', sa.String(), nullable=True),
    sa.Column('contract_id', postgresql.UUID(), nullable=True),
    sa.ForeignKeyConstraint(['contract_id'], ['crm_dev.contracts.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_contract_uploads_contract_id'), 'contract_uploads', ['contract_id'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_dev_contract_uploads_contract_id'), table_name='contract_uploads', schema='crm_dev')
    op.drop_table('contract_uploads', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contracts_teamId'), table_name='contracts', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contracts_status'), table_name='contracts', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contracts_start_date'), table_name='contracts', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contracts_player_id'), table_name='contracts', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contracts_organization_id'), table_name='contracts', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contracts_last_updated'), table_name='contracts', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contracts_is_sensitive'), table_name='contracts', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contracts_end_date'), table_name='contracts', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contracts_created_by'), table_name='contracts', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contracts_created_at'), table_name='contracts', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contracts_agent_id'), table_name='contracts', schema='crm_dev')
    op.drop_table('contracts', schema='crm_dev')
    # ### end Alembic commands ###