from typing import List, Optional, Union
from enum import Enum
from dataclasses import dataclass
import ssl
import smtplib
import traceback

from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
from email.mime.application import MIMEApplication
from email.mime.text import MIMEText
from email.mime.base import <PERSON><PERSON>B<PERSON>
from email import encoders
import os

from pydantic import BaseModel, root_validator, validator

from app.config import settings


@dataclass
class MailConfig:
    smtp_server: str
    sender_email: str
    password: str
    port: int = 587


class ContentType(Enum):
    plain_text = 0
    body_html = 1
    attachment_file = 2


class ReportContent(BaseModel):
    type: ContentType
    content: Union[str, bytes]
    file_format: Optional[str]

    @validator("file_format")
    def check_file_format(cls, v):
        if v is not None and v not in {"pdf", "xlsx", "csv"}:
            raise ValueError("Invalid format type")
        return v

    @root_validator
    def check_file_has_format(cls, values):
        if (
            values.get("type") == ContentType.attachment_file
            and values.get("file_format") is None
        ):
            raise ValueError(
                "If passing attachment file, file_format must be specified"
            )
        return values


class MailConfigOptions(Enum):
    outlook_enskai_alerts: MailConfig = MailConfig(
        "smtp.office365.com",
        settings.OUTLOOK_ENSKAI_ALERTS_ADDRESS,
        settings.OUTLOOK_ENSKAI_ALERTS_PASS,
        587,
    )


class Mailer:
    def __init__(
        self,
        subject: str,
        recipients: List[str],
        contents_list: List[ReportContent],
        mail_config: MailConfig = MailConfigOptions.outlook_enskai_alerts.value,
    ):
        self.subject = subject
        self.recipients = recipients
        self.contents_list = contents_list
        self.mail_config = mail_config

        self.message = MIMEMultipart("mixed")
        self.message["Subject"] = self.subject
        self.message["From"] = self.mail_config.sender_email
        self.message["Bcc"] = ", ".join(self.recipients)
        self.image_paths = [
        "enskai_logo_1.png",
        "facebook-rounded-colored-bordered.png",
        "instagram-rounded-colored-bordered.png",
        "rectangle_243_1.png",
        "rectangle_243.png",
        "twitter-rounded-colored-bordered.png",
    ]

    def add_plain_text(self, plain_text: str):
        self.message.attach(MIMEText(plain_text, "plain"))

    def add_body_html(self, body_html: str):
        self.message.attach(MIMEText(body_html, "html"))

    def add_attachment_file(
        self,
        file: Union[str, bytes],
        file_format: str,
        custom_name: Optional[str] = None,
    ):
        if file_format == "csv":
            attachment = MIMEApplication(file)
        else:
            attachment = MIMEBase("application", "octet-stream")
            attachment.set_payload(file)
            encoders.encode_base64(attachment)

        file_name = (
            custom_name
            if custom_name is not None
            else f"{self.subject}.{file_format}"
        )

        attachment["Content-Disposition"] = f"attachment; filename={file_name}"
        self.message.attach(attachment)

    def add_mail_item(self, item: ReportContent):
        if item.type == ContentType.plain_text:
            self.add_plain_text(item.content)
        elif item.type == ContentType.body_html:
            self.add_body_html(item.content)
        elif item.type == ContentType.attachment_file:
            self.add_attachment_file(item.content, item.file_format)

    def send_mail(self):
        for item in self.contents_list:
            self.add_mail_item(item)

        with smtplib.SMTP(
            self.mail_config.smtp_server, self.mail_config.port
        ) as server:
            context = ssl.create_default_context()

            try:
                server.starttls(context=context)
                server.login(
                    self.mail_config.sender_email, self.mail_config.password
                )
                server.sendmail(
                    self.mail_config.sender_email,
                    self.recipients,
                    self.message.as_string(),
                )
                server.close()
                print(f"Sent mail for {self.subject}")
            except:
                print(
                    {
                        "action": f"Failed to send mail for {self.subject}",
                        "error": traceback.format_exc(),
                    },
                )
    def add_images(self):
        for image_path in self.image_paths:
            with open(
                os.path.join(os.path.dirname(__file__), "images", image_path), "rb"
            ) as image_file:
                image_data = image_file.read()
                image = MIMEImage(image_data)
                image.add_header("Content-ID", f"<{image_path}>")
                self.message.attach(image)
