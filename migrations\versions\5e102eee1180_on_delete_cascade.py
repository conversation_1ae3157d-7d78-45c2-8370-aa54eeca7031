"""On delete cascade

Revision ID: 5e102eee1180
Revises: b677c202eae5
Create Date: 2024-11-28 10:46:54.963828

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5e102eee1180'
down_revision = 'b677c202eae5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('community_deal_community_proposal_id_fkey', 'community_deal', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key(None, 'community_deal', 'community_proposals', ['community_proposal_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'community_deal', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key('community_deal_community_proposal_id_fkey', 'community_deal', 'community_proposals', ['community_proposal_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    # ### end Alembic commands ###