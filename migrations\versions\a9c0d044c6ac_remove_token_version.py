"""Remove token version

Revision ID: a9c0d044c6ac
Revises: 7080396d4f59
Create Date: 2025-05-14 11:20:19.977534

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a9c0d044c6ac'
down_revision = '7080396d4f59'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'token_version', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('token_version', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False), schema='crm_test')
    # ### end Alembic commands ###