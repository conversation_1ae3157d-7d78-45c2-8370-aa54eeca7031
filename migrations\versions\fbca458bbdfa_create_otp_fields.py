"""Create OTP fields

Revision ID: fbca458bbdfa
Revises: 7765403115a5
Create Date: 2023-11-15 14:58:13.097996

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'fbca458bbdfa'
down_revision = '7765403115a5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('suit_scores',
    sa.Column('playerId', sa.Integer(), nullable=False),
    sa.Column('hiring_team_id', sa.Integer(), nullable=False),
    sa.Column('suitability_score_w_min_coalesce', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('playerId', 'hiring_team_id'),
    schema='wyscout'
    )
    op.create_index('idx_player_id_suit_1', 'suit_scores', ['playerId'], unique=False, schema='wyscout', postgresql_include=[])
    op.create_index('idx_team_id_suit_1', 'suit_scores', ['hiring_team_id'], unique=False, schema='wyscout', postgresql_include=[])
    op.create_index('suit_scores_playerid_idx_1', 'suit_scores', ['playerId', 'hiring_team_id'], unique=False, schema='wyscout', postgresql_include=[])
    op.create_index('suit_scores_team_score_idx_1', 'suit_scores', ['hiring_team_id', sa.text('suitability_score_w_min_coalesce DESC NULLS LAST')], unique=False, schema='wyscout', postgresql_include=[])
    op.add_column('user', sa.Column('otp_enabled', sa.String(), nullable=True), schema='crm_test')
    op.add_column('user', sa.Column('otp_url', sa.String(), nullable=True), schema='crm_test')
    op.add_column('user', sa.Column('otp_base32', sa.String(), nullable=True), schema='crm_test')
    op.add_column('user', sa.Column('otp_verified', sa.Boolean(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'otp_verified', schema='crm_test')
    op.drop_column('user', 'otp_base32', schema='crm_test')
    op.drop_column('user', 'otp_url', schema='crm_test')
    op.drop_column('user', 'otp_enabled', schema='crm_test')
    op.drop_index('suit_scores_team_score_idx_1', table_name='suit_scores', schema='wyscout', postgresql_include=[])
    op.drop_index('suit_scores_playerid_idx_1', table_name='suit_scores', schema='wyscout', postgresql_include=[])
    op.drop_index('idx_team_id_suit_1', table_name='suit_scores', schema='wyscout', postgresql_include=[])
    op.drop_index('idx_player_id_suit_1', table_name='suit_scores', schema='wyscout', postgresql_include=[])
    op.drop_table('suit_scores', schema='wyscout')
    # ### end Alembic commands ###