"""Transfer fee and salary in shadow squads

Revision ID: b5e5d69f4a29
Revises: 305ebb244bf8
Create Date: 2023-11-09 10:52:20.702244

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b5e5d69f4a29'
down_revision = '305ebb244bf8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_players', sa.Column('transfer_fee', sa.Float(), nullable=True), schema='crm_dev')
    op.add_column('field_players', sa.Column('asking_salary', sa.Float(), nullable=True), schema='crm_dev')
    op.drop_constraint('field_players_field_id_fkey', 'field_players', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key(None, 'field_players', 'football_fields', ['field_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'field_players', schema='crm_dev', type_='foreignkey')
    op.create_foreign_key('field_players_field_id_fkey', 'field_players', 'football_fields', ['field_id'], ['id'], source_schema='crm_dev', referent_schema='crm_dev', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_column('field_players', 'asking_salary', schema='crm_dev')
    op.drop_column('field_players', 'transfer_fee', schema='crm_dev')
    # ### end Alembic commands ###