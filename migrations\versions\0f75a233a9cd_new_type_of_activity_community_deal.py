"""New type of activity - community deal

Revision ID: 0f75a233a9cd
Revises: 0760df0c0923
Create Date: 2024-10-24 14:00:25.522390

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = '0f75a233a9cd'
down_revision = '0760df0c0923'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('community_deal',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('community_proposal_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('feedback', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['community_proposal_id'], ['crm_test.community_proposals.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_community_deal_community_proposal_id'), 'community_deal', ['community_proposal_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_community_deal_created_at'), 'community_deal', ['created_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_community_deal_created_by'), 'community_deal', ['created_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_community_deal_is_sensitive'), 'community_deal', ['is_sensitive'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_community_deal_last_updated'), 'community_deal', ['last_updated'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_community_deal_organization_id'), 'community_deal', ['organization_id'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_test_community_deal_organization_id'), table_name='community_deal', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_community_deal_last_updated'), table_name='community_deal', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_community_deal_is_sensitive'), table_name='community_deal', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_community_deal_created_by'), table_name='community_deal', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_community_deal_created_at'), table_name='community_deal', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_community_deal_community_proposal_id'), table_name='community_deal', schema='crm_test')
    op.drop_table('community_deal', schema='crm_test')
    # ### end Alembic commands ###
