import traceback
import uuid
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Response
from sqlalchemy.orm import Session

from app.schemas.contract_upload import ContractUpload, ContractUploadCreate
from app import crud, models
from app.api import deps, utils
from app.utils.cloud_storage import CloudStorageManager
from starlette.responses import StreamingResponse

router = APIRouter()


@router.get("/file_info/", response_model=List[ContractUpload])
def read_contracts(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all files' info.
    """
    utils.check_get_all(ContractUpload, current_user)
    return crud.contract.get_all_w_org(
        db, current_user.organization_id, utils.can_access_sensitive(current_user)
    )


@router.get("/file_info/{id}", response_model=ContractUpload)
def get_file_info(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get file info by ID.
    """
    contract_upload = crud.contract_upload.get(db=db, id=id)
    utils.check_get_one(contract_upload, current_user)
    return contract_upload


@router.get(
    "/download/{id}",
)
def download_file(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get contact by ID.
    """
    # first check if user can actually access the contract file:
    contract_upload = crud.contract_upload.get(db=db, id=id)
    utils.check_get_one(contract_upload, current_user)
    # if all is ok, download file
    cm = CloudStorageManager()
    media_type = {
        "jpg": "image/jpeg",
        "jpeg": "image/jpeg",
        "png": "image/png",
        "pdf": "application/pdf",
    }.get(id.split(".")[-1])
    return Response(cm.get_blob(id), media_type=media_type)


@router.post(
    "/bulk/{contract_id}",
    response_description="Upload multiple files at once",
    response_model=List[ContractUpload],
)
def upload_bulk_files(
    *, 
    db: Session = Depends(deps.get_db),
    contract_id: str,
    files: List[UploadFile] = File(),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    utils.check_create(ContractUpload, current_user)
    files_for_writing = {}
    for f in files:
            ext = f.filename.split(".")[-1]
            if ext not in {"pdf", "png", "jpeg", "jpg"}:
                raise HTTPException(415, "Unsupported media type")
            filename = f"{str(uuid.uuid4())}.{ext}"
            files_for_writing[filename] = (ContractUploadCreate(contract_id=contract_id, id=filename,name=f.filename.split(".")[0]), f)
    written_files = crud.contract_upload.bulk_create_with_user(
        db=db, objs_in=[x[0] for x in files_for_writing.values()], user=current_user
    )
    try:
        cm = CloudStorageManager()
        for k, v in files_for_writing.items():
            cm.upload_blob(k, v[1].file)
        return written_files
    except:
        for k in files_for_writing:
            crud.contract_upload.remove(db=db, id=k)

        raise HTTPException(500, "Failed to upload document")


@router.delete("/{id}", response_model=ContractUpload)
def delete_contract(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a contract.
    """
    contract_upload = crud.contract_upload.get(db=db, id=id)
    utils.check_delete(contract_upload, current_user)
    contract_upload_out = ContractUpload.from_orm(contract_upload)
    crud.contract_upload.remove(db=db, id=id)
    try:
        cm = CloudStorageManager()
        cm.delete_blob(contract_upload.id)
        return contract_upload_out
    except:
        contract_upload = crud.contract_upload.create_with_user(
            db=db,
            obj_in=contract_upload_out,
            user=current_user,
        )
        traceback.format_exc()
        raise HTTPException(500, "Failed to delete document")
