from pydantic import BaseModel, <PERSON>
from typing import Optional, List, TYPE_CHECKING
from app.schemas.extended_base import (
    ExtendedUpdateBase,
    ExtendedCreateBase,
    ExtendedBase,
)
import uuid
from datetime import datetime

if TYPE_CHECKING:
    from app.schemas.field_requests import FieldRequests, FieldRequestsCreate


class RequestFieldUpdate(ExtendedUpdateBase):
    name: Optional[str]
    for_date: Optional[datetime]
    formation: Optional[str]
    requests: Optional["List[FieldRequestsCreate]"] = Field(default_factory=list)
    contact_name: Optional[str]
    contact_email: Optional[str]
    contact_phone: Optional[str]
    class Config:
        orm_mode = True
        use_cache = True


class RequestFieldAutofill(RequestFieldUpdate, ExtendedCreateBase):
    name: str
    for_date: Optional[datetime]
    formation: str
    requests: Optional["List[FieldRequestsCreate]"] = Field(default_factory=list)
    contact_name: Optional[str]
    contact_email: Optional[str]
    contact_phone: Optional[str]

class RequestFieldCreate(RequestFieldUpdate, ExtendedCreateBase):
    name: str
    for_date: Optional[datetime]
    formation: str
    requests: Optional["List[FieldRequestsCreate]"] = Field(default_factory=list)
    id: Optional[uuid.UUID]
    contact_name: Optional[str]
    contact_email: Optional[str]
    contact_phone: Optional[str]

class RequestField(RequestFieldCreate, ExtendedBase):
    requests: "List[FieldRequests]"
    hide_field_view: Optional[bool] = False
    contact_name: Optional[str]
    contact_email: Optional[str]
    contact_phone: Optional[str]

    class Config:
        orm_mode = True
        use_cache = True

class RequestFieldShare(RequestField):
    org_logo: str


class RequestFieldViewAll(BaseModel):
    id: uuid.UUID
    requests: int
    name: str
    last_updated: datetime
    created_by: str
    has_shared_link: Optional[bool]
    contact_name: Optional[str]
    contact_email: Optional[str]
    contact_phone: Optional[str]
