"""Migrate staging to prod

Revision ID: 3d40b0c3da0a
Revises: 5f5d3c3db0c1
Create Date: 2023-09-19 14:05:30.486516

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3d40b0c3da0a'
down_revision = '5f5d3c3db0c1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('community_proposals', sa.Column('position', postgresql.ARRAY(sa.String()), nullable=True), schema='crm')
    op.add_column('community_proposals', sa.Column('foot', sa.String(), nullable=True), schema='crm')
    op.alter_column('community_proposals', 'are_you_the_agent',
               existing_type=sa.BOOLEAN(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm')
    op.create_index(op.f('ix_crm_contacts_id'), 'contacts', ['id'], unique=False, schema='crm')
    op.drop_constraint('contracts_agent_id_fkey', 'contracts', schema='crm', type_='foreignkey')
    op.create_foreign_key(None, 'contracts', 'contacts', ['agent_id'], ['id'], source_schema='crm', referent_schema='crm', ondelete='SET NULL')
    op.drop_constraint('notifications_settings_user_id_fkey', 'notifications_settings', schema='crm', type_='foreignkey')
    op.create_foreign_key(None, 'notifications_settings', 'contacts', ['user_id'], ['id'], source_schema='crm', referent_schema='crm', ondelete='cascade')
    op.drop_constraint('player_records_assigned_to_id_fkey', 'player_records', schema='crm', type_='foreignkey')
    op.create_foreign_key(None, 'player_records', 'contacts', ['assigned_to_id'], ['id'], source_schema='crm', referent_schema='crm')
    op.drop_constraint('source_to_record_source_id_fkey', 'source_to_record', schema='crm', type_='foreignkey')
    op.create_foreign_key(None, 'source_to_record', 'contacts', ['source_id'], ['id'], source_schema='crm', referent_schema='crm', ondelete='CASCADE')
    op.add_column('user', sa.Column('first_name', sa.String(), nullable=True), schema='crm')
    op.add_column('user', sa.Column('last_name', sa.String(), nullable=True), schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'last_name', schema='crm')
    op.drop_column('user', 'first_name', schema='crm')
    op.drop_constraint(None, 'source_to_record', schema='crm', type_='foreignkey')
    op.create_foreign_key('source_to_record_source_id_fkey', 'source_to_record', 'contacts', ['source_id'], ['id'], source_schema='crm', referent_schema='crm', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_constraint(None, 'player_records', schema='crm', type_='foreignkey')
    op.create_foreign_key('player_records_assigned_to_id_fkey', 'player_records', 'contacts', ['assigned_to_id'], ['id'], source_schema='crm', referent_schema='crm', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_constraint(None, 'notifications_settings', schema='crm', type_='foreignkey')
    op.create_foreign_key('notifications_settings_user_id_fkey', 'notifications_settings', 'contacts', ['user_id'], ['id'], source_schema='crm', referent_schema='crm', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_constraint(None, 'contracts', schema='crm', type_='foreignkey')
    op.create_foreign_key('contracts_agent_id_fkey', 'contracts', 'contacts', ['agent_id'], ['id'], source_schema='crm', referent_schema='crm', onupdate='CASCADE', ondelete='CASCADE')
    op.drop_index(op.f('ix_crm_contacts_id'), table_name='contacts', schema='crm')
    op.alter_column('community_proposals', 'are_you_the_agent',
               existing_type=sa.String(),
               type_=sa.BOOLEAN(),
               existing_nullable=True,
               schema='crm')
    op.drop_column('community_proposals', 'foot', schema='crm')
    op.drop_column('community_proposals', 'position', schema='crm')
    # ### end Alembic commands ###