from sqlalchemy.orm import with_polymorphic

from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.contract import ContractCreate, ContractUpdate
from typing import List, Type, TypeVar

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy.orm import Session, lazyload, Load

from app.db.base_class import Base
from app.models import User

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDContract(CRUDBase[models.Contract, ContractCreate, ContractUpdate]):
    def __init__(self, model: Type[ModelType]):
        """
        CRUD object with default methods to Create, Read, Update, Delete (CRUD).
        **Parameters**
        * `model`: A SQLAlchemy model class
        * `schema`: A Pydantic model (schema) class
        """
        self.model = model
        self.model_cols = [x for x in model.__dict__.keys() if not x.startswith('_')]

    def get_all_with_filters(
        self,
        db: Session,
        org_id: str,
        can_access_sensitive: bool,
        filters: dict
    ) -> List[ModelType]:
        # Base query
        query = db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
        ).filter(self.model.organization_id == org_id)
        
        # Apply sensitive data filter
        if not can_access_sensitive:
            query = query.filter(self.model.is_sensitive != True)
        
        # Apply additional filters dynamically
        if "contract_type" in filters and filters["contract_type"]:
            query = query.filter(self.model.contract_type.in_(filters["contract_type"]))

        # Order by last_updated
        query = query.order_by(self.model.last_updated.desc())

        return query.all()

    def create_with_user(
        self, db: Session, *, obj_in: CreateSchemaType, user: User
    ) -> ModelType:
        obj_in_data = jsonable_encoder(obj_in)
        filtered_obj = {k: v for k, v in obj_in_data.items() if k in self.model_cols}
        db_obj = self.model(
            **filtered_obj, created_by=user.id, organization_id=user.organization_id
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get_all_filtered_for_team(self,
        db: Session,
        id: str,
        org_id: str,
    ) -> List[ModelType]:
        return (
        db.query(models.ClubContract)
        .filter(models.ClubContract.teamId == id,
                models.ClubContract.organization_id == org_id)
        .all()
        + db.query(models.CommissionAgreement)
        .filter(models.CommissionAgreement.teamId == id,
                models.CommissionAgreement.organization_id == org_id)
        .all()
    )

contract = CRUDContract(with_polymorphic(models.Contract, "*"))