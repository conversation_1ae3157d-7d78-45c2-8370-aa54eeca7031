from sqlalchemy import Column, Integer, Table
from app.db.session import engine
from app.db.base_class import Base


class PlayerAggregateStats(Base):
    __table__ = Table(
        "player_aggregate_stats_view",
        Base.metadata,
        Column("playerId", Integer, primary_key=True),
        extend_existing=True,
        autoload_with=engine,
        schema="derived_tables",
        info=dict(is_view=True)
    )
