<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html data-editor-version="2" class="sg-campaigns" xmlns="http://www.w3.org/1999/xhtml">
    <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
      <!--[if !mso]><!-->
      <meta http-equiv="X-UA-Compatible" content="IE=Edge">
      <!--<![endif]-->
      <!--[if (gte mso 9)|(IE)]>
      <xml>
        <o:OfficeDocumentSettings>
          <o:AllowPNG/>
          <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
      </xml>
      <![endif]-->
      <!--[if (gte mso 9)|(IE)]>
  <style type="text/css">
    body {width: 600px;margin: 0 auto;}
    table {border-collapse: collapse;}
    table, td {mso-table-lspace: 0pt;mso-table-rspace: 0pt;}
    img {-ms-interpolation-mode: bicubic;}
  </style>
<![endif]-->
      <style type="text/css">
    body, p, div {
      font-family: arial,helvetica,sans-serif;
      font-size: 14px;
    }
    body {
      color: #000000;
    }
    body a {
      color: #1188E6;
      text-decoration: none;
    }
    p { margin: 0; padding: 0; }
    table.wrapper {
      width:100% !important;
      table-layout: fixed;
      -webkit-font-smoothing: antialiased;
      -webkit-text-size-adjust: 100%;
      -moz-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }
    img.max-width {
      max-width: 100% !important;
    }
    .column.of-2 {
      width: 50%;
    }
    .column.of-3 {
      width: 33.333%;
    }
    .column.of-4 {
      width: 25%;
    }
    ul ul ul ul  {
      list-style-type: disc !important;
    }
    ol ol {
      list-style-type: lower-roman !important;
    }
    ol ol ol {
      list-style-type: lower-latin !important;
    }
    ol ol ol ol {
      list-style-type: decimal !important;
    }
    @media screen and (max-width:480px) {
      .preheader .rightColumnContent,
      .footer .rightColumnContent {
        text-align: left !important;
      }
      .preheader .rightColumnContent div,
      .preheader .rightColumnContent span,
      .footer .rightColumnContent div,
      .footer .rightColumnContent span {
        text-align: left !important;
      }
      .preheader .rightColumnContent,
      .preheader .leftColumnContent {
        font-size: 80% !important;
        padding: 5px 0;
      }
      table.wrapper-mobile {
        width: 100% !important;
        table-layout: fixed;
      }
      img.max-width {
        height: auto !important;
        max-width: 100% !important;
      }
      a.bulletproof-button {
        display: block !important;
        width: auto !important;
        font-size: 80%;
        padding-left: 0 !important;
        padding-right: 0 !important;
      }
      .columns {
        width: 100% !important;
      }
      .column {
        display: block !important;
        width: 100% !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
      }
      .social-icon-column {
        display: inline-block !important;
      }
    }
  </style>
      <!--user entered Head Start--><!--End Head user entered-->
    </head>
    <body>
      <center class="wrapper" data-link-color="#1188E6" data-body-style="font-size:14px; font-family:arial,helvetica,sans-serif; color:#000000; background-color:#FFFFFF;">
        <div class="webkit">
          <table cellpadding="0" cellspacing="0" border="0" width="100%" class="wrapper" bgcolor="#FFFFFF">
            <tr>
              <td valign="top" bgcolor="#FFFFFF" width="100%">
                <table width="100%" role="content-container" class="outer" align="center" cellpadding="0" cellspacing="0" border="0">
                  <tr>
                    <td width="100%">
                      <table width="100%" cellpadding="0" cellspacing="0" border="0">
                        <tr>
                          <td>
                            <!--[if mso]>
    <center>
    <table><tr><td width="600">
  <![endif]-->
                                    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="width:100%; max-width:600px;" align="center">
                                      <tr>
                                        <td role="modules-container" style="padding:0px 0px 0px 0px; color:#000000; text-align:left;" bgcolor="#FFFFFF" width="100%" align="left"><table class="module preheader preheader-hide" role="module" data-type="preheader" border="0" cellpadding="0" cellspacing="0" width="100%" style="display: none !important; mso-hide: all; visibility: hidden; opacity: 0; color: transparent; height: 0; width: 0;">
    <tr>
      <td role="module-content">
        <p></p>
      </td>
    </tr>
  </table><table class="module" role="module" data-type="code" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="7a23fbfc-da2a-414e-b3c3-c66299693231">
    <tbody>
      <tr>
        <td height="100%" style="text-align: center;" valign="top" role="module-content"><img src="data:image/png;base64, iVBORw0KGgoAAAANSUhEUgAABjAAAAGMCAYAAAB0yOt+AAAABmJLR0QA/wD/AP+gvaeTAACE0klEQVR42uzdCZgcZZ348ZpJMlNdVd2TmenqnkxC7pNc5OYm3Pfhungs7noh67H7F891V9So6LqriwqKsqAI3qPCcoggYhQEEjI5JsnkhhAC4QiBQELuTP1/1T2TuXqS7p7ut67v53neh13XZbqruqq769v1vpoGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAi7piu1AQwfj6YgjCt9ORYs0Co5wgEAAAAAAAAggJw1k0Y4a6b+xFk79WedY3qe44Q+xsweY1Z2rCt2zOkx5vY9NnSME48yTu4+NrrjlGOM0woYp8uYX+Q4s/d4WsYzZx9jnFPguCD3ePZY48I+xsV5jEs6x5aOcVmOcXnvsbXI8dwV1zkL5w/kSAcAAAAAAACAAHFWTB/vtE5ZJwHD6T2m5RjTs2Ntz3FCjzEjx5iZHeu6jll9jNmdY33HmNNjzM0x5nWODe448SjjpO5j48k5xik9xqm9x6aOcVqOcXqPcYbjPN1zzO8xzux7PHNWj3F2jnFO59jsjnOPMs7rMc7Pjme7jgtyjAu7jy0d46Ie4+Ic45LO8VzHuDTHuKz32Hp5jnHFscYhiRi3ETEAAAAAAAAAICD2rzh+ssSLlTKczCBiEDHCHDG2XnGL03plFUc+AAAAAAAAAPjY7uaJU53WycuOxAsiBhEjEndiXP69bc2XGpwBAAAAAAAAAMCHdi8bP13iRbMMp1fAIGIQMcIdMQ46W6648ZXWKy3OBAAAAAAAAADgI7uXTpjWGS86RhAjxpw8IsY8IgYRI3fE2Hr5d7evuyzOGQEAAAAAAAAAfGB/y6QpTuvxS7vHCyIGESMoEeOyUkaMAzK+Q8QAAAAAAAAAAI85K6dOdNZMapGA4WQHEYOIEfmIkZlO6qWW80zOEAAAAAAAAADgAadlwihn9fFrOuMFEYOIoTpiXOTf6aSeu+ImZ+OF1ZwpAAAAAAAAAEAhZ+3Ekc7qSetlOBIxnGBFjFlEDCKGiohxyHnubT90Fs4fyBkDAAAAAAAAABSQOy8mSLRYm40XHSPMEWN2HhFjLhGDiNFHxLjiNiIGAAAAAAAAAJRZdsHuiSu7xwsiBhGDiHHUiLH1iluYTgoAAAAAAAAAymT3svHTndUTljurJzrZQcQgYuQbMc4hYjx3+fdY2BsAAAAAAAAASmz3U+NOkDsvmjvjBRGDiMGdGAUv7L3lihu3r7sszhkFAAAAAAAAAEpg92K582LVhKVy94XTO2BEMWKwsDcRox8RY+vl392x6MIEZxYAAAAAAAAA6If9S8ZMcVZOXCYBw8mM/CLGQRkbJVCsy29M6T3WTM0xpuUY07Njbc9xQo8xs++xrmPM6jq2ymjrR8RYJ+Gi+9hwrHFS97Hp5BzjlB7j1KOM03KMM3qPp3uO+T3GmX2PZ86Scfa2fkSMvTI2yH++TqJFjnF+djzbdVyQY1zYfWxxx0U5xiV5jkt7jMtyj62Xu+PFAiPGATdicCcGAAAAAAAAABTJaZkwwVk1bpWzarxzJGDkFTEmbn2redKQID/3N5ZMvVDCxZ4i78Roi9TrZNNZ7+vHnRjNWx47tTbQz/+5S98nQeJwwXdiPHfFTayJAQAAAAAAAAAFctaMGiHhYl02XnSMfCPGxK3bHw12wHjxb7kCRt4RI+IBo6CI0fzY/WEMGHlHjO87zbMGccYBAAAAAAAAgDxJpLi+e7woJGKEOWDkFTGiFzCKXhPjnHAEjOcuO1zkmhiv71x10WzOOAAAAAAAAACQJ5k6qo+AkVfECHnAyBUxZhMwiosY4QkYxS3sTcAAAAAAAAAAgEJkA8Y4p8iIEZKAMfMoAeOoESO6AaPQiBG2gFF4xCBgAAAAAAAAAEAhOgNGUREjRAFjplNExIh2wCgkYoQxYBQWMQgYAAAAAAAAAFCI7gGj4IgRjoCxtiNgFBwxCBj5RoywBoz8IwYBAwAAAAAAAAAK0TtgFBQxwhMw1nYEjIIiRsQCxvw8A8ZZ0QoY+UUMAgYAAAAAAAAAFCJ3wMg7YoQkYMzIBozCIwYBI9+IEZqAcWnugHHsiEHAAAAAAAAAAIBCZALGyrFOkREjRAFjhlNExIhgwJjvFBUxQhUwLnWKiBgEDAAAAAAAAAAoxJGAUVzECFnAKDhiRDRgFBExQhcwCo4YBAwAAAAAAAAAKES3gFF4xAhhwCgoYkQ4YBQYMUIZMAqKGAQMAAAAAAAAAChEr4BRUMQIa8DIO2JEPGAUEDFCEzAu6REw8o4YBAwAAAAAAAAAKISzakzvgJF/xAhJwDghR8DIK2JEMGCc4RQVMcIQMDZ3BIxLnCIiBgEDAAAAAAAAAAqRDRhjnCIjRogCxglOEREjogGjiIgRloCxpSNgFBwxCBgAAAAAAAAAUIjOgFFUxAhZwCg4YkQ4YBQYMcIUMLZ0BIyCIgYBAwAAAAAAAAAK0T1gFBwxQhAwju8RMAqKGNEKGBt6BowCIsbms0IQMC7qDBiFRwwCBgAAAAAAAAAUonfAKChihCRgTO8RMPKOGNELGJt6Boy8I4YEjKkhCBgXdwaMwiIGAQMAAAAAAAAACpE7YOQbMcIUMKY7RUSMiAWM0yRgnO4UFTE2hSlgXOwUETEIGAAAAAAAAABQiGzAGO0UGTEkYIwIUcAoOGK0OetnfuzoY3aeY14/xkm9x8ZjjVOPMk7PPZ4+46fZgFFExAhdwCg4YhAwAAAAAAAAAKAQTktHwCgqYoQwYBR1J0bXhb1zjNmdY33HmNNjzM0x5nWODe448SjjpO5j48k5xik9xqm9x6aOcVqPcXqOUUDECGXAKChiEDAAAAAAAAAAoBCZgNHSETAKjhjhCBhregYMIkbJI0ZoA0beEYOAAQAAAAAAAACFOBIwiosY4QkYa3oGDCJGSSPGpvkhCRgX5QgYeUUMAgYAAAAAAAAAFMJpGdUZMAqPGOEKGESM8kWMUAWMi5wiIgYBAwACY1jMMJKNVVX1E2OxuhN13T5N15NnxGL1b8uMqvq3VVfXnZv9z+3TzKraqdXV9rhEIlEn/8+VbD8AAAAAAEokGzBGOUVGjJAEjGmdAaM/EWMdEaPPiBG6gFFwxCBgAID/DNT1wSNisdQppt7wXtO0v23oqT8YsdQyM5baIP/casTSrxlG6g35n3fJcLqMvZn/PPt/e1HGFlNPrbYM+0kzlm4y9PR1VqzhSmNQcqZpmmn5WwPY3AAAAAAAFKgzYBQVMUIUMKY53kaMOXlEjHnBjRihDBgFRQwCBgD4QEJL1MVi6XlmzP43N1a0h4o9PeJEKcfrMlbKuEv+7rVyZ8fMeDyeZE8AAAAAAJCH7gGj4IgRsoBBxChbxAhFwLggR8DIO2IQMHIYrA0eLL9S/rz8WvkbjP4P+SX4FX7Zt/J4ri3lc4sbyQ/Jv7Y65IdEpRwP16h6vUTrbGNbcnfFCYZhXy8h4cnM3RPlCxbHGm/JXR1L5K6Pb7ohRdPqEvIAK6K0N3R9yAjLaPiKitd5XKb4CuAmGmDF0u/y4n1E9suX3buS+ITSt8bGRsMyUl/28v3eMtJfM83UdPZG/mQ6wOOUfR6rTl7MFgcAACih3gGjoIgRwoBBxChLxNh0RkgCxoU5AkZeEUMCxrkEjF5fJmuHy8W8lzy8kBi2cbNf9q18gV9Z4ue2y4rZV4b8kBgo2+0vql4vETjFVNRoNbVyke8q07D/6HG06Gvsl/GYVW3/qwSWBi0i00xJvDk5G3LKv43lDptPBW37JIzkxe1Tk6l+PbbJ9rpTY7qzo3J/LNB+7Hp6/rCM5A/ZG4WcdxLzVO0bed/5H7Y4AABACeUOGHlHjJAEjKk9AkYQIkbA1sQIS8B41g0YFzpFRAwCRs4vkwQMAkZBY1Oi2h4b4kOCgFEitVptjVmd/rg8z40BOn5fjJvJG6qrGydoIb8jg4DRt+rq1Bh3PRWPXoNLLSud4tPJMd/fmnxyztgh94MY7JF8zzsEDAAAgMDKBoyRTpERI0QBY6rjr4gRsoW9wxQwnnUDRsERg4CR88skAYOAUfC4S9OGxUJ6SBAw+m1stfw6+iNGzH4uwMexLA5u31ZbWzs8vOd+AkZuaVMe8+NeBTR53c3gk8nRxeONyTKvmVPgHTPpf2Cv5HveIWAAAAAEVmfAKCpihCxgEDHKFjHCFjAKjxgEjJxfJgkYBIzCp9yRdQM+HtJDgoBRvEpdT57h/oJcxqEQHMttmcW/Dfurg0X4zv0EjBwqZEqgm+UxH/bg9bbH1O33aRFbi6UYllX/MT+dK+Q94355WIPYM/mcdwgYAAAAgdU9YBQcMUIYMIgYZYoY4QsYhUUMAkbOL5MEDAJGUeMVw6ifE8JDgoBRzGvNTKVl6qVvyXPaHcJj+rDcUdIqi9hfFqaLlASMXipNveG98ngPevEak/VXvi2PoZpPJceky91dj/nsHHGgpqZmNLsmn/MOAQMAACCwegeMgiLG1reCHjD+Mj5HwCBilD5ihCVgXNA9YOQfMQgYOb9MEjAIGEVOm2EkF2mabYXskCBgFMa962K+EUsv8ehX6yrHLstI/W8sVndcOM79BIyuEon6ue56Bp68tozUQ6x7kef72qDUdI8i09GHnr5O4+6ZPM47BAwAAIDAWbBAq3RaRsyU0dw7YOQdMXbL/3xty0NpM6jbIRMwWqfkCBhEjNJGjDAFjAucIiIGASPnl0kCBgGj+F8Nx/T0f4bsog0BI2/DYu5UYvI8Xo7Q8S3TSqWbE2bdBe5rJdjnfgJGB10fPMLdrx69pjaz7kXeKuV1+18+PTcs07TaGnbRsc47BAwAAIBAueUabZBEinc7K0Zsk4DhZEfREWO/jJ+tv68xGcRt8fKfJ5zitE5dJBFjuUSKPMb0zrGu5zihx5iRY8wqYMzuHOvdMSfHmNtjzOs+NhxrnNR9bDw5xzilxzi193i6Y5yee2w67ReLfjYvEeTjpnvAKDhiEDByfpkkYBAw+jdvu6U3nKmFJ2IQMPIQj8fr5aLr9zKLXUfzOH8luw5MoxHccz8Bw1VXV5eQbfFTj15He2Ix+53yMCr5NJLXeSfpTufm03PCQctqmM9eOtZ5h4ABAAAQGJl4sfy4j0q82CXDyYz+R4w2p2XMX5xVo9JsYYRV74BRUMQgYOT8MknAIGD0d+qM1OpYrH5oSA4JAsYxzxn1Q03Dvj8kC3X3azF7mVLqh0Fd4JuA0X68m/Wfc9cw8ORuHiP9dS3gd/KoJNH0Qj9PVWdZqR/KwxzAnjraeYeAAQAAEIwLsPIrVWf5iKucFcMlXgx3jgSM0kQMd9y97p5knC2NUB4/OQNG3hGDgJHzyyQBg4BRggs3euoHWjgWoCVgHIU7T3/79mnjWG9ffNmw/xDE9QsIGO4F8fSl8vhe9+K14x5HQb6DxwODYrGGO31+PtgcljVyynfeIWAAAAAE4wLs8pEnSLjYlo0XHaPUEWPMdU1N/AIIITx+Np8rAeP8HAEjr4hBwMj5ZZKAQcAoyThkmsl3h+CQIGD0wb3TQB7zExzjOceTiUSiLljn/mgHjHh1crwZs5/x6PXygq43jOQTSP50vWaUu938fi7Q9dR72FtHO+8QMAAAAHxv88KRukwd9UD3eFGWiLHLWT3mFLY4wqYzYJzvFBExCBg5v0wSMAgYJRtbqqqGTAr4IUHAyCGZTMblIvSjHN9HHSur5aJ4cM790Q0YNTU1tbL2xEKPXidvJczU+Xz6KIysffHhQJwH9PTvNW1YjD3W13mHgAEAAOB7h5Yd9y5nxXF7cweMkkeMu5pu4AM0wqV7wCg4YhAwcn6ZJGAQMEp98aYuEeBDgoDRy0hd5p7/CdNG5TUeD8oUMhEOGAMMI3mzR2u4HJQL8QvkMVTw6aMQtmXGkn8KyDlgh2naJ7DP+jrvEDAAAAD8feF1gVbptBz3sAQMJzvKGDGyAePNPc0j57HlEarjyA0Ym7sGjIIiBgEj55dJAgYBo7QX6OTizefkz1cG9JAgYHQ3QC4+fyZz4ZVjO6+FmTOvHyPZ6P9zfzQDhuyba+Qx7fFkkWcjeXc6nTb55FHoazXtXvTeGZjzgJH6Mnutr31JwAAAAPC1HX8bPlmmj9rRGTCURIz/ZMsjTI4EjM1dA0beEYOAkfPLJAGDgFHy8aKl26cH9JAgYHRhWckz3V8Uc1wXFjEs0/61O02Rv8/90QsYieyF8Fc9el20VFfbY/nUUcxrNf2NgJ0Dlsbj8SR7Lte+JGAAAAD42oEljR90lg87JBHDURYxVox+vOVOfumF8MgGjPOyAaPwiEHAyPllkoBBwCjHBcvkIsuy7AAeEgSMjtePaTew7kU/7kSKuRfPRur+PfdHK2BYVjplGqlWr6YVShi1F/KJo7j95safgB3/e2XavYvYe7nOOwQMAAAAf194XT70ZgkYTnYoixiblzYN49deCM9xdCRgnOcUETFe37mMgNH7yyQBg4BRlnHIjNnfkYcxMGCHBAEjq1IuwF3n0ToBYRm7ZbqiqzWfrncQrYDhrqGQ+oVHr4P98rc/LQ9iAJ84CheP11+eCQJBO/6N1E801jrJcd4hYAAAAPj7wuuyYY90BgxFEWPl6DeafzmMdTAQnuOoW8AoOGIQMHJ+mSRgEDDKNnbJBdzLAnZIEDC0zN0XJ8jj28bx3P/p1GRR75P8ee6PTMAYaJrJT8vjOOzJmihm8qduQOHTRlEq5LXz84Ae+1t0ffAIdmHP8w4BAwAAwN8XXpcPW9Y9YKiJGM2/ajyPrY/QHEe9AkZBEYOAkfPLJAGDgFHW8bQ7FVGADgkChlw0tPT0rRzLJRuL/bgeRkQCRoXcSXRhJqZ6tO6F+x7LJ43i6Hp6tGzD54M6jZxhpK9mL/Y87xAwAAAA/H3hdfnQHAGj/BGDgIFQHUc5A0a+EYOAkfvLJAGDgFHuiwh2kxac6VMiHzDa777YzrFcsnFYYsG3/HfuD3/ASCTscfL3N3t1B5osPn0inzKKZxmpj3h050xJRixmL2Qv9jzvEDAAAAD8feF1+dCnZDiKI8ZbzU2Np7L1EZrjKBMwzs0RMPKJGOcTMHJ+mSRgEDDKP3Tdfr8WjPnAIx8wLL3hBxzHpV8TRo6B0/117g93wEgkEnUqj+UAn/P8aoBsxycCfty/LtMozmJXdj3vEDAAAAD8feF1+bBfZAOG0ojxUnPTsKlsfYTmODoSMM51Co8YBIzcXyYJGAQMJeMVq6phcgAOiUgHjFisfijng7KNpZo2LOaffR3qgFFlxOzve7XuhUxbdYfGot39PRfNk4Ww3wj4Md9mWfVfZW923a8EDAAAAH9feF0+9N9ltCmNGCtGrVh819B6tj5Ccxx1CxiFRgwCRu4vkwQMAoaiYaT+6P4q2ueHRKQDhuyjD7t3C/hrGpbUmzIVz1q5CP6oqTc8KP/7zzuGadj3yjQtf3bXGmg/jx3y88VMeW1dK5u50h/n/vAGDMtKuq/j3Z5MmRdLNw8WfLro9+vzvzIxKPifCZ7RtJE6e7RjvxIwAAAAfG33o0PPlnCxuzNgKIgYy0fesWCBP74oA6XQO2AUEjEIGLm/TBIwCBjqptGxjOQX5KEN9PEhEfE7MFKP+OB1ssdd+Nq9gJkw7Qvi8eQEmYZliKbVJTSt0ejxkKs0zbZEStdrRiUS9XPdefPNmP0r+f9f58P587e5CxP7ZF+HMmC4U3W529mr/WsYDXP4ZNFfw2Ierl1S6rFfzl+XsE87zjsEDAAAAF/b/rdkXKLFuu4Bo6wR44AEjMvZ8giT3AEj74hBwMjBNM20TLVxr3zZ+xuj/8OrxWpz7lv/BQwZ6ddMs/4cHx8SkQ0Y7dNH7fLw9bFDLqrfaeppeX3U1Pbz6VTEYnXHGUb6cvn3/rY90vri19zy+v+ulgkvXu/v8AUMN3TJ3/LqvPeWxLar+VRRkv14sWzPA2H5YYOpp37BXu047xAwAAAA/H/xdcXQ6yRaHFYUMZatuieVZqsjVMdQnwEjr4hBwECk+DNgZMYiubg8zKebLcIBI/1uj6ZgOih3TPxZ15NnaOW5O2egXBCdKXf/uIuTb/dDxJM1EmZ4v7/DFjDcX+3bv/UoVMlxk75RHsQg3nn6rSKmp34apjsz3Wnw4nGm9M2edwgYAAAAvvfaw43DneWN63sHjJJHjEPyP3+ALY6wyQaMc/oIGMeMGAQMRIqPA0ZbLDvVlh8v9kU1YFTI4/m+F9OrSLz4djweV3Fxr9JypxeStVg8/3W3Yd+kebzIc8gCxgB5HX3OqynDTNP+i0xjZvOu03+JxLA62aY7Qze9pJH+EHuXgAEAABAYEiY+JBFjX1kjxorhDy76mTtXNBCy48cNGM+4AeMcp4iIQcBApPg4YLhjtxWz3+nDzRbVgDHAUD/1ziFDT31F/QK3jYZpJj8lIeMNb36pL9u5OnmxRsAolQq5UHm+queSYzwrAeME3nFKw9DTHwzj+lgxuctMnl511PcvAQMAACAgNj4wtloCxq0ynDJFjG1Oy6gJbGmE0ZGA8YwbMAqOGAQMRIrPA4Y7NtfIwss+22wRDRjJuDuVk9JfrccyF5e8WguiwrKSZ7SvjaFs6ii5iPn/0um06Yc9HpaAkai2x8rUUc95teC83HjxDt5tSkaXsPhwGAOGGy+tqvpJUd/BBAwAAIAA2XZfY1ICxv1liBgvy39vDlsYYeVsPqszYByJGOfkGzEkYMwnYCAyAhAwHPm17e81j3+J3kMkA0Ys1jBX9Toomub9hXxdT4+SC/nryn2RW+40+aFlpVN+Oj+EIWAMFnL3w6MeTgV2vca6FyXjXuBXHVIVTyP1dXmalVHexwQMAACAgNn0UDolAePnJYwYz8rC3SeyZRFm2YBxdmfAKCxiEDAQKYEIGO4CzmbqMz7abJEMGKaeukrlfpdwcJZfnnt1dWpMmabPOmDqDX+oqUm67zsD/XZ+CH7AGKnLnRc3ebRot8TX1AO1Wm0N7zQlUylB6KuK9+MzMnapCxip1oSWqIvyTiZgAAAABNC6e5JxCROfkYixox8R44CM+5xlw49niyLsOgPG2U4REYOAgUgJSMDITK1jGMmZPtlskQwYcifMfyjc509oPvvVurz+Zsnj2lSyC2eGvUzW2fgHTbMtv54fAh4w5GJ38prM3S0eTX8nEW407zKllHAX725RuA/bJCgskGP1doV/87DchXFplPcyAQMAACCgmpq0AU5L40wJFr+RsaOAiLHfaRn+hPx33uuGELYkoqB7wCg4YhAwECnBCRiZ8aQ7HYwPNlsUA0aFRKQbVV00lIvZn/bj8RKPJy+Tx7ejn89vq8Sg6+TieqO7Xf18fghywND1zPolL3h0rton01Zd4Pf9G7j3KzN1XuYCv9L1S+omy989V2UIk2PhZ5p3a//44LxDwAAAAAi0B24cW31gyYgZEiT+Xe7I+Kv8c4OMVyVYvNFlbJGxXALGzc6yEReuvWtoPVuuPBxHq3j4ltE1f/vphAkvPzr5FGfNjH901s74orN2+o3O2mk/cdae0DF+LP/Zf8r4pPzf377lz1NnPfLzUSMW3j5SZyuWYb/0Chi5IkafC3sTMBCtC0LBChhyscF25wf3+pf5UQwYg+Q5/1LRc96v6/ZpPj1kBpi6/e/uYyz0eUkMeFN+yf0jq6rOvRs2EHPcBzVgDNYbRkpwa/ZsIWYj+UXeXUp/3pW7IW5X+n4TSz2aPVbHVst0YKsU/u1t1dX2uKjuaAIGAABAiDTfog1a9X+Nx6367ZDZq36TOrljrPmtPY67LcqnacHkqmW/GzXCaZ3+DmfNtP911kx/XMYLMg7KcCRS9Bgn9Bgz3LFHxgZnzcyHnNaZC2Sc9mjTWHvBgmgv2lcquQNG3hGDgIFICVrAcC+mmtWpczVvf9kc1YDxa3W/evbXYtZd1dbW1kiIuL+QIGPEGh5rf90ODNL5IZgBY1hM1kn4jUfrXsjftO91HwPvLqXlTscl2/dZlSHKqE59pMvf/7zSuz8M+2NR3dcEDAAAAKA4FSvvn1rrrDnhYmft1N84a6Zuk3jRJsPpHNM7R34Ro+s46KybtVyCxn/vXzVz2u0LuDOjP7IB46wcASOviEHAQKQEMGC4Y2UsVjvcw80WxYChy6+fH1b0nHf7/bhpXw/j+WPOZR9LbZTtJhdBG40gnh8CGDAqDT35RXdxdE/uEIul1sssdyN5ZynDCUhPf1Dx/nwlFms88j5jVdVOcf8zVX8/FrP/7Of1ccp73iFgAAAAAIWo2JIJF1M+LtGiRYbTOablGP2KGDJmOs66mftk3Cv/nfmtTZOr2AWF6wwYZzlFRAwCBiIloAHDcacS0TTPYm8EA0aj4S6sTcA4wl0g+gt9/8o//ZpczP6m/HeGBPn8ELSAIb9av0j+Xbs8Oi/tcNdK4F2lHEa6AfWPamOU/eter6+YfZ/C97g3Eom6E6O4twkYAAAAQJ62Nc8ynNWTP+S0TnlWhpMZ3QJGWSNGdqyf8Udn/fS57lob7JH8dQ8YBUcMAgYiJbABI7NIbv17PdpsBIwyTyHlk8Xaj8qyGmx5HazoNe2MYf9WLopND8P5IUgBw/21vPx7XvLofHQwbtZ/lneU8pAQOFO28U6F+3O/LN79971fY8l3qby7R46J/47i/iZgAAAAAHlwWiefKmOxRIu2I/HCq4ixbuZemV7qB07LtGHsmTz3X6+AUVDEIGAgUgIcMGTYz1lW7RQPNlsUp5CqsmKpe1RdPJQIMCMIx4/RbVqbdLNMc3O2FrB1Lo4mKAEjnU6bckwu8upcJNvpTg/vCAs9Q7e/pngqsA01NTW1PR9HPB5Pyp0ZaxXehdEqF9jTUdvfBAwAAADgKCRaWHLXxTfknztlONkxxTl2xJha7ojRJhFj/aG1M97usND3sffjpvk5AkbeEYOAgUgJdsCQoacelGWVaxRvNhbxLu84bFn1Hw3C8ZPQEnVu2LFi9r/KZXQzbOeHgASMKgle3/do0W7HNFKLY7G643g3KQ+JBvWynVvUBozkN49yTHxL6Z0gsdTfRW2fEzAAAACAPux8bOpoCRaPyDjUGS98FTHcsdPZMOsr2/82Ic4e61smYDx9Zo6AkVfEIGAgUgIfMGQ6D/nV+3WKN1sUA8bAuJG6Q+FF4YfkbwYh2Fe0L7QbyqkeAxAwBsj0Qh/K3LXjzfnn5bhun8Y7SfnI/r1UtvNehfv0dVlA+9S+j4m6k9z/jsK7MH4XkHNhCc87BAwAAACgmwVyR0NmyqjVk9c4rcc7veNFIRFjmoKIMeuAs272r158dIbN3svtSMB4+kyniIhBwECkhCBguGO7rifPULjZohgwNPdXyWrnoE9P4Qj1lt8DhmXZp8v/7zaPzjsy1Vnqw/IwBvBKKetr8OeK9+vf2qNkH9KmPKZHFD6eF+NV9ROjtc8JGAAAAMARmXixZtI5Eiiez8aLjuH7iCFTSs3+w/N/mljPXuytW8AoPGIQMBApIQkY7q9Ul7rzkyvabJEMGLFY+uNqp3Gxf8m6Al7vc/8GDLnzapSsg/OUV+ccXU/9UB5GNa+Scr7+aoe7ax0p3K9tslD3J4/1uIzq1IcVTll2WM69n4jWfidgAAAAABlH4sXqiVud1RItVncNGIGIGDJm37uROzF66RUwCosYBAxESmgChlzkMYz0jZqaqXwiGTBk+16meJ8eNM3692oaaz95xa8Bw7ZtyzTtX3p3vkkviccbk7xCyisetz/ongdU3u1gVdVNPtbj0vWa0fLf3arwcS2WPzsoOucdAgYAAACQITHirGy8mORkR1Ajxsx7uBOjx77NFTDyjxgEDERKiAJG5oJ3wkherJU/YkQyYFhVQyZ5sE9fMavtCzQihid8GjAGGLp9neIL213jxWuyaPgMXh1lV6l4qiZ33C1/tyqf9wAJuj9S9bhkO7wpMefU6Jx3CBgAAACA9upfx02SaLGhM14EPmL88KWHppns2aw+A0Z+EYOAgUgJWcBwxzOyyOqwMm+2SAYM93nL49npwT59SaYIu0IjYijnx4DhvhZkvOHR+WWfqdvv5ZWh4L3JTE1TfL6R6aMa3pHv47Os+rMyd/4penxxM3lDdM47BAwAAABE3M7HptY6qyc8KndfONkRiohxyFk/61POAi7uuLIB44zcAePYEYOAgWhdJApfwFCxdkJUA4YXv4juGLstI/klTWs0OGrV8VvAiMfrJ8p/d7NHr8E2y0rewqtCDYlUX1a4zoQ7Nkn8Pi7fx1dTU1PrTiWmcnqrqKwJRMAAAABA5DmtE2+UcHG4M2CEJmK87myYcQ57uGvAmO8UETEIGIiUMAaMzIVGI/nPWvmmkopqwJC531NfUnxRscd+tZ+UC07nc+Sq4aeAkdSScfnvPeFdGE3/pba2toZXhRLu3V7PKN3HRvJmrcC7vOQ1+1mVUyTGYvaV0TjvEDAAAAAQYc7KCZfL3Re7useLkkaMgzJkaqrJf5J//spZM+VWp3XqjyRi3C3R4kkZL5Y1YqyfueTlRVPSkd/P3QJGwRGDgIFICWnAaJ92KDmzXBfXohowLKvhTHlMBzzet+4F9d+1798KjuLy8VPAkLsfbvUsnhmpzaZZO5VXhKL3JTN1ruI1Tg7oun16oY9T19PuYt67Fa/RURH+8w4BAwAAABH12sOja5xVE1bKkDAxwSlhxNjlrJn8kMSKj+9fOXHqXd8ck7rzm2lz4QJtoPt35cpTxQM3jq1u+sbomq33jR/qrJx6hkSN/5GA0SLjUIkjxmG5E+MrUd/XvQNGQRGDgIFoXSgKb8BwLzr+MS5zzpRhs0U2YIhB8pie9ck+3iHjLkuvPysqU6uo5pOAUSmx6mrvFu1O7bJiyXdpxDJVZH+nble7j+2nNG3w4CIea5VRLTFV4Rosciw0hv+8Q8AAAABARDkrx35R4sXhTMAoTcTYKeP2NxZNOvGhO9MFL6C96p4paWfttHdLxHhC4sXBEkaMFw+snTkr0vt602k5AkbeEYOAgUgJdcBwL3ga9tfkaQ4s8WaLcsDQZN73G30Wqt6Qi9+PyPRW71GwgHuk+CBgVMTjmceww6tziGXZ18vjqOLVoEZ2bQmli3c7hp7+vFZkoJJj5O1qpzJLfVoLeUwjYAAAACCSnObxSWfV+FdkOEcCRvERo81ZM+lxuZPinKYFk/v9hXb9ffLY1kz9rMSLbSWKGG2yoPdtjhPdXwp2BowznCIiBgEDkRLygOGOVxNm3QUl3myRDhjuVCuKp00pJFitkQuK34rF0idqWl2CI7x/vA4YsVjtcNOwl3n3mrLvLdNdXOjrPcm036d4P+9NJOxxxT9i25J/x1a1d4tosXCfdwgYAAAAiJgFC7RKZ+W467LxomMUHTH2Srz43psSREr5GN3Y8OZTU06WeLFIRlv/I8aMHQfWz5ge1X3ePWAUHDEIGIjWxSJ1AeNV00i1eHMRMr2kxNNuRDpguM9f7np42Ofhyg0sj8ViyWstq36SphV+pyS8DRh1dXUJOc5+4eGdPRstq24yrwKldAlWD6qeatD9u/14zBVxM/k/CtdnORyLNcwN93mHgAEAAICIaX1wWJ2Eii3dA0ZREWOvs3LyvzkL5w8s12Pd0zJtmMSLh0oQMdy7MG6M6j7vHTAKihgEjJxqa+Ti0icNPfUFRgmG0XCRX/aswoCxVS4UnCf/fNmDi5FtlpH6X610U0lFPWBoViz9TveXy0G4C0cuwr8px90DhpG+Rn5pPVbTGg2N9QzyvJDoWcAYYBn2592LtZ4tFG8kL+Z1olY8npyg/Lyi2x/s7+OW18qszPoUyi66139H/mxleM87BAwAAABEjASKD/SOFwVHjH1O68TPKXm8zbNkSqnpC/s/ndSMHc7auZGc9iATMDb1DBh5RwwCRs4vk7XD5YveSyGfakjluNkv+1ZdwLCfq62VEGYmr5X/fb8H23yvTCv07hJttsgHDDcCqNwGJRx73PUyzFjyk1VVQ+TODG2QxkXqo5z7vQkY8nff5q5t4tFrpE0ual8nD2MArwC1DD35JdV3arl3+pTkscdSTyp83Jvkwns6vOcdAgYAAAAipOmGYTFZvPuBvgNGXhGjzVk1+fsqH7ezShb4XjP9mX5GjENyJ8b7o7jfswHj9MMSMZwiIgYBI+eXSQIGAaP/d2C4AUPTRupyYfJuj7b7i7o+ZEQJNhsBw33t6OmzPbzIXJKpWGQskzszvi5xa57GBesc5371AcOdtkn+92c8e13oqQe1bNiCQolEoi5zPCq9Oyv9M61EAdOobvio/DsPqXrsVsz++/CedwgYAAAAiBBn6Zixzqpxu2Q4RUeMVRP/+lrz6Brlj7112mkSL97qV8RYN+P3TgR/WdoZME53iogYBIycXyYJGASM/gcMdyoy929WV6fGyMXK9R7Na/+Q1v+ppAgY7dtBLv7fFJJj8qCMjbJeyk1yQev89mmmOPcrDhiJxLA6+eejHr4ONllWg82e9+C9yKw/W+E6Eu44YBj2haV7/HaD4s9Jd7tvp+E87xAwAAAAECESLj6cjRcdo+CI8aqzcuoZnjx2WdhbwsUNMg73I2Lsfa15Vk3k9nu3gFFwxCBg5PwyScAgYJQuYIiKWMx+h6oLoz2nh5Hpgz6h9W/+cALGkXND3TBZdHdZyI5Pdw7+Z2TdlB/G4/WXy3LSieie+9UFjLiZ+oyl27d6tu6F3E1kWcn5vON7wo2hP1Ydq9w7Akv5JOQ1dIfCx/+qnJ8mhvO8Q8AAAABARCxcoA2U6aN+3T1gFBgxWifd7P57vHoOe1ZMH+qsmbb5SMAoKmLMvCRq+753wCgoYhAwcn6ZJGAQMEoaMFzVskivV7/ef71GFl3tz8U2AsYRFe2Ls+8I6bG6J3NnhpH6nqUnz4xazFB8B8ZyDxeG32fGGj6jsR6KJ3R98AjF04a1yWv7myV/PzXtC9x1NdRdfLf/LZznHQIGAABAcC/KNqc+4iy1l3SOdH5jWUOOMaSP0bjEWX6sMSzHOG6J09J1DO9jjMhzjOoxRneOlT3HmB5jbMdYKuNNGU6REePVNx4fP8fz/b5myvUSMdr6ETGelbFEQsYSWROjx5h97LHeHXNyjLk5xrzOsbFjnJTnODnHOCXHOLX72NQxTu8yTtvcPV4UFDEIGAQMAkaZFvHuETDkdVU/VH69/5RH++Axy0qnCBglMVDuqPlXDy8+K5ovP/WmTDG1JG4mP2OatVO1CKyToDJgeLkWityV9VNNGxbj3d6j9yHTfq/SO2/kbpv2dW9KyrZtS6alWq7wtftkTU1NLQGDgAEAAOCfgLHUvl6G032kcox077GsIccYkmM0ZsfyrmNojjEsxzjOcVZ0HcP7GCM6R0vHGJljjOoxRneOlV3HmBxjbO9RcMSYeFfTgslVXu/3l56YONVZO+1liRhO/+7EyNyNIetidB2zjjJmd4717piTY8zNMeZ1jg3uOLGPcVLvsfHkHOOUHuPU3mOTO07LMYqKGBIwTiFgEDAIGOW/A8NVIVO2nCn/t12eLOAsizcXeRGagNFbtVyE/HpmXvloHMPbZPxWLvC/LcxrJkQjYKSb3fc43uk9PHcY9u8V7/cnNa08d1jL+8rnFa7lscfS688iYBAwAAAA/BUwmnsGDCJG2SLGynHv98N+X7hw/kBZ0PvP2YBBxFAQMQgYBAwChrqAkYkBhp76ouLFW4/8oj5upN1p9gqdNoaAkdNIPR5Pfqt9QeyoHMuHZCyV1/AXampSY8p1UZSAUbbxAuteeEvuWJgh++EVpXfcmMlry/aeWpWaJn/jRcWfJyrCdd4hYAAAAAQ4YNRnAwYRQ0XE2NnSNGqCb/b92umf6wwYRIwyRwwCBgGDgKFoCqlOaVP+O3/zYl/IL39X6XrNKAJGyQy0rPqvta8dEbXjeruhp281jCHue0hVOM79oQ4Y+ywr9RF5mpW8y3sYMPT0darvnopXlXXx64HyN+5R+Hw2xmJ1wwgYBAwAAAAfBYykQ8RQEjGeevC2YXV+2fcvPzbpHAkX+4kYSiIGAYOAQcBQewdGhmXVTvFoKimZD92+TdPGVhMwSnhR0kheE+KFvY85rYu8Pn5mGI0zg35xPNQBI3Pcs+6Fl2rlPcGdwkvxvr+7/MeNfWX73VlK7gIzzfQ/EDAIGAAAAD4LGEQMBRHj7oUL/DMNxKp7JkyTaPFi94BBxChTxCBgEDAIGB4EjEzEMFIf8WgNhX0yb/k1BIxSR4z6OXKHy9IIH+f75HXyy5qa9GgChu/Gk5aVTvHu7q2EWXe+4tfXQTculP/cl2yUQLZG2fPSUw8SMAgYAAAAPgwYRIwyR4zb/LTvW+8bPdxZM3WTDIeIUfaIQcAgYBAwlE8hdUSVXPT5nVeLMptm7TQCRokvUCYSdXEzsy7GWxE91t21XXaYsYbPFniXDwGjvGOlLDrfwLu7t+R8/yO16x6l10pcGKLo2Pmmwuf2ijkoNZ2AQcAAAADwYcAgYpQxYnzdT/t+7V0T6yVerMoGDJ9EjPWhjRgEDAIGAcOjOzBc1dX2ODOW2uDFhWa5W+CPmtZoEDBKbpBcNLxEtm9zhI/5g/Ir6QdkkfPxWoAW3A1xwJDjPf3jPI93lOW1VT9U9sMWpWsexexvqzr+Eon6OQqnRWzT9fTnCRgEDAAAAJ8EjHqHiKEgYqwe/w1/BwwiRhkjBgGDgEHA8DBgZF9v6Xd6tB7GQctIfomAUabXmvzi3bLsr8qc969F9tg3UpusWOrv3NcQAcPzsd+K2f+iBSgohYmh2x9QPGXgbstKzlf3DJNxOd7/qHAaqVVhWdOFgAEAABCKgEHEKH/EGH+nn/b9yvvHj5Zosbl7wCBilCliEDAIGAQMjwOGqJZf7N/cPv2O6n2zU9cbziBglM0A2bezZfvdJ89tb0SP/51yYfPLyWQyTsDwfLxo6ckzeJdXrlKOgYcV7+vHBwuVT1LWVrpa4fN7Sy7Gn0/AIGAAAAD4KGAQMcocMX6/8PaRum8Cxt1jZjmtU7YfO2AQMUoQMQgYBAwChvcBQ15zdcPk/2exR/un5RgXuggY/TZSl4vjb5M58B9zfwkfxSmlZEqtO9zXOQHD62E/JfvhON7p1bGsuskSMN5Qup89mGKp/bPTC8ouxsuaIgQMAgYAAIDPAgYRo4wRo+WhH6RTftn3by6eeIkEjIMyHCJG2SMGAYOAQcDwbhHv7he59Pqz3AWQvdhHclHjBnkIgwgY5VVTU1Mbj7tTydhPyXPdE7UFviUQPFJVVXc8AcPb/RA37J/I063m3V4NXU99UfEddq9bVekpHjzVgTJV1m0Kn+f2mprhtQQMAgYAAICHAaM2R8AgYpQpYuzd+qdR03yz71dN+XomXnQMIkY5IwYBg4BBwPBJwMhe/Mlc6Drsxfz48Xj9FVru+fEJGCWXuSPj78zstDK7PZo+zKt1MVrlItr0qAcM2fet8s99Hu2HfayHocwAM5ZarzRIG/a9bjfx4slaVibEqzqfHTJ1+70EDAIGAACAxwGjziFiKIoYK8d+wg/7feMDY6ud1snN3QIGEaOcEYOAQcAgYPhgCqlOtTWyqPcjHu2np+tyTy1DwCifSneNDJla6sbMWhHexCv1d/zEUhusqobj/XfuVxcw4mbqs7IdfuHt2iT1c3jHL/cF/eR890K74jvqrvLuGduWhPjVCoPoQ+55lIBBwAAAAPAmYCzpCBhEDEURY+HCJtvyer+/2Tz+VAkYO3sFjJwRg4W9SxAxCBgEDAKGrwKGpsXjyQlGLP2aR/vqN5qWNgkYXkibpmm/X35R/GBEFvzeEq9OTohqwJB48Sm5mJiW/3mZV/tAnu861Qs9R0ylaSZvU7xft7mvKw+fc4VMUfYfKtfXqalJjyZgEDAAAAC8CxhLOgJGwRFjl/zzNmeZ/d3sSOc5huQ5GrNjxbHGsDzG8D7GyM7RcrQxqn2MvFn+ubUfEWPPwRVjz/N8v7ce/2MJGE7nKDBitE77s8SK7+Y/ZuQe67qOWUcZc/IY844+NrjjxPzHppNzjFPzHKd3HxtP/8aOp05nMU8CBgHDH1NIHbkAJBey3+vRvjoQy04tQ8DwzkBdT4+Wi8ufku3xuLtPQnxuWOanC+iqA4b7NxOJ+jnuRWev9oFlpn4hD2MAh1051Na031mlcPHu1C3uOcTb46jxOKXTo+mpL2gBng6NgAEAABCGgFFcxNi6/cHkkEhtL/ng7qwc+aN+3onxf1ubhsW8eg77l06ZJtHi1e4Bo8CI0TrtNI4e9P/LJAGDgOHdHRjtqsyY/SuP1kZ4QaaxOrHLYyFgeKPCnY7FNNNTDT35JQlLf25fLyNU54e4ad+h+WRBaS8ChrufDSN9tYd33RySaXj+WQv4NDx+5K7PoHp/6nrDmX44dxl6+vcKn3eLe64M7mdOAgYAAEBgdQsYBUeMZOQCRmabLR91ZT+nk3rLWTHqPZ489uZZgyRO3C3Boq13wMg7YrzqrqHB0YP+f5kkYBAwPA8Y8jqsc3/FutmjNQoeictcVu0PhYDhC8m4rBsxWeLStbJI7x9kWz0fksW/D1iW/U7NB7+g9ihgiGExWQflex6ugfJKgvUwSmykLnfi3av47ovV7nnCD89eFol/hzu9k6LnftjSG+YH9zMnAQMAACCwsgGj1ikyYkQyYOxYOHSYs3LUtv5FjDHr9i0fPU75/l4z6WqZPmp/7niRb8SYcocT4FvI4acvkwQMAob3ASP7Wky93aNf3R+UC6pf1bJTyxAwfHiaqq5OjJXXx9/JfnLn2F8Z5LszTD25OharH+r9ud+rgCHnJtNukL//iIf74XFZUL6RQ6s0pP+Ol226S3HAWKD55HNwXV1doj2yKrowb/9I/uygYH7mJGAAAAAEVmfAKCpiRDJgNC3QqmQtjJ/3c2HvNqdlzAPr7lHzC67M1Fetk+dKvNghw8mOPANG94hxwGmdejlHDkrzZVJpwHjFiDX8LtTDSF/jl30bkDUwuhooC3rf5M0vs2UhccO+UCNg+F2FZaVTMsXUqXKsfV224ZPti8AH6u6MWKzhM5rHF1+9DBiumprkTDd+erYfDPv77t0gHFL9PyZlCsDPqZ4KTO7QOt5PG0GiwrcVnoeed+9aDOZnTgIGAABAYHUPGAVHjEgGjMx2axl1mYzD/YwYh2T8uPm+RqOsj9WNF6smTHNWH79ehtMZMIqKGJu3Nc8yOHJQmi+T6gKGXBj+M1tc4QWVgN2B4ZJfRQ+RKYOe8uaCZqrVvShEwAiUarmbYV5mqqnsftvu4dREBQSM9Fr33Ovtud/bgCEq5W6Uq5QugNx97JUI9iGN9TD6qaZWtuUixfvuCf99lsocT7vU3cllvz+YnzkJGAAAAIHlLKnpETAKihiRDRgP3Zk25S6Mlu4Bo6iIcdiNGE7z6JpyPM4FC7RKiRezndZJ65zVk5xMwCg+Yhx2Vk//AkcNSvdlkoARVgG8AyP7uPX0OfLvfdWDC5qH3UWW5Z+PETACaUBVdt2MT8i2Xdw+zZRv78yIVac/7u253/OAkdln8ji+5eF+2pZI1M/j0CleXB9ymru2i9qF2Bs+6sfzj9KQo6ce0LS0GbzPnAQMAACAwMoGjMFOkREjsgEjs+1aRnxQIoZTgogh/91xjzqtY8aW9PEtnD/QWXn8Vc7qiS9n40XHKDJirJ76wp5F04Zx1KB0XyYJGGEVxDswOsjdEF/y6KJmm/ztNwgYgVcpF6+myTRBX5PtvMWnIWNRqY+bws79vggYWnYB6NQTHu6HJy2rweaQKe44k7vmblZ791LqTfdOPT9uDHnv+GgmsKi5Y/AN0xw8PXifOQkYAAAAgdUZMIqKGNEOGM2zBjkrRmwsWcRYOfZ1GZ9oeaj/v2qSxbrHyV0XTRIv5K6JiU73gFFUxGiTgPFljhiU9sskASOsghwwZFnUhKE3/CHsi77zKlVhbLVMFXSZbO97PJyuKNd4S9fTZ3t37vdLwNA0XR8ywsv1MGTauh/LwxjAsVLU54dNSveVmf61X7eHHM+jlL6ODfurwXvNEDAAAAACq3vAKDhiRDpgZLbfilH/KHdiHCxhxHDXxVh9eMW4f9791MiGQh/PgeaxM53WCd91Vk94XcJFWzZedIz+RIzJG/Y8NeU4jhiU4QIEASOEgh0w5PFXyS/ovVzkl4ARNoOqqoZMci9qybZ/2R9rYaS+79253z8BwxU3kpe2T/vl0XoYqY9yiBRG11NXKb67aa+8bt/m401SYRqp2xVuj2VxLZ4M1mdOAgYAAEBg9Q4YBUWMyAeM5lvkLoyWkQ9mA0bJIoY7Djgrx21zVo3/tbN6/IdeeGTMydseHj38pfa7MxxZ2+L5P02s3/bwiEm7F48931k19t/lv/ukrHexU4aEiwlO93jRr4ixz1k15QMcLSj9l0kCRlgFPWCISrmo+SHF86sTMMJvQE1NzWjTtL/hecgw7DXxeLzem3O/vwKGu18kInzZw+N9h0xNNJvDI29VRix9v+J9tEmO3Vo/b5SEYV+k6rjKhjf7omB95iRgAAAABFbugJF3xIh8wHAdaB4xU+LFi2WIGI6zapw7Dst4XcZzMjY4K8evlljRKuNpGS9KsHhL/ulkx4TOUaqIsXry71oemmZytKD0XyYJGGEVgoChpWWRUrmoeQcBA2XgLvp9vBmzf5m5EOjNHRhvWlb9Wd6c+30XMLRkMhmXC7K/83AqqWWJRKKOQ+PYZD/NUB0Ag3BBerCQsNOsMIL+RP5sZXA+cxIwAAAAAqvvgJFXxCBgdGzHFSM/IfFibxkjRo8x/iijpBFj/e4nJk5lD6M8XyYJGGGlLmDYz5VzMeJYrH6oXADdQMBAeYzUTbP+n2R/POvFa8CSuw68Off7L2C44tXJCYaeWuXdXTGp/5WHUcVxcXRy4fzzSqePkkWrLT15RoC2jarX7JbqantscD5zEjAAAAAC6+gB45gRg4DRrvm+RkPCxR0yDocoYrwh40rH0SrYwyjPl0kCRliF4Q6MdhWyCLM7P/5+AgbKdy60T5WL+uvU34VhL/Tm+fozYLjap+F5xaPj8qBpJq/SAvSrdvXkTplYeoni/bJY0xqNQLz3mqnpCl+/hySCfiQ451kCBgAAQGBlAsZTNU6REYOA0cXm349skDsxFkq8aAtBxDjgrJn0GXeND/YsyvdlkoARVmG5A6NdhWXU3yh/7zABA+U7H2Yu6m9S/DrYGo8PrffoufoyYIiB8v/zaTcmeHRsPu9OkcQR0cd7S3XqXNlGuxTujza5GP3pAG2iannMdym7UB9LPeoeM8E4xxIwAAAAAutIwCguYhAweti3ZNQECRXNnQEjgBGjdfIhiRj/s/D2kTp7FOX9MknACO1FpvDcgZFRW1tbI9OIrCBg+FYo7hSU19gVii/Ovm4Y9XPUn/t9HTCEbclC67/ybD0M0/6LZVk27yS9WXrqh4r3x7aqqtopwfpsZb9D3RRb6ddkqsV5wdguBAwAAIDA6hYwCo8YBIxc23TpmLESLlr6Dhi+jxjfXrhw/kD2JMr/ZZKAEVZhCxiZC2eWfbo7FzoBw1/cC/CyjsR7QvJ0BsmUZT9S+DrYLzHh7erP/X4PGO5jrBsm62Gs9ixixFI3aKyH0f19xUyl1a8XY9+nBeQOg85zYrIxFkuvVbaWjmV/LRifOQkYAAAAgeUsSXQPGMcMGd0iBgGjr+26bNQIiRfLAxUxWicdcuMFew/qvkwSMEJ7oSmEASP7vOx/83BqGQJGt/NH43BLb/hB+/ok2xOJYXVhOHasqvSUzJ0RqtbBqE7/q/p95/+A4ZILwbPk37Hbo2N0Xzxuf4B3ky7nX73hfbJdDijcB4ctq+6dAdxUlXIB/QaF2+mZIKwRQsAAAAAIsGzASDhFRgwJGAYBo69t+1RmTYw/BCRi7Jb//DPceQG1XyYJGKG90BTSgJFZQFZPPUDA8E6dVpeQhY6vlefwQpdpUg4bRsPHQnL4uHPY/0bZhTY9/XX15/5gBAxRYVSnPqxuOp7e5zeZnmcu7yhZqs+98trZ4H5OCeR7sGmfoDD27JM7xy73/2dOAgYAAEBgdQaMoiIGAeMYWptsy2kZ8d8SMPb5N2JMWu+snHTFwoUa8QKKv0wSMMIqvAEjM43JdLmwtYWAodwA9yKZPPZFuS7MuVOmaNrY6jAcPzJV2YeVvRaM1E/Un/sDEzBcHdN6HfZoPYxH3amTov6eUl1tj5PtsVPp9pf1NuRPVwZzi6VNM5b8k7rYY//S/585CRgAAACB1T1gFBwxCBh5aL5l1iCJGG93Vo5slYjR5qOIsc9pnfDrN5vHS8UIxwKoCBYCRniFOWBo7q+yDfuD8rf3EjDUbG+zKj1VXlO/PtZ0Prqe+ictBAt6x2INcxVeIP+V+ucXqIChxePxenn9NXt0rB4yYsmbtYivhyEXyD+nOCId0PWGM4K8zWQKtGsUhtA3LKvB9vd5lYABAAAQWL0DRqKAgDGYgFGAfUuGj3ZWjPqeRIzXPI4Ybc7K8audlePev+6eCXH2DLz7MknACO/FplAHDC3z61bDvoOAUVYVMn3OUPkV9Bfk8W7Laz0HuQsjHh9aH/xzozxvRRdrCRj5Pua0e/Fzq0fH625ZD+ODEX5LGSjboEXpNjfSS9xwFej34eyi59sVbbO2WMz+V3+fVwkYAAAAgZU7YOQdMQgYBWq+RRu0a9GIMyVi3CvR4k0PIsZWp2XC9a8/LouMc9cFPP8yScAIq/AHDHdKk9QYubi+ioBRDsm4FUu+SyLR0oIv5Ovpz8u/YECQjx9Ls1LyXF4hYPgnYLjciKDqcecY2+TOrxnR/KyQPjF7J4rKgJH8ohb8u7kGSoj5scLt9qT7N/37OiJgAAAABFY2YMSdIiMGAaNITU2Tq3Y/OfwciRa/kvGqjLYyRoz9Mp52Vk74ys7Hx44hXPTzmJHt96P/mhB/ecmc0c6GE//OWT/vc87GOV931p94m/zv1zvrTrrW2TjvvK2PTB/adMOwGFvsaF8mCRhhFYWA4Wpfk+EtAkbJDEok6udaRuoeeYz7i3xuOwyjfk6Qjx/DMIbI89hDwPBXwBBV8u/8vvKL6Z1jUV1dXSJibyeVcp69SfF23hOvqp8Yho1nWckzFS5Cf8id7s+/nzkJGAAAAIHVGTCKihgEjP5uf7kY/sZfh41zVoz+nMSLJTJ2yzhUgogh0WLcq87q8U2HWsa/a/PCkYPZ2v3cV/JLPGft3Hpn4+xPOBvmPOqsm/uWxAvnyNjgjhO7jldl3OtsPOUdr7TOt9iCub5MEjDCKioBQwySC8DfIWD0W2a6KJnn/ttycfvNEszH/lAikagL6vFTXd04gTUwfBkw3PUwkvLvfMSr47b9wujA6Lyb2JaqzwldFu/+g/syDckGdKff2qhq28ndMt/QfLrwOQEDAAAgwJzFXQNGwRGDgFHq/dEyaoKMq2X8rwQLWfR79L4CIsZrMv7orB77nxIvLnFaJ9exRUu0XxZolc76OR9y1s99XobTfRw1YrijTUazs+GUc9mSPb9MEjDCSl3AsJ/zOGBoyWQyLo9lBQGjaLIoeurD8pheLOkxb9hf9+uFtGOR7XGFuqly7FvVn/uDGzAy5zczNd0993i1qLdsv7dF53OC/ffKt7GRvjpM27B9HaE2RcfbhsHCn68lAgYAAEBgOYut653FXQNGnhEjGzIIGGXW1KQNcJpl2qeVo2W6qTHvklhxzZHRMvZquXPjEvnniU7ryAa2VpmOkWfmpZ0Ns++VgOFkx1yniIjhjoNyN8ZNzub5Olu148skASOsohQwXJZVO0Uez14CRkEqdT19lhFLN5fpOe6TuwveF8DDZ4Cu27cqu9Cmp/9T/bk/2AEje8zbV3p4zL9oWekpEXgrqZZf9P+f4m27Ix5vTIZpIw4e3DDSXQhe4fohl/rzMycBAwAAILCyAcNyiowYBAyE2v41s6fIdFGrOuNFvyPGYRm/d1aeWsvWJWCEWYSmkOp8zmbq0x7OjR+ogGFZdcfLheWfu5GhzM9zu1Htz4tpfZGLf43ur5hVvQ4sq/5j6s/9wQ8YYpDcKbPAs6mkYum/JLREqO+0ra5OjVF64T2zXZN3yp8eELZtKaH4fnVRNPUL+ZNV/vvMScAAAAAIrM6AUVTEIGAgtPa1zhwr8aLZWT/b6R0w+hUx3Cmlfrtj47xE1LcxASO8onYHRrtqd+50hQumBi5gWPKzdctocC/6vqBuO9nPmdWpoEzhVyEXUK9V+Bo6IFP0XKn+3B+KgKHVyrlHjvkHPDqGD8t2/JZ73gnv+4j9b4q3aZvctXVBKLelnrzKPd4VbceX3Pjkv8+cBAwAAIDA6h4wCo4YBAyE0sYHxiac9bN+7ayb3ZYNGCWPGAedTad8palpclWUtzMBI7yieAdG5nlXpafKY3qWgJFj25j2CbIuhTtd1EEPnu82uaD0j5rP18SwrIbJ8qv+jQq3y85YrO4k9ef+cAQMV/tdAl4d87t1PfO6Dp0araZWnt8TirenvJ9oofxclkgk6uT5bVY3jVTDx/y2DQgYAAAAAdY7YBQUMQgYCOdxsX7mxyRe7JPhZEZ5IsZOZ/1JZ0d5OxMwwiuqAcMVj9sflMe1h4DRc7skJ6g63vu6WC+L8/6HpqVNPx4z8Xi8XgLPfarDjkxZNUT9uT88AUNz75qRX+2rnuqoy3hG9uHMsL2H6PqQ0xRMMdft7ov2O1rC+75s2t9Rt5h3+i+1PntvJmAAAAAEWO6AkXfEIGAgdLY9PHm4s27WehnOkYBRtohx0kPNTbNqorqtCRghvlAS4YChaSN1uRD9Y79PJeXBhqmUuwt+5/HzPmya6SZpBRM1H92NUVNTUxs3k3cqX0PFSDd7sR1CFjAyLCv5Fa/WwJHt+ad4fGh9iN5CKuV5fU/xdnzLMOrnhPl9WWbwO12e5+uKtueueLz2VH995iRgAAAABFbfASOfiBEnYCB8x8T6WZ+UeHE4GzDKHjH2OmvnnRfVbU3ACK9oB4zMBelRMjf+KgJGdxIwrvDJ3SnPxmLpj7t3PXh/HqwbZhj2bzy6+H2zN885fAFD0xoN+Xv3eBQuD8l6Ed+WBzEwHJ8N6o6T18g6tTEvtUgL4eLd3Y3U5bn+Td2C6Kkb5I9W+Od1RcAAAAAIrKMHjGNFDAIGQnY8NM8a5GyY1dwZLxREjPUn3ek4/vmCp/bLJAEjrKIeMFzy6/bLPJxWxpcBQ9OScfnbj/tlG7hrcsiFpvdoWl1CU3+hbaCsn3CehK7VHl30PhiLJd/tzbk/jAFD0wbrg0dKSFjn0et5j0S5d4bjs0HyXe7dUooXRP9kFN6b3bUplJ1vjFSrF1PU9f26ImAAAAAEVjZgmE5xEYOAgXDZvmjqLAkWr/UOGGWNGGtafz+5IYrbW2XAkLFcpo45O8zDXWPAL/uWgJExIFbd8E0CRndx036f4ouT+VzkXi8X276s6+nRCjZBpVwsPUUucP3C4+f9fFVVZiotD8794QwYmXOfmT7HiKVf82ifvlJdbY8N+EeDQe13sqjebuOi8LnLfZ4KF50/aMVSb/fPZ04CBgAAQGB1BoxiIgYBA+Gye9mMf3TWzTycO2CULWK8ser/ppwcxe2tOGA47b86DPO42S/7loCR5a5tINMDPUbA6CTTNiXlzoenfBp2JKykl0jI+Lyl15/VfmdGCQyL6bp9umUkv+jGVD+sj2IayYc1j6bMCXPAyJz/zOS18rf3e7Nv7afc11tQPxdYVbVT5Hm8qHi73RWtz16pn6ratnLOu1vzyXpDBAwAAIAA6x4wCo0YBAyE7HhYM+urEjCc7FAXMQ6tm/f3UdzeHgSMsA8Chg+5C8PK43yVgNHl9SHTNrm/zvXxseQGhr0ydshYbOn2rXGz/rOmnrzKqE5eKtOizM496ueYpn2BOwVOrDr9cYkhN8oF9Efl37G9/d/X5ptQoyev8fACaqgDhqalTbmj5ydevXZlKqlvaAFdD0Piz6cUHyf7ZXu9W4sQmbruXIWB7QXLqpvsj8+cBAwAAIDgXrBdbPQIGAVFDAIGwnU8rJ15e2fAUBgxNs77WBS3NwGDgFGCXxs/5/eAIQa0/yKbgHFEbY1c4H0ogMfYwfYQ8XofY2f7IuUHfP48nvVyqqHwB4wj72+LPdq/++Kx1Ns0LWjra2UWQle6zbLTxyUj9V1G7oKrN9VtZ9+sL0LAAAAACPIF20zAMJwiIwYBA+E6HtbO/KUMR3nE2HTSv0RxexMwCBhRuAOj88Jc+n4CRpfXiO6uFZDaxXGrflh66geah9O6RCFgdHmeL3u0rssz8sv344P0mcCykmdK2HxD8bb6fgQ/flUYRvrzCrfxYk0bW+39Z04CBgAAQHAv2B4JGIVEjHhHxCBgIFzHw7oZtzprZzjKI8bGeR+N4vYmYBAwInIHRoZM2zHGg7ndfRswRJVl2Ddx3Cofr8didSd6e+6PRsAQA+TX/dd4dkeOnnrA/bV9UD4TyHpBNyreRrslpJ4dxc9fVlV6iqrF5uV4f9PSk2d4/5mTgAEAABDcC7ZuwFjUETAKjhgEDITreFh7woJswFAaMdr2r5r7tihubwIGASM6d2AcuUD3AXd6FwJGx/ZINmamcOHYVTeM1B2y6au8PfdHJmC4JNSlfujVWieWZV8vj2GQ38+N8XhjUh7vM4q3zyJNGzw4mp94h8XkOLxb3XnHvsn7z5wEDAAAgOBesO0IGMVFDAIGQmXX0unvkHhxQHHEeH3FfdPnRnF7EzAIGFELGJq7HoZcQM4sokzAyJCIcYnCBWUjPuQX10Zylvfn/kgFDLlEPniwbPtmL/Z55tfvMfvvNZ+vh+EufK/6PGDq9r9H+TNvLGZfqXDB9Oc1rS7h7fMlYAAAAARWt4BReMQgYCBUXn5s8nQJF692BgwFEWPD3FXLHphhR/PLMwGDgBG5gCGv+/qhppFcTcA4okIusv63H6JO2IdczP8v2d4DvT8GohUwXJZVO6V9oXeP1sOon+Tn86I8zv9TvF12yEXm6Z59//JBUJLpxdy7XjYr2t4HJFK929vzDgEDAAAgsLIBI+YUGTEIGAjX8dB05QBnzYwnuweMskeM26K6vd3pYxR+eSZgKETAOOZr/xL3l9EEjA4jdbnI+leO4bJOHdXql/UQohgwMudFM3WVZ1PIGamHbc22/Hg+dH/MoP58aN/nTqPkxfPd+ZhWu3dJ5Zd2LdXe2dSkDfBw01fKdr9B3Wswfb+3rzMCBgAAQHAv2B4JGEVFDAIGwndMrJnxQYkWhxRFjN0yTovy9jarU+fKl71XucBIwIhSwBAVcjHnei/vOvDbBnHvTJFFh1dzHJdlHLKs5Hz/7OtoBgz3uLfM+u96dNwfjptJX15UlYu9n1W9TeRv/pPq57nwdk1/s1m7WuLFVhmOjLY9Swb85c2l2mmO480dGaZZO9U9Pyja7gfd87x35x0CBgAAQHAv1nYLGAVHDAIGQueZP01Jy2LeK3oHjHJEjHm/a7lzmhnxTV7RHjF2cJGRgBGhgJFZtFbmIF9IwOh6ganuRPll9HMcyyUdbZbR8GXZvJX+2c+RDRiZ9TDk+f/Jo9fCbjnnvMNvx72Ey1WKt8MWOdccp+r5LVyoDXzjiYEX7F0y4HGJFgfa40XXsWtvc+Xtr/xVG+fB5q+WY+RRVeciee1/RvNo+iwCBgAAQIA5i/UeAaOgiEHAQDiPi3XT3yMR460yR4ztzvqT5rK1Myrky9758qVvOxcbCRhRCRiueLzuJK/WgfHvuaD+bHl82zieSxUvUvd4NVVO3xcSoxswMudHs3aavOdt8Og1sS2RsMf6ZVsYRsMc1XdfWIZ9q6ZoLZjXF2vT31pSeUcmUvQOF11HW/bODO0/Nj2kpdTug9SHFS7mvVz+ZLU35x0CBgAAQHAv1OYMGHlHDAIGQumBG8dWO+tO+L5EjLYyRYwDzsbZn1iwYP5AtnbXL9HpS+SL34tcdCRgFDCP+XNBDhhigBWz/5+7wCkB44hK91fi8hhf4ZjuX7yQsdjLKVv6vpAY7YChuVNJxex3yuPb6cVrQ46vP2tao+GHY12C5XdUT6Uld32eV+4ntv4+bejepwZ+Ve6seL49Tjh5jkN7mitXyNRS736iSVMSHt27ASVivKFqOjs5/k/y5rxDwAAAAAisbMDQnSIjBgEDobV7kUwltWbG78oQMQ5LwLjlpZbITx2Vk3yJvkK+/L3AxUcCRhTuwHDV1dUl5HV/FwGjmwESNC+T7bKZ47rYkW6Ox5MT/LhzCRgZVfL6XqBw/YEedyEkv6ApuguhL+50WsqnjJN1dty/XK7ntPEBLbF7qXbV3qcqVxYYLnqOvXuWVNy750ltXvMt2iAFx+Sd6hbztm9yz/HqzzsEDAAAgMDqDBhFRQwCBkItGzFOuEsixuESRYyDzobZP9r+twlxtm7fiBgEjCgFDFd1dXKCPJdnCRjdVAyWhaflsT7NsV3wnRdLDSM50687loDRIXMB/16v1sOQC8kXaR6tR+CyYqm3K3/ehv1VrQzrwTQ1aVV7m7VT9y6p+H0f61wUNfYtqXxt99LKG559SBu1YEH51rEx/397dwIlR1Xvcfx2JySzZZ8lhJBA2ElAVMQNH+IKqMBj8URFRTZFRAR9En0+jMoTXEB5Lk9QFAVEg2wC0TyBCJmZbBMIBCRsIsgqW0Jgwt7vf6trMj2VulW3qququ6u/n3Pu8WC6q6pvV1Un/1/de8d27Sf980JWa5BIgb8n+/sOAQYAAEDDGhlgRA4xCDCQe+v7Z08u3b7HjyXEGKwuxHjT06W73jivtODwUfRqODfEYDopAoymCDBEobW1Z658npcJMEbq6Jg6W0YTrMxwjvYGDy+6VowbN2Xnev5OCTCGjSuHl/fU4nyRvrm7huthjJVjuDTjz/zKmDFb7pLkh9ChwgPXqVmyzsWPJXB4NqngwqfdK2HG5/75ZzU5pe9jjJwPd2U2Aqi1c2729x0CDAAAgIZVWuoNMCKFGA0fYCw4XI06+2TVeu581ebfptESaL+av02LquFTflVfJyVVkIW9v1bFdFLPle7c81DuONHIFDIfYh58Aoycr4FRadS49s4LCTB8irzjtpoioeZlWQY8DdhebW/ruqalZeLMev8+CTBG6uiY8q4Mn373tisn1eAeKrNHbZPhugtDrVd2ndh0TIsXq9GDK4snS7jwSIrBRWV7deOKUUs3rhz9zlTOw7aur2W4oPqVssuWbO87BBgAAAANywkwlnoDDNsQo/EDjEcXTdq/NNA1WFrVVdq8dfu0ns3bzVM9bUtDm1Zut1S2rXzadE/butxWV7YZhjZzuN061LbxtG192qzhdltl286nbT+yrdnBp+3oaTsNLLl4xqSGvlbu3OPI8lRSe5RihBjPrFv++j2540Qn0z3sL/8YfJLiJAFGzkdgDBX1Jmb1NHYDds/o9tbOU6T4/SzXus/CuGO7v18nCzNbFBIJMDyK7a1dp9bs/GnrPE2p9KYnMpwDp2Q9OkmCouOT/Az3Xq669ciIjMKL4TZQPC+V3+32nt0y+/uWhFdZr9FDgAEAANDIRdmhACNeiJGTAKNzUEKMktPyFmKsrpcQI28BRuQQgwAjvkJ7S897CDEIMJohwCgXkaa8O4vibqPeD9rapr1BitI3ZvikcL23Rzo6uvUIv9GN8iUSYPga1dHReUUtpkrToaC0f8/uo04eL78Rf834c25Iet2FvAUYzv21teea7BZU7/lqtvcdAgwAAIDGLco6AcbYUswQI0cBRmeJECPNECOPAUakEIMAo8p/VBNiEGA0wRRSQ4qyHsaZaU+X1NhdNGlCe+vUU5t8irmX29u7Fkxo6dlWNdgUjQQY/jo6erqlb9bW6Hy6J6sn4uUzvlX2N5jx57s06c+RwwBDvpuej0pfvZjRd7JsvBo/Obv7DgEGAABA4xZlNwUYsUKMnAUYhBjphRh5CDB28wkwrEMMAozqFaRYtx9rYhBgNEGAoSZMmDBJQru/EmAEGtXePml3KYZeWINiaG0X6m7pXtPe0v2JRpkyyqeATYBhIKMw9pXjfqoW55WsoXKtHh2R/m9591kZf7YXW1u7Dk/6g+QxwNCjVDJczPuFjpbOfbO77xBgAAAANG5RdkSAETnEyGGAQYiRUoiRkwDjdT4BhlWIQYCREHdh70cIKwgw8jqF1JBxrZN1kfdhAoww27S0j+1+r5xzVzdBkPFAR1v3N1pbJ2/dyN8YAUaggqyHcZIc+8ZajOqRdadOVymuhyHrbUxrb+m8PdvP1fXg5MnJBzN5DDDKv99dP8jqu5H72c9URiPICDAAAAAauSi7WYARKcTISYAxxRNgdBoCDBb2jh9i5CnAeF0pRohBgJFoAaTnoDQLuwQYSRRACDCSKWT2nJTWdB75667prW1jp+7fXp7D/Zlcjbho7b5fpnY5Y8yYKTurjBdbTqeQSIARdi7LaIgLarHOi14Po61tykEpfveHpj093ubnQM85aXyWvAYYMlplb+m3dRldn3e3tk6akc19hwADAACgcYuyS8f4BBjWIUaOAowpJUKMNEOMvAUYkUMMAoyEtbV1H0yIQYCR8wBDdHVIXy4gwIhkzLhxk/aWJ3vP1YX/DOdzT7oNyn3ujvb2zi+NHdu9nWqwdS6CC4kEGGFaWibOlONfXquRPnr/KXys0XotiqwDmZaWzn3S+I7yGmAo1dMu18312a3lo6fDy+K+Q4ABAADQuEVZJ8AYU4oZYuQswCDESC/EyEmA8bfKACNCiEGAkQp3JMZDboGS1trzo3r5bqT4sSqLz9zR2n1fvgMMPSf+lF3cOckT7bvmuEfIdDXtUz4pU8hc7a4r8HKdhxYvSHtUnr7/9bi2zgN1gJXH78VdxPmZLO4REgB+oVH7qaWl6x16+qOa/J60dV0mhzAmyc8zdmznTrLtv2f8u7hS7zqN7ye/AYb87rR1H5/hd3SNymBk2RZbjN8rq88k97jv8rd0AACAJIuymwKMWCFGDgMMQox0QowcBRh/qwwwLEOMta8nwEivQPmGjtauw2hdh+m+qJfvRa9JkNFn/oCuSzTDed6acN812a2ioNeNkIWvj5Aw6GJ3HZ2XajFFj6e96h7HfR0d3T+Tote/d3T0dOf9yxg3btwU57NmcI8YN7Zzx0buKx32tNboNyXpdSPkPrZlnn4X8xxgTFATJsm5d0g231HX/iqlkKnSeDV+clbnXfsW3a/jb+gAAABJFmVHBBiRQ4ycBhiEGMmHGNvnK8CIGmIQYABAPRnV0TF1dltL11F66jVpS6RtyCi02KCfCu9o6/xpe3vXp8aO7dpB5WBdC6DZ5DnAAAAAAOrK5gFGpBAjJwHGZJ8AwxBiDOQwxFidSYiRkwBj9+EAI0qIQYABAPVKry0xWi+crNeakLnL3y8LZn9BRmqcJdMPXdLW0r1QQoelerFZveaK257zhBKv6Kmf3D97QL9enl6+vr2163d6AWG9DoOe8q6jY/Ku09S0NlUeNVSg64HGRYABAAAAZKQcYGxRihli5CjAmFwixEg1xMhRgLF7KXKIQYABAI1IBw0telHb8lorEyaV27jODpnzqbLJBCWTh/9cv161qiaY3gxoVgQYAAAAQEaGA4xYIUY+AoyVQwEGIUaKIUbOAoyIIQYBBgAAQG4QYAAAAAAZKfVVBhiRQ4z8BBgrhwIMQoyUQowcBhgRQoy1e0iAMYcAAwAAIAcIMAAAAICMOAFGf2WAESnEyFeAESfEWNX17Oat26f1+LSpnraloU0rt1sq21Y+bbqnzSi31TZt5nC7daht42nbbqwixMhJgLGbJ8CwDDEIMAAAAHKDAAMAAADISKlvtE+AYR1i5CDAGC8BxqThACNaiPHaMzd07nPP5RN9Wqdd+6Ntm5pgmx65/XPh9H1Kt21zRhUjMXIcYFiEGAQYAAAAuUGAAQAAAGSkHGCMLsUMMXIUYEwqxQgxXmuqc+W2bY6sYjqpHAUYu5XKLUKIQYABAACQG7ddoya9sLJ4i4QKr2UYYLz8wkDxe/Q+AAAAmspwgBErxMhZgBE5xGjSACNWiJHDACNCiEGAAQAAkCvr+9X2z68s/kKChcGUgwsdkjz83ID64j0L1Xh6HgAAAE1lZIDhhhj2a2LkMMCIFGI0cYAROcRo/ADjDr8AwzLEWPs6AgwAAIC8/f14vio+O6D23riicF1K4cXg4EDxJ4PL1HR6GwAAAM35l+7NAoxIIUZOAwzrEKPJA4woIUZOAow75vgEGBYhBgEGAABAbt3/K9UyODDqcFmj4u6kwovnVxWuem6F2mO+hCT0MAAAAJpWOcAYVYoZYuQkwJjoE2BYhRjNFWDcogOMmaV4IUaOAow75pQihxgEGAAAALm34WbVtXF5cf7GFcXH404XNbiyeOvgslGH9S9QrfQoAAAAmt5wgBErxMhRgDGxFCPEaL4AY7UOMOKEGDkLMKKHGAQYAAAATUCPmNjQp+Y8v6J4gQQSz9mGFxJc/OO5geJXbl6guuhFAAAAwDUywIgcYuQswIgcYjRngBErxMhhgBEtxCDAAAAAaCKLZVqp51ap/WR9jOsloHjJFFy8sLL4jIzY+PlTN6ldSyVVoOcAAACACpsHGIYQY6lviJHDACNSiNHkAUaEEGN1HgKM2ZsHGPYhBgEGAABAE7rlCjVx44pRx0pYcZeeIqoivHhBwo2/PHbD6HcvmK/G0FMAAACAD/8AwzrEyGmAYR1iNFmAsfVwgBE1xFg9KycBxuzNAwy7EIMAAwAAoIk9cr2aObiq+B0JLp6QdvvG5aOOXnuVGkfPAAAAAAHMAYZNiJGDAGOhKcCwCjGaMMCYUYoVYqzeNkcBxuxSjBCDAAMAAKDZ/+0lU0Q9uEjNeWyR6qY3AAAAAJu/RPcVAwKMsBBjtAQYqvEDjBUTBqWVYoQYTRpgxAgxchdgRA4xCDDq+bvtVeOkzS71qXeV+tXh0vYpLVW7yP+2pr7vxWqi7HeO7P+98r+Hyv++Q9pOpTuYSgINcO0sUKPkvJ0p7a3SDnLOYX0d3ai2pXcy6P8b1dZyr3q70+/96oNy79hT7ikt9AwinUcDaovSMrWDnD97SztEzqX95L93Ly1XU+gdAAAAALX/R4sOMPqKpcghRn8OA4zoIUYTBxgRQ4xcBhiRQgwCjLD+HVBtpb7CLdU19XXr/S1TPfL6U0r9hcXy3pellXzaC/LnC6Wg8xk5vgnJ3Xel4NurviLbXyrtVcO+n5d2hbz2U7pvLO/nF1XfhyP682ubtt1f/Lb0xYBV6ysskWP5Xam3+M2SLEhaZV+9NWRffaWFamyE7R3h/1mLC2Ic23zfbfUWz8j8+ulVM8K/G7VVIvvSoUW/OljOiQvk8z5hOH91e1r69bdybAfKE8fFCP36tarP3V71Sc+18VvPebNCPsPOlsfzWf9zr/jLitccm+i111v4i/F4yqGn7qM1hn5/Sd7/J6cQndBiwLKtr1pf/72FXuf671cn6YAldNv96hOGa/J3yV4jxV8HH7c6xu58KFyf7Hc9vF/3mqpme4vs/06jxsi+P6bvffK+9YZz6TW3b06T72n7iPfHI63PmfI1eYN8/v91zluLEE62/z7DdlbqMCbm782hxuOTvzPwNzUAAACgVkXDoQAjVoiRwwAjWojR5AFGhBAjtwGGdYhBgBFaXJJREOZCqGUbLiga97NYdUiR4tSAgo2pPeW8r4pRGbLvTjnGM2VbGyPu+1/OvkMK9W7xppRcK55X8VtxSRXbWmJbLPb5jfpp6PaXqA9FKFB90bCdNTGO7VeGfrsk8+unX30ptJ+koFzVPqQY7hQX+wp/i3EO/M32e9LnXfXnrvryyG06xV3v6262GelU/nuS7376Kvr/v5K99gqPGe6Tuv8fibCd5XpEV/X35+LPYn4OCYeL5+t7X8C9/z9839tbuDWx6+MmtaVs85WQY11pd37q+3GS33VFUNxX6K9yew9bXMdFZ6RhX+HeiNt+1Qk7LEdXyT7mVfE5HtLBVsi9fG7A+/8v5t9BHjJu0yKMAwAAAJBW0aMywIgcYuQ0wLAPMQgwbEOMXAcYViEGAYZd8SDVAEMKIq+X1z1Q5X7u1tNLRb/Xqn9LoPClR0XMNH++ug0wSk5o06s+HKnPnOmJCo9ZHOdFEb6HfAYY+qnj8O+gP/b2l6nxcn79MYFz6hIdIob0a1YBhj6eM+3+nlT7AEOPKnGeiI++rQ16Wq/q7s+xA4yhJqGLepPh3p9+gKFHg9gcp8Uog0YOMPSUUO4Ikmr2oc+nQy36fF4C94vfmELGkABDf5eHR7yfnxO4PQIMAAAAoIZFQ2+AES3EyG+AYRdiEGDYhhi5CTB2NQQYoSEGAUZogSzdAMMtdmxMqOj0jF6zIkLx7ISAaaqitd7Co7K9vfz3U9cBhjsll9onwjnxXsvtPms7MiaPAYaMbJhlWdh+TcK3bWJtv79wR4Ln1mp5Ir4roF8zDDD0U+XBxf16CDDkWti3ynuITEmn9oh/f646wNBtnZxLr/O5ztMPMGyDAZnaz2JbDRlg6On85Dr+e0LH/ZqeVirkd29eQr9Dv/WbCi00wNC/lZZTP8qx7hZ6fRFgAAAAADUsGm76h3msECNHAcb4UowQowkDjK1LsUKMXAUYu5ZihBgEGKEFsvQCDLcQ/nLCBcZBvWCuRWHk8JhPTQdPZ+XzpHADBBglZwSM5SLDMh/6L+yDHXWI3W9eDgOM8noqtv30H5G27ay3ULgr4fO3PH3TItVu6NcsA4zydDUBixXXOsBwpu7qK9yewDbvjrvAd0IBhm5rvWFj2gGGs9C5/T14dfh133gBRnnNp6pHH/od+4kBv33zEtuPrEPlcy+fa/Ebdlbo96mn1LLpdwIMAAAAoIZFwxHzZkcOMXIWYEQOMZo0wIgRYuQuwIgcYjz6VO+us7njBBXI0gkw9PzvzoiJ8Pe/KO0+CQGWuYWel62K8X2qO+D+upflqI8XnOJied8PBSzsXdlu1302cn8NEWDootfc0PNhQG0hr33Sepv9xd9bnWd5DDD0iAbrfioMWG+3PIXXIsttP+6ef2vK68VEO788/Zp1gKH75bKA77m2AcYS9ebQvtdrXZTXNHgppF++GO/+nFiAoaf2+ZLnHp1ugGGzPkxlC5kisNECDL12kvz/S61GVcj73XPpTiekt1njZKl6j6Hf5yXYT0/qaew89/K5VscXMvJIjvPTVsdAgAEAAADUzrpr1CT5i/k1w0+nRQoxchhgjLcMMCY2eYARMcTIS4Bxe2WAYR1irC/duvtJpfmqyB0nqEAWEGD0q+Pkz/cObcvUDj7FprD5vv8qBY7DvPNsSwG9Tf7/I50nxYOLsOf7fh79VKcU4EL2fa3s4wO6UOzZ9wT5/z/rFpECnkotnjHifTI9i7FvTGsk9BYvNr6nYpSHOcAoXiT7neRps/SxRS1ce86HAyJPkROytoJb9MpVgOEGdNGKgT7XiWHbx4QXztXX5XXTNnuvFIHdc2C9oTj9F3nvHEO/nmcIGRZb3QfK5+5WnvvALRaF5E8Zjic8wNBP+RuPp/gDYwBqPv69Kr6Hk80FZ/V5fa/Z9Fpnseriz/2nbyt+2zTqJfxcMAYYV/lc/28MDDz0NEYVUwKlHmDYrQ9T+bR/2NRIewXcZx80BKxnG99TURg3BxiyELrNee+zzoixf4ebBPfqC95RSM5vmIQT8ud/CAn//q5DEp9+mmf6Xjc7Z/QIkSXqQ8ELi6tjPffyuZbf6fLKa2TENsprgjxBgAEAAAA0gHVLnBDj2uGnfq1DjIdfvHHMLr84WU2uaZsftY3f1AZ7Oz68eYBhPRLjtTsWjB+xvby2BWePnywBxgnSSrFCjNUzVz9+3YxZyR/b9Mxa6Y6dP+cEGLdXBhihIcaG0prdP094UWWA0aveEnObB4Q8mXmq39zaI7axWI2W180PGBXxil8hVhdDA6ef8hRjDPtuCVlYdKNtUUVee52hsPZdy2L9JVECnJDwaFHo/vqLFxjee7/5u7AY2ZG/AOM04zRjes0B/+vpq+H9r1rd0UCmc+96XXS0OL4Z5ZBw0/vuke/goJB+NY3A+EMVRezwAKO38JwUUXeME2CEnHMnmqZTsgwPvmd4/9KAPvxhRYFZFl9X21V3nhkDiUsDjuGsgFB659ACewIBRuD6MKb1IGS9lyrOs7sN19znLN9vCDDU12N+/kkhI6L+YLNOhL5m5bVPB/xGn+xzDzGNwFhl3M8KNdU4YrK3cLnnmOZGCKWOjnYPJ8AAAAAA6tL6P6vJ7kiMuNNJVbQtZAi+t43xtLE+raXclnlbq09rG27Lh1q7p3UY2rhyW1HZxpdihhjehb3dNnm4DQy1KZ7W6dO6SqVVfq3bp/WMbDdP9WlbGtq0crulsm3ladN92tal2CGGd2Fvp23r02YNt9sq23Y+bfuRbc0OPm3HgLZTud0+1Hb2abt42q4lyxBjsHTbbicQXlgXYpMPMKTAGLDNj0XblvpawEiIi332fU/AosHvi7Tv/uKPAqZOOtuyL2oQYBgKmL2FPwXuqzzlian4fpqxyOcpcBm+x3wFGMa1EfSxFS+MWxh2F54vGUdCSLBnfYzy1L8TeOhCtWekk6FfaxNglNtKPX2Z53hqG2D0Fb8ftdBeDj/lwZR+tV8y9+cYAUa/8/dKw9oT6tCKe396AYZ5fZgNcnwHBzztPyfmeVZfAUbg1GbF30Tc1tsCpih73HtPiBNguL933zW87y7PdTU3wsiap2R0Upfn3Ng70vpUBBgAAABAfXCnk7oisRCjnxAjdogxQIjR4CHGRgkvjiW8iFRoSjTA0NPaGJ/Ul8WhI2+vvNCnaT2A9ZWFWWcqJ3PR6PTI+5ZtB6xv8Q/Lwlj2AYYudPu/59yQgu9BQYXFgClRNoY9TZynAMPpC/M1c6Be2Nz45zepXUPOlxsM7/2XnqYo3c9V0wBDB5Lf9BxPbQOMoDUceos/1tPdpX9/jh5guP3+uGEExlEV9+r0Agzj+jDFBe46O4ZRBcVvxdxffQUY5vN+bZzpxALPxSXqnZ7XxgswZLSEKSTxXFdzo02fV/z5pvc6IysjrB1EgAEAAADUl0euVp36H6SEGIQYjR1ibF/LEGNQ1rw4xruuAcIKZAkHGOaFOV+KW4CV49gzYEqU/SpeZ5rWZ53NOg2GfR8QsO/XWxSy0gowTGtgfC/g+zwkZF+/Nbz3HufPb1TbBgQcR4QUk3MUYBS/ZVwPRK/hopueFiliQdR9ct6wiL06NYPPZV4DQz8FHtaWqekRCrn+o6T61T4Vx1PjKaScJ8WDpr561LneetXs9O7PMUZglEeBvBI23VtaAUbg+jD96iPud3uh4TX3xjt36yfAkPUrtklqBOKmbeowvbzQt1+Y9gPPfSRegKHX1bH4TqIHGDLaQu4PgeccAQYAAADQOB6/ShbT00+nZRZijCHEIMRIPsS4rSYhxkZZ8+LoxYvfOZo7SeRiU8Ai3sX/1fP2W7Q9h4snxV8YtndjVcdpmjddppiqKEJdbXrqN/Z+y0+MrrNZ3NRQGEspwIjc1gZNI+QWPdcb+u87FZ/H9PTs1SHFsRwFGIW7worKcr5eZnjNnQHX4nsDAqKZ6X8u4wgMyzZ8LVb01S0Rt/OgDuPc46ltgKFHf8l0UdZTYOlzPOFia7w1MGSBcYvQNcUA4zRjiL1YTXRfc0g1wbDPeZZSgCH3b7vfwA9X9P9hxvWfYgbp7vXwU8P31TvytzLGGhjltXfuM7zvSs/5Ndf4/ZqCM72AeDkA32B47wsEGAAAAEADcUKM/uLvGYlBiFG/Icasegsxnpdpo45j5EXsYtO46ovj6vMVhaBrDcXl86o6zv7CQtNULhWvGUhyWpKKz7TKtC6ExXvrIcAYHHoCNqDoeZjNSJyAp3Rf0iMIArafiwBDF1cDCq8frfi8RwS8bjdDH33ctMi1RZ+cW34AIlL7iWcbWQYYLwZMOXORezw1DTCcbSxRbzaPpjE8bS4FZafIG2G9EvP92T7AcNawKY+Ae97wnicqjym1AMO0Pozcwze9JnCUUvHMGPtMK8CwbddWnHemAOmBKu89J1mNkIgYYOiRU/Jnfw64ro/1XFemAONJZ2o1cx89EvCwxDcIMAAAAIAG89jlqpvppAgxmmtNjJ3ihhjPsmB3tQWyxAOMVUnOJT6yQOtbFLusYt8PGYpYn6myIHelodD2U4v31kGAUfydPE1eCDnOSw3vf7jyvbr4HnAefCqgmJyPAEOKq8YAxx054BZoJ5iL9P7rsQRMr7LW4jx7OMa5cb/ns2UXYOgFg/UUTAFhUD0EGO45//6AJ8eD2hqZTujt1d2fDQFGufh/36ZWHqH2UsgaI9+zOt+qCDAC14fxFsLNo5QeCLtf+ZxndRRgFP+7mvM24LfaNGrlec/5Oi9glMN9nvZQ6DRpnlEjgQHGMjU+MKjwb3+V62QXAgwAAACgAT17uZriFs1eq2mIsayOQwzfACOFEGMVIUadhhjrS6vnnEh4UWVBNvkAY3k6oyCKvzQV5yv2/Y9qilgBxS3TqJJzLN5bH1NI9RYv1lPi+O5DFpU1Pg1dMcKl4jOtNeznzwGFzZwEGIV7DYW+P21+bRX+FGWe/4A+SifAkKK3p18zHIGhvuwGA68Z163R67zUQYDhbKu8rsGiGP3yUlCwF35/No7AiNqekOPo9tz7UwgwjOvDvFpaoaZ6vp+gUUp7Rbwu6yfAMI8mWFHdvccYHKwb8TpzgBG9VYwqsziOJ90//3iEfbzohBf9amcCDAAAAKBBrbtGFmXtK1zj/MMviRBjKSEGIUZuQoz1pTVzPk94kUBBNvkA40pDwf6CKgvH14UtYCr/vTRsDYeY+15jKI59JfZxZ78GhvF49cK6AQWsd/sc05nGOd49RVKL4nzDBBi6qBrQT8f5vP444+uXqjf4XIsfs3nC2nCexRmBscbTr5kGGO4+/ydwOqY6CTA2bVNPKVVe7H4wQt+8EnckRkIBxityLn7Q53xLIcAwrg+z2RpIIaOUzoq433qaQup402iG6n6HJPSzWFcnsQCjv3i24TgCAwy3P6+3HB14unvMBBgAAABAI6sIMRiJQYhBiFFuGxh5kWBRNukAQy/87f+090DsY9SL6fYVHjPs+9Thz1K4PKy4FHnfenSCsVipjrQojKUTYOhFUZ0gwdPKi6g/a+iHjaXlaorPMRqmyCo85TePf7mIa1/Id4tejR9gSFHV9uly9zN3Gxe19QnV5FrcN+AamxNynsUJMJZ4Pt955qf3CzeGN/Vxn+MKDjD0ug3OuRzpuGsWYGzatkyr405ztSDgehuxyHe8+3PVAcaLeqSD4d6faIARsj7MF/yPwThK6Z+mEWOG87+eAoyDjPcJQ8Bref/5jeG3dbHne5iXQOD1X6ZpvKwCjCVqx8CFuYemCpPfV/eYCTAAAACARrduiRNiXF3zNTEIMQgxah1i3LHzYOnWOccTXiRYlA0KMPqLv9fTJIU39W8V2/uY8WlqmSoiZmHs/QFPsr+9orByinHub58Cs+W+PxFQlNveojCW0giM4vkB3+lO5oV8h8Mm57XlOcs3Gj7jfb4hiRTfzYX5wvWGoldDBxi6mCf7eNAY9Pj305nOvPCG9Se8BUKnKG78Loo/DDnPlvrMbz/UDAX24i89/WoKMP4Qv+gaHGC45+vsiCMaah5gjNiPE8I46xMEP3Ue4/5XVYDhBEPqTQH3iWQDjN7iGQFT2P3acH3cFDBibO8I51k6AUZ/YZnVb2BFcBscXI68/1p/vvJolXU20zNWGWC8FDZ9l02AEXo+6LZEfajimAkwAAAAgDx45go1ceSCh4QYNQsxWNi7ViHGxtKts48hvEi4MBsUYPSqt0Te3mK5V5kWk+0vLIzyVK27vRZzEVRGZVRsTwor2wWEMRfE+izlxXH9tnm7ZWEs8wDDfZ9h/YDizz3Frk8kNkXV0FPGvWqaT9GrsQMMKaYm3E+6gPdmn/PFsN5KYb2ETdNjFD63MK7b4Q2zahRguP17cj0HGNL3Pc7aBgvUqJB9fiog8Dw8+nkXKcB4yQms9Ho3verAsHtt4gGG8TyLPYXRjyLsO6URGOrrMftiiXFRbDmXYtzzfhhwH3mj554+zzySqniu0/Q5Yv7d/0zIOW4XYAyotoDfzys8x0yAAQAAAOTFI1erzvIT0VmFGGMIMQgxkg8xfIOM0BBjsLR61lFhxSPEKswmGmCEFGF958k3bkc/9V6eEsk0f/ZPNnuPTFUVsO9jrfct55q856qAvjnNsi9qE2CYCp8SItl/V7HX2vicT9GrsQMMKaYm3k8+c8xL330y4D1L9SiNiH11ZkDhc5bntbULMMojXK6ttwCjdIcaI8XVLzkBUjmEOCH8XDEVbaM/fR8QYFyawL0/sQAjcFq5+CNIHrX9za+/AMN47ul2gw4WI3xPHw5YD+bezUZymQOMVZ7zdKFxRJnPVIMVn80qwHCP5WC/NX0klNjW8zoCDAAAACBPHrtchqYTYhBi1HWIMSvpEENGXmx/9GKfufiRQGE2lQBD7VGe79sYPPxGP50ZuA09tdGIe53Peg4+hQ055gOCFwWW6T5CikfuFCCLArbztBTsJlkWxrKfQko/+arnF/cPMC6rKDpOMi+iW1Vb4tOnDRtguGuwPJxCPz3sfUperzmiF+UNeM/dco7vGXrMTgBXPD2gONzr0681CzAqrrvH6iXAcO8ld222pkTF1De+962+wgbDZ/5s9PtzgwQYEsalcH3o36B9Lc+z+gowyiMHHwj4bCu8AaLPfUcCfHWScURjOVD76ObfhW2A4YQGhm0Xzwm4rqwDjPJ+Cn80rVvlORYCDAAAACBPHr/KmcqAEIMQowlGYuz0vLz3OEZepCeNAMMthl4SuoCnjGLQ88LrYo/zHqfwLuGHFPflz/8V/P7imQHFqBtC9n2XU7xbpnbQT1hv6geZ+9t90n5dSFHt5AgF3JQCDBkdIlOHjGhS2Hae1nXmbTf227cqvvujUyk66qCoV83wFL1MAcZD8tpjQlu/2qeiTwwBRmG51baWqd0jXiP7ptRPvvP8G55aHjlVkJ4CRgrpelqpoUDOKXjKk83y/k/rKc5C9ru/z7l2niH0GrDqV92WqHfGDTDc8+SgeggwdOE6eJ0AXbDX9yrV6rxej9Qor+WxKKDYvF9DBBh6AW2b79pd+8gdPfNAOtdH8WeNGGC459CRIZ9vg3PN9au36ekK3d/ALZxrWEbrhF7DfYWb/aYJsw0w3Gv+HMNrX5bt7JZIgCG/BRKKPbfpt1fWjCHAAAAAAJrEowtVl/4HbPQQYzRrYhBiNMqaGM/Ka05gzYtGDTCcp6n/EaFYtS7Ca28pLVLtxn3Lk63l+b4ti+3GRY4TmP4jvQAjZhte1Ff++/9SLMyfbBlgWLbihRYBhm0fnJpQETm1ef4DRkOYzuGnA5/UDph/PuY+Td/Tb6sJMCz7O4sA460RPvczFq8Z1KMzGiTAsA0Xfu1uZ+/Urg99H7cYfVmXAYYzgqJy7brQtsG8+LfPaw1BbKQAozwKz/RbeX0SAYb7nv90+/NdhmMmwAAAAADyav2f1WT5x8Dx5X+kF33aFidKGFFFG5tga5XWUX0bGGrjfdrEKtsUT+tKoPUEt5ujtGnSpifUZhjatsNtdZS23XBbk0Tb4UQJLw4ivGjcAMPZ9k1q101zxydZ0PLMn234XHunMD3S/fKZuiL1QX0FGIsqvpsu5ylbw/RCesHh0Faem32Dab2GPAQY7pROjxu2dY9VP+lmWuDYMM+/8yR2f2Fx8oGJXp9Bddd1gFGeAu3Omk8hVfV5NqJfzo13f26AAMP8FH/J+Q6sro+gvlbva8QAwy3MtwavyxR7hNuHA/ZpHWC418lnA/r+oEQCjPJaMu8POGYCDAAAAAAA4C/NAMPd/t4BBeDoxVfDtBa+++5XH0wwQLktbM5y32OonwDjn3q6oYrv5TMBU90cZf/5iguMRbalapvh1zVogCHF04Bj+m/766B4RtR5/t31FK5KsPD5kJzDOwZ8l3URYDjvW6reEBBAZhNglPt/VSLXXsTgs+K8qesAI2R9mH94F5gO+K7mBJxT51vcZ+sywHC2vUJN1YFuQtfwRjmmI0J+96IFGM56OfL75v+e+4ameKz4riIHGBa/1QQYAAAAAADAX9oBhrOPPjWz6qdQewt/kUJKZ+R9l0eBrK2yaHSp7Lsj3meviwBjrV5rZMR2zU/3vxKl2CqFp48EBAVfrjgHGjTAKJ5vMx2XRT/tFWeef6dA3F/8hnG0jP310yvX87SQz1o3AYZ7zpxaywDD2Ybcc/Si9FX0+6NRQtfN7891HmAErQ/TW/xBxHvlWuMUXe5aRQHvrdsAw9m+rPsQfC+xag/Y3HOiBhjutfKugEB7nue1BBgAAAAAACA7WQQYzn6cQqwzlc59EYs2dzrvs3yS13ffznQ86jinmBht3yv1QslVfe7aBhhPyn6+PbTQ8KZtlp8INs21fmOM82djWMGsEQMM57zpKzxlHM0Q4Zx0Fzp+0DgtWsi6Ks6C833Fi+S1r0YueupFvUvh0/HVXYBRfrr/+loGGJvOg151WsVCxHbT/Oj1D25SW1Z3f673ACNgvRLPgu4W59+ZAdfsB0Lus3UdYFT0+VtiTA33tBPmee7jxn3ECDDcPrjSuN5GRfhJgAEAAAAAADKlp4fQRUff1q+2T3x/+knUXnWgnhPeWVOivADxyBEAfYW75M9/KKMG3uO3PkDsfcvC31J8OcwtgD/kUyzRiyDfLn/+HTnGd1QTmmzaZ6/6iqFvP2L1/n71hfI0TZZNFoV29tmrDjAVxWWb+5jfrw6P/hmL3zRub0BNcPvhAON5ZtXU8RXH/+mqtmUZSpWLaobPZVkY9WzvBOP2AqZ2GrENWf9F71vO02sNi0e/Wl5vQ87xfnWwzQLIFefq0dV9R855fcLIz1z8rqH/DrQ6Jpn2TLbxe8/7v2X1Xmf6L9/j/H6sa1nWDpHPd5L07w3S1hnuHzLllJpfzagLzzlznP85o06petsSClT1Xetp6HqLP/b/fosXRzn33O9694D70lHBn6V4luG+8T7Lvphv+IyHpPK7u0S9WbZ/uhvw+U2V9tjQ78TQPTTCOXOwoR+/E/K+7QPudx+reN3bDK87v4rzfCvjvperKfxNDQAAAAAA1IyzSLI83SlttjMyoJTdwu3uyIytnH0vUz18G2jA66dFP6HsFH91uGH5lDYS6Hu5ZzgjY/T9I+YaF4B7HXc655G+liXkp0cAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADXy/4VIE3ykdm7AAAAAAElFTkSuQmCC" alt="Logo" style="width: 200px; height: 50px;"></td>
      </tr>
    </tbody>
  </table><table class="module" role="module" data-type="divider" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="4a7973f0-d3c5-46d0-9947-d6c48494c42b">
    <tbody>
      <tr>
        <td style="padding:0px 0px 0px 0px;" role="module-content" height="100%" valign="top" bgcolor="">
          <table border="0" cellpadding="0" cellspacing="0" align="center" width="100%" height="1px" style="line-height:1px; font-size:1px;">
            <tbody>
              <tr>
                <td style="padding:0px 0px 1px 0px;" bgcolor="#000000"></td>
              </tr>
            </tbody>
          </table>
        </td>
      </tr>
    </tbody>
  </table><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="4163597f-ac80-43ad-9d70-28492925dae5" data-mc-module-version="2019-10-22">
    <tbody>
      <tr>
        <td style="padding:5px 0px 7px 0px; line-height:26px; text-align:inherit;" height="100%" valign="top" bgcolor="" role="module-content"><div><div style="font-family: inherit; text-align: center"><span style="background-color: rgb(255, 255, 255); color: #000000; font-size: 18px; font-family: helvetica, sans-serif">A new assigned player from:</span></div>
<div style="font-family: inherit; text-align: center"><span style="background-color: rgb(255, 255, 255); font-family: helvetica, sans-serif; font-size: 18px; color: #000000"><strong>{{creator}}</strong></span></div><div></div></div></td>
      </tr>
    </tbody>
  </table><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="812800d2-23d9-40ba-b6c8-96c02a5fc49a" data-mc-module-version="2019-10-22">
    <tbody>
      <tr>
        <td style="padding:20px 0px 18px 0px; line-height:16px; text-align:inherit;" height="100%" valign="top" bgcolor="" role="module-content"><div><ul>
  <li style="text-align: left; font-family: helvetica, sans-serif; font-size: 16px; font-size: 16px"><span style="overflow-wrap: break-word; margin-top: 0in; margin-bottom: 11.25pt; line-height: 15.75pt; text-size-adjust: none; color: #0388f2; font-size: 16px; font-family: helvetica, sans-serif"><u><strong><a href="{{player.player_info.tm_link}}" target="_blank">{{player.player_info.firstName}} {{player.player_info.lastName}}</a></strong></u></span></a><span style="overflow-wrap: break-word; margin-top: 0in; color: #333333; margin-bottom: 11.25pt; line-height: 15.75pt; text-size-adjust: none; font-size: 16px; font-family: helvetica, sans-serif"><strong> |</strong></span><span style="overflow-wrap: break-word; margin-top: 0in; color: #333333; margin-bottom: 11.25pt; line-height: 15.75pt; text-size-adjust: none; font-size: 16px; font-family: helvetica, sans-serif"> </span><span style="overflow-wrap: break-word; margin-top: 0in; color: #333333; margin-bottom: 11.25pt; line-height: 15.75pt; text-size-adjust: none; font-size: 16px; font-family: helvetica, sans-serif"><strong>{{player.player_info.team_name}}</strong></span><span style="font-size: 16px; font-family: helvetica, sans-serif"><strong>&nbsp;</strong></span></li>
  <li style="text-align: left; font-family: helvetica, sans-serif; font-size: 16px; font-size: 16px"><span style="overflow-wrap: break-word; margin-top: 0in; color: #333333; margin-bottom: 11.25pt; line-height: 15.75pt; text-size-adjust: none; font-size: 16px; font-family: helvetica, sans-serif">{{player.player_info.birthDate}}</span><span style="overflow-wrap: break-word; margin-top: 0in; color: #333333; margin-bottom: 11.25pt; line-height: 15.75pt; text-size-adjust: none; font-size: 16px; font-family: helvetica, sans-serif"><strong> |</strong></span><span style="overflow-wrap: break-word; margin-top: 0in; color: #333333; margin-bottom: 11.25pt; line-height: 15.75pt; text-size-adjust: none; font-size: 16px; font-family: helvetica, sans-serif"> Passport: </span><span style="overflow-wrap: break-word; margin-top: 0in; color: #333333; margin-bottom: 11.25pt; line-height: 15.75pt; text-size-adjust: none; font-size: 16px; font-family: helvetica, sans-serif"><strong>{{player.player_info.passport}}</strong></span><span style="font-size: 16px; font-family: helvetica, sans-serif"><strong>&nbsp;</strong></span></li>
  <li color="rgb(51, 51, 51)" style="text-align: left; font-family: helvetica, sans-serif; color: rgb(51, 51, 51); font-size: 16px; font-size: 16px"><span style="font-size: 16px; font-family: helvetica, sans-serif">Positions: </span><span style="font-size: 16px; font-family: helvetica, sans-serif"><strong>{{pos_list}}</strong></span></li>
  <li color="rgb(51, 51, 51)" style="text-align: left; font-family: helvetica, sans-serif; color: rgb(51, 51, 51); font-size: 16px; font-size: 16px"><span style="font-size: 16px; font-family: helvetica, sans-serif">Current status: </span><span style="font-size: 16px; font-family: helvetica, sans-serif"><strong>{{player.control_stage}}</strong></span></li>
</ul><div></div></div></td>
      </tr>
    </tbody>
  </table><table border="0" cellpadding="0" cellspacing="0" class="module" data-role="module-button" data-type="button" role="module" style="table-layout:fixed;" width="100%" data-muid="dc1b737e-7cb2-4bc4-807b-bbdcde40e0ec">
      <tbody>
        <tr>
          <td align="center" bgcolor="#ffffff" class="outer-td" style="padding:0px 0px 0px 0px; background-color:#ffffff;">
            <table border="0" cellpadding="0" cellspacing="0" class="wrapper-mobile" style="text-align:center;">
              <tbody>
                <tr>
                <td align="center" bgcolor="#ffc501" class="inner-td" style="border-radius:6px; font-size:16px; text-align:center; background-color:inherit;">
                  <a href="https://platform.enskai.com/player_records" style="background-color:#ffc501; border:1px solid #333333; border-color:#333333; border-radius:6px; border-width:1px; color:#000000; display:inline-block; font-size:14px; font-weight:bold; letter-spacing:0px; line-height:normal; padding:12px 18px 12px 18px; text-align:center; text-decoration:none; border-style:solid; font-family:helvetica,sans-serif;" target="_blank">View the Player</a>
                </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table><table class="module" role="module" data-type="divider" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="9b2d5836-48ff-4d65-a903-6522496d0440">
    <tbody>
      <tr>
        <td style="padding:20px 0px 0px 0px;" role="module-content" height="100%" valign="top" bgcolor="">
          <table border="0" cellpadding="0" cellspacing="0" align="center" width="100%" height="1px" style="line-height:1px; font-size:1px;">
            <tbody>
              <tr>
                <td style="padding:0px 0px 1px 0px;" bgcolor="#bcbcbc"></td>
              </tr>
            </tbody>
          </table>
        </td>
      </tr>
    </tbody>
  </table><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="28226bfc-c591-4796-a8ed-1e94935832c5" data-mc-module-version="2019-10-22">
    <tbody>
      <tr>
        <td style="padding:18px 0px 18px 0px; line-height:22px; text-align:inherit;" height="100%" valign="top" bgcolor="" role="module-content"><div><div style="font-family: inherit; text-align: center"><span style="color: #bcbcbc; font-family: helvetica, sans-serif; font-size: 10px">This notification was triggered by a user action on EnskAI: Agents Platform.<br>
</span></div>
<div style="font-family: inherit; text-align: center"><span style="color: #bcbcbc; font-family: helvetica, sans-serif; font-size: 10px">For help or more information, contact <EMAIL><br>
</span></div>
<div style="font-family: inherit; text-align: center"><span style="color: #bcbcbc; font-family: helvetica, sans-serif; font-size: 10px">© Copyright EnskAI 2024</span></div><div></div></div></td>
      </tr>
    </tbody>
  </table></td>
                                      </tr>
                                    </table>
                                    <!--[if mso]>
                                  </td>
                                </tr>
                              </table>
                            </center>
                            <![endif]-->
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </div>
      </center>
    </body>
  </html>