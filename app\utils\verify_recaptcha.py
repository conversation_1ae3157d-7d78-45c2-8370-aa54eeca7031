import requests
from app.config import settings

def verify_recaptcha(token: str) -> bool:
    """ Verify token with Google reCAPTCHA v3 """
    if not token:
        return False
    url = "https://www.google.com/recaptcha/api/siteverify"
    data = {
        "secret": settings.CAPTCHA_SECRET,
        "response": token
    }
    try:
        r = requests.post(url, data=data)
        result = r.json()
        print("reCAPTCHA result:", result)
        if not result.get("success"):
            return False
        if result.get("score", 0) < 0.5:
            return False
        return True
    except:
        return False
