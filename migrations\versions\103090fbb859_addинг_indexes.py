"""addинг indexes~

Revision ID: 103090fbb859
Revises: 9ce4b2908388
Create Date: 2022-08-10 08:44:40.836689

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '103090fbb859'
down_revision = '9ce4b2908388'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_crm_dev_contacts_organization_id'), 'contacts', ['organization_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_proposals_player_id'), 'proposals', ['player_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_proposals_request_id'), 'proposals', ['request_id'], unique=False, schema='crm_dev')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_dev_proposals_request_id'), table_name='proposals', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_proposals_player_id'), table_name='proposals', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_contacts_organization_id'), table_name='contacts', schema='crm_dev')
    # ### end Alembic commands ###