from sqlalchemy import Column, String, Integer, DateTime, Float, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from app.models.extended_base_mixin import ExtendedBaseMixin
from app.db.base_class import Base
from app.config import settings
from app.models.comment import Comment

class PlayerRecord(Base, ExtendedBaseMixin):
    __tablename__ = "player_records"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    playerId = Column(
        Integer,
        ForeignKey("wyscout.player_info2.playerId"),
        index=True,
    )
    player_info = relationship(
        "PlayerInfo",
        # primaryjoin=f"wyscout.player_info2.playerId=={settings.PG_SCHEMA}.player_record.playerId",
        viewonly=True,
        primaryjoin="foreign(PlayerRecord.playerId)==remote(PlayerInfo.playerId)",
    )
    control_stage = Column(String)
    position = Column(ARRAY(String))
    quality = Column(Integer, default=-1)
    potential = Column(Integer, default=-1)
    club_asking_price = Column(Float)
    transfer_strategy = Column(String)
    deals_with_player = relationship(
        "Proposal", back_populates="player", cascade="all, delete-orphan"
    )
    description = Column(String)
    current_gross_salary = Column(Float)
    expected_net_salary = Column(Float)
    scouting_reports = relationship("Report", back_populates="player")
    changelog = relationship(
        "PlayerRecordChange",
    )
    video_link = Column(String)
    organization_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.organizations.id"),
        index=True,
    )
    organization = relationship("Organization", back_populates="player_records")
    contracts = relationship(
        "Contract", back_populates="player", cascade="all, delete-orphan"
    )
    notifications = relationship(
        "NotificationSettings",
        primaryjoin="foreign(PlayerRecord.id)==remote(NotificationSettings.player_id)",
        # cascade="all, delete-orphan"
    )
    uploads = relationship("PlayerUpload", back_populates="player")

    comments = relationship(
        "Comment",
        back_populates="player",
        foreign_keys=Comment.player_id,
        cascade="all, delete-orphan"
    )
    assigned_to_record = relationship(
        "AssignedToRecord", primaryjoin='PlayerRecord.id==AssignedToRecord.player_id',
        cascade="all, delete-orphan"
    )

class PlayerUpload(Base, ExtendedBaseMixin):
    __tablename__ = "player_uploads"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    id = Column(String, primary_key=True, )
    player_id = Column(
        UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.player_records.id"), index=True
    )
    player = relationship("PlayerRecord", back_populates="uploads")
    name = Column(String)