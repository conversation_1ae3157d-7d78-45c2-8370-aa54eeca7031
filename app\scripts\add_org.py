from sqlalchemy import create_engine
import uuid
from sqlalchemy.sql import text

try:
    from app.config import settings
except:
    from ...app.config import settings

try:
    from app.scripts.add_purcases import add_purchases_decorator
except:
    from ...app.config import settings
import pandas as pd

@add_purchases_decorator # Add purchases while adding the organization
def create_org(name: str, description: str, schema=settings.PG_SCHEMA) -> uuid: 
    engine = create_engine(settings.PG_URL)
    org_uuid = uuid.uuid4()
    # You can either supply a schema or use the default one
    with engine.connect() as conn:
        conn.execute(
        text(
            f"""INSERT INTO {schema}.organizations (id, name, password)
     VALUES ('{org_uuid}', :name, :description)"""
        ),
        {"name": name, "password": description},
    )
    return (org_uuid, schema)

