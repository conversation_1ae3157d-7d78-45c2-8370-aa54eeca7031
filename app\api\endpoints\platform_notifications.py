
from typing import Any, List
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from psycopg2.errors import ForeignKeyViolation
from app.schemas.platform_notifications import (
    PlatformNotification,
    PlatformNotificationCreate
)
from app import crud, models
from app.api import deps
from app.config import settings
from sqlalchemy import text

router = APIRouter()

@router.get(
    "/", response_model=List[PlatformNotification]
)
async def read_all_in_app_notifications_for_a_user(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all in-app notifications for a specific user
    """
    notifications = crud.platform_notification.get_all_by_user_id_in_app(db=db, user_id=current_user.id)
    return notifications

@router.get(
    "/count", response_model=int
)
async def get_count_of_notif_for_a_uer(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Retrieve the count of notiications for a specific user
    """
    if settings.PG_SCHEMA == "crm":
        query = text(f"update hubspot_users set last_activity = now() where email = :email")
        db.execute(query, {"email": current_user.email})
        db.commit()
    count = crud.platform_notification.count_by_user_id_in_app(db=db, user_id=current_user.id)
    return count

@router.post(
    "/",
) 
async def create_in_app_notification(
    platform_notification_in: PlatformNotificationCreate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create an in-app notification for a user
    """
    try:
        notification = crud.platform_notification.create_with_user(db=db, user=current_user, obj_in=platform_notification_in)
    except IntegrityError as e:
    # Check if the IntegrityError is due to a ForeignKeyViolation
        if isinstance(e.orig, ForeignKeyViolation):
            print(f"Ignoring ForeignKeyViolation for created_for: {platform_notification_in.created_for}")
            db.rollback()  # Rollback the transaction so that the session can continue
            return True
        else:
            raise
    return notification

async def create_in_app_notifications_bulk(
    platform_notifications_in: List[PlatformNotificationCreate],
    user_ids: List[str],
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create in-app notifications in bulk for a user
    """
    for user_id in user_ids:
        crud.platform_notification.create_bulk_notifications(db=db,current_user=current_user,obj_in_list=platform_notifications_in, user_id=user_id)
    
    return True