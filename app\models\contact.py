from sqlalchemy import Column, String, ForeignKey
from sqlalchemy.orm import relationship

from app.models.extended_base_mixin import ExtendedBaseMixin
from app.models.player_record import PlayerRecord
from app.models.activity import Activity
from app.db.base_class import Base
from app.config import settings
from app.models.source_to_record import SourceToRecord
from app.models.assigned_to_record import AssignedToRecord
from sqlalchemy.dialects.postgresql import UUID
import uuid


class Contact(Base, ExtendedBaseMixin):
    __tablename__ = "contacts"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    id = Column(
        UUID(as_uuid=True),
        # ForeignKey(f"{settings.PG_SCHEMA}.user.id"),
        primary_key=True,
        index=True,
        default = uuid.uuid4
    )
    first_name = Column(String)
    last_name = Column(String)
    contact_type = Column(String)
    title = Column(String, default = '')
    email = Column(String)
    contact_organization = Column(String, default = '')
    phone_number = Column(String)
    owner = Column(String)

    sourced_entities = relationship("SourceToRecord", foreign_keys=SourceToRecord.source_id, viewonly=True)

    assigned_entities = relationship("AssignedToRecord", foreign_keys=AssignedToRecord.contact_id, viewonly=True)
    updated_activities = relationship(
        "Activity",
        back_populates="updated_by",
        foreign_keys=Activity.updated_by_id,
    )
    organization = relationship("Organization", back_populates="contacts")
