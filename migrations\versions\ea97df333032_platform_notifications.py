"""Platform notifications

Revision ID: ea97df333032
Revises: ebeac9243352
Create Date: 2024-10-16 12:43:48.910463

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = 'ea97df333032'
down_revision = 'ebeac9243352'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('platform_notifications',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('active', sa.<PERSON>(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('activity_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('team_request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('player_record_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('community_proposal_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_by', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_for', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=True),
    sa.ForeignKeyConstraint(['activity_id'], ['crm.activity.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['community_proposal_id'], ['crm.community_proposals.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['created_by'], ['crm.user.id'], ),
    sa.ForeignKeyConstraint(['created_for'], ['crm.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm.organizations.id'], ),
    sa.ForeignKeyConstraint(['player_record_id'], ['crm.player_records.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['team_request_id'], ['crm.team_requests.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_platform_notifications_active'), 'platform_notifications', ['active'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_platform_notifications_created_at'), 'platform_notifications', ['created_at'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_platform_notifications_created_by'), 'platform_notifications', ['created_by'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_platform_notifications_created_for'), 'platform_notifications', ['created_for'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_platform_notifications_is_sensitive'), 'platform_notifications', ['is_sensitive'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_platform_notifications_last_updated'), 'platform_notifications', ['last_updated'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_platform_notifications_organization_id'), 'platform_notifications', ['organization_id'], unique=False, schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_platform_notifications_organization_id'), table_name='platform_notifications', schema='crm')
    op.drop_index(op.f('ix_crm_platform_notifications_last_updated'), table_name='platform_notifications', schema='crm')
    op.drop_index(op.f('ix_crm_platform_notifications_is_sensitive'), table_name='platform_notifications', schema='crm')
    op.drop_index(op.f('ix_crm_platform_notifications_created_for'), table_name='platform_notifications', schema='crm')
    op.drop_index(op.f('ix_crm_platform_notifications_created_by'), table_name='platform_notifications', schema='crm')
    op.drop_index(op.f('ix_crm_platform_notifications_created_at'), table_name='platform_notifications', schema='crm')
    op.drop_index(op.f('ix_crm_platform_notifications_active'), table_name='platform_notifications', schema='crm')
    op.drop_table('platform_notifications', schema='crm')
    # ### end Alembic commands ###