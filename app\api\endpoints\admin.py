from typing import Any, Optional

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from app.api.endpoints.users.auth import UserManager, get_user_manager
from app.schemas.purchase import PurchaseCreate
from app import crud, models
from app.api import deps, utils
from app.config import settings
from sqlalchemy import text
from app.utils.admin_logging import log_admin_action

router = APIRouter()


class ManuallyAddPlayerRequest(BaseModel):
    firstName: str
    lastName: str
    height: Optional[int] = None
    birthDate: str  # Expected format: YYYY-MM-DD
    foot: Optional[str] = "right"  # Default to 'right'
    currentTeamId: Optional[int] = None
    birthArea_name: str
    passportArea_name: str


@router.get("/modules")
async def read_all_modules(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all Modules.
    """
    utils.check_access_admin(current_user)  # Ensure the user is an admin

    query = text(f"SELECT * FROM {settings.PG_SCHEMA}.modules")
    modules = db.execute(query).mappings().all()  # Fetch results as mappings (dicts)

    return modules


@router.get("/roles")
async def read_all_roles(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Retrieve all user roles.
    """
    utils.check_access_admin(current_user)  # Ensure the user has admin access

    query = text(f"SELECT * FROM {settings.PG_SCHEMA}.user_roles")
    roles = db.execute(query).mappings().all()

    return roles


@router.get("/organization/{org_id}")
async def read_all_purchases_by_org(
    org_id: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Retrieve all purchases for a specific organization by its ID.
    """
    utils.check_access_admin(current_user)  # Ensure the user has admin access

    query = text(
        f"""
        SELECT p.*, m.name 
        FROM {settings.PG_SCHEMA}.purchases p
        JOIN {settings.PG_SCHEMA}.modules m ON m.id = p.module_id
        WHERE p.organization_id = :org_id
        """
    )
    purchases = db.execute(query, {"org_id": org_id}).mappings().all()

    return purchases


@router.post("/", response_model=Any)
async def create_purchase(
    *,
    db: Session = Depends(deps.get_db),
    obj_in: PurchaseCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new purchase.
    """
    utils.check_access_admin(current_user)
    purchase = crud.purchase.create(
        db=db,
        obj_in=obj_in,
    )
    # Log the purchase creation
    log_admin_action(
        db=db,
        user=current_user,
        action_type="create_purchase",
        target_type="purchase",
        target_id=purchase.id,
        details=f"Purchased module {purchase.module.name} for organization {purchase.organization.name}",
    )

    return purchase


@router.delete("/{id}/{organization_id}", response_model=Any)
async def delete_purchase(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    organization_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a purchase.
    """
    utils.check_access_admin(current_user)

    # Get purchase details before deletion for logging
    purchase = crud.purchase.get(db=db, id=id)
    if not purchase:
        raise HTTPException(status_code=404, detail="Purchase not found")

    purchase = crud.purchase.remove(db=db, id=id)

    # Log the purchase deletion
    log_admin_action(
        db=db,
        user=current_user,
        action_type="delete_purchase",
        target_type="purchase",
        target_id=id,
        details=f"Deleted module {purchase.module.name} for organization {purchase.organization.name}",
    )

    return purchase


@router.get("/users")
async def get_all_users(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Get all users along with their roles.
    """
    utils.check_access_admin(current_user)  # Ensure the user has admin access

    query = text(
        f"""
        SELECT u.*, ur.name AS role_name 
        FROM {settings.PG_SCHEMA}.user u
        JOIN {settings.PG_SCHEMA}.user_roles ur ON u.role_id = ur.id
        """
    )
    users = db.execute(query).mappings().all()

    return users


@router.post("/upload_players/{organization_id}/{creator_id}/{agent_id}")
async def upload_players(
    *,
    db: Session = Depends(deps.get_db),
    organization_id: str,
    agent_id: str,
    creator_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Upload players for an organization.
    Returns the number of players that were added.
    """
    utils.check_access_admin(current_user)

    # Get organization name for logging
    organization = crud.organization.get(db=db, id=organization_id)
    if not organization:
        raise HTTPException(status_code=404, detail="Organization not found")

    query = text(
        f"""INSERT INTO {settings.PG_SCHEMA}.player_records
        SELECT 
        uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) 
            PLACING '4' FROM 13) 
            PLACING to_hex(floor(random()*(11-8+1) + 8)::int)::text 
            FROM 17)::cstring),
        now(),
        now(),
        FALSE,
        NULL,
        pi2."playerId",
        'signed',
        ARRAY[
            CASE
            WHEN pi2.primary_ws_position IS NULL THEN 'cb'
            WHEN pi2.primary_ws_position = 'gk' THEN 'gk'
            WHEN pi2.primary_ws_position = 'amf' THEN 'cam'
            WHEN pi2.primary_ws_position = 'cb' THEN 'cb'
            WHEN pi2.primary_ws_position = 'cf' THEN 'st'
            WHEN pi2.primary_ws_position = 'dmf' THEN 'dmc'
            WHEN pi2.primary_ws_position = 'lamf' THEN 'lw'
            WHEN pi2.primary_ws_position = 'lb' THEN 'lb'
            WHEN pi2.primary_ws_position = 'lb5' THEN 'lb'
            WHEN pi2.primary_ws_position = 'lcb' THEN 'lcb'
            WHEN pi2.primary_ws_position = 'lcb3' THEN 'lcb'
            WHEN pi2.primary_ws_position = 'lcmf' THEN 'cmf'
            WHEN pi2.primary_ws_position = 'lcmf3' THEN 'cmf'
            WHEN pi2.primary_ws_position = 'ldmf' THEN 'dmc'
            WHEN pi2.primary_ws_position = 'lw' THEN 'lw'
            WHEN pi2.primary_ws_position = 'lwb' THEN 'lwb'
            WHEN pi2.primary_ws_position = 'lwf' THEN 'lw'
            WHEN pi2.primary_ws_position = 'ramf' THEN 'rw'
            WHEN pi2.primary_ws_position = 'rb' THEN 'rb'
            WHEN pi2.primary_ws_position = 'rb5' THEN 'rb'
            WHEN pi2.primary_ws_position = 'rcb' THEN 'rcb'
            WHEN pi2.primary_ws_position = 'rcb3' THEN 'rcb'
            WHEN pi2.primary_ws_position = 'rcmf' THEN 'cmf'
            WHEN pi2.primary_ws_position = 'rcmf3' THEN 'cmf'
            WHEN pi2.primary_ws_position = 'rdmf' THEN 'dmc'
            WHEN pi2.primary_ws_position = 'rw' THEN 'rw'
            WHEN pi2.primary_ws_position = 'rwb' THEN 'rwb'
            WHEN pi2.primary_ws_position = 'rwf' THEN 'rw'
            WHEN pi2.primary_ws_position = 'ss' THEN 'ss'
            ELSE pi2.primary_ws_position -- Keep the original value if no match is found
                END
            ],
            -1,
            -1,
            NULL,
            NULL,
            NULL,
            :org_id,
            :creator_id,
            'sell' AS transfer_strategy,
            NULL,
            NULL
        FROM wyscout.player_info2 pi2
        WHERE agent_id = :agent_id
            AND pi2."playerId" NOT IN (
                SELECT "playerId"
                FROM {settings.PG_SCHEMA}.player_records pr
                WHERE organization_id = :org_id
            );"""
    )

    result = db.execute(
        query,
        {
            "org_id": organization_id,
            "agent_id": agent_id,
            "creator_id": creator_id,
        },
    )
    db.commit()

    # Get the number of rows affected by the INSERT
    players_added = result.rowcount

    # Log the player upload
    log_admin_action(
        db=db,
        user=current_user,
        action_type="upload_players",
        target_type="organization",
        target_id=organization_id,
        details=f"Uploaded {players_added} players for organization {organization.name}",
    )

    return {"players_added": players_added}


@router.get("/users_table")
def get_all_users(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Get all users along with their roles, organization, tokens and other details.
    """
    utils.check_access_admin(current_user)
    query = text(
        f"""
        select email, is_active, u.id, o.name as org_name, o.id as org_id, ct.tokens, ur."name" as role_name, concat(u.first_name, ' ', u.last_name) as full_name, phone_number, u.use_whatsapp, date_created  from {settings.PG_SCHEMA}.user u
        join {settings.PG_SCHEMA}.organizations o on o.id = u.organization_id 
        join {settings.PG_SCHEMA}.user_roles ur on ur.id = u.role_id 
        left join {settings.PG_SCHEMA}.community_tokens ct on ct.organization_id = u.organization_id 
        """
    )
    users = db.execute(query).mappings().all()

    return users


@router.post("/send_create_account/{email}")
async def request_reset_password(
    email: str,
    user_manager: UserManager = Depends(get_user_manager),
    current_user: models.User = Depends(deps.get_current_active_user),
    db: Session = Depends(deps.get_db),
):
    """API endpoint to trigger a password reset email."""

    utils.check_access_admin(current_user)
    user = crud.user.get_by_email(db=db, email=email)
    # Log the password reset request
    log_admin_action(
        db=db,
        user=current_user,
        action_type="send_password_reset",
        target_type="user",
        target_id=user.id,
        details=f"Sent password reset email to {email}",
    )

    return await user_manager.send_reset_link_to_email(email)


@router.post("/enable_whatsapp/{user_id}")
async def enable_whatsapp_for_user(
    user_id: str,
    phone_number: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """Enable WhatsApp and update phone number for user."""
    utils.check_access_admin(current_user)
    user = crud.user.update_phone_and_enable_whatsapp(db, user_id, phone_number)

    # Log the WhatsApp enablement
    log_admin_action(
        db=db,
        user=current_user,
        action_type="enable_whatsapp",
        target_type="user",
        target_id=user_id,
        details=f"Enabled WhatsApp for user {user.email} with phone number {phone_number}",
    )

    return {"success": bool(user), "user_id": user_id}


@router.post("/disable_whatsapp/{user_id}")
async def disable_whatsapp_for_user(
    user_id: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """Disable WhatsApp usage for user."""
    utils.check_access_admin(current_user)
    user = crud.user.disable_whatsapp(db, user_id)

    # Log the WhatsApp disablement
    log_admin_action(
        db=db,
        user=current_user,
        action_type="disable_whatsapp",
        target_type="user",
        target_id=user_id,
        details=f"Disabled WhatsApp for user {user.email}",
    )

    return {"success": bool(user), "user_id": user_id}


@router.post("/manually-add-player")
async def manually_add_player(
    *,
    db: Session = Depends(deps.get_db),
    player_data: ManuallyAddPlayerRequest,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Manually add a player to the derived_tables.manually_added_players table.
    """
    utils.check_access_admin(current_user)

    # Query birth area details from countries table
    birth_area_query = text(
        """SELECT "birthArea_id" , "birthArea_alpha2code" , "birthArea_alpha3code" ,
          "birthArea_name" FROM wyscout.players WHERE "birthArea_name" = :area_name"""
    )
    birth_area_result = db.execute(
        birth_area_query, {"area_name": player_data.birthArea_name}
    ).fetchone()

    if not birth_area_result:
        raise HTTPException(
            status_code=400,
            detail=f"Birth area '{player_data.birthArea_name}' not found in countries table",
        )

    # Query passport area details from countries table
    passport_area_query = text(
        """SELECT "passportArea_id" , "passportArea_alpha2code" , "passportArea_alpha3code" ,
          "passportArea_name" FROM wyscout.players WHERE "passportArea_name" = :area_name"""
    )
    passport_area_result = db.execute(
        passport_area_query, {"area_name": player_data.passportArea_name}
    ).fetchone()

    if not passport_area_result:
        raise HTTPException(
            status_code=400,
            detail=f"Passport area '{player_data.passportArea_name}' not found in countries table",
        )

    # Get the next playerId (max + 1)
    max_player_id_query = text(
        'SELECT COALESCE(MAX("playerId"), 0) + 1 as next_id FROM derived_tables.manually_added_players'
    )
    result = db.execute(max_player_id_query).fetchone()
    next_player_id = result.next_id if result else 1

    # Insert the new player
    insert_query = text(
        """
        INSERT INTO derived_tables.manually_added_players (
            "playerId", "firstName", "lastName", height, "birthDate", foot,
            "currentTeamId","imageDataURL" ,"birthArea_id", "birthArea_alpha2code",
            "birthArea_alpha3code", "birthArea_name", "passportArea_id",
            "passportArea_alpha2code", "passportArea_alpha3code", "passportArea_name"
        ) VALUES (
            :playerId, :firstName, :lastName, :height, :birthDate, :foot,
            :currentTeamId,'https://cdn5.wyscout.com/photos/players/public/ndplayer_100x130.png',
            :birthArea_id, :birthArea_alpha2code,
            :birthArea_alpha3code, :birthArea_name, :passportArea_id,
            :passportArea_alpha2code, :passportArea_alpha3code, :passportArea_name
        )
        """
    )

    try:
        db.execute(
            insert_query,
            {
                "playerId": next_player_id,
                "firstName": player_data.firstName,
                "lastName": player_data.lastName,
                "height": player_data.height,
                "birthDate": player_data.birthDate,
                "foot": player_data.foot,
                "currentTeamId": player_data.currentTeamId,
                "birthArea_id": birth_area_result.birthArea_id,
                "birthArea_alpha2code": birth_area_result.birthArea_alpha2code,
                "birthArea_alpha3code": birth_area_result.birthArea_alpha3code,
                "birthArea_name": player_data.birthArea_name,
                "passportArea_id": passport_area_result.passportArea_id,
                "passportArea_alpha2code": passport_area_result.passportArea_alpha2code,
                "passportArea_alpha3code": passport_area_result.passportArea_alpha3code,
                "passportArea_name": player_data.passportArea_name,
            },
        )
        db.commit()

        # Log the action
        log_admin_action(
            db=db,
            user=current_user,
            action_type="manually_add_player",
            target_type="player",
            target_id=str(current_user.organization_id),
            details=f"Manually added player {player_data.firstName} {player_data.lastName} with ID {next_player_id}",
        )

        return {
            "success": True,
            "playerId": next_player_id,
            "message": f"Player {player_data.firstName} {player_data.lastName} added successfully",
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to add player: {str(e)}")
