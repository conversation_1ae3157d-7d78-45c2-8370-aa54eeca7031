"""Added email proposed to in community deal

Revision ID: ef5124a47f5a
Revises: cbd4abdf21c2
Create Date: 2024-11-14 14:26:42.419206

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ef5124a47f5a'
down_revision = 'cbd4abdf21c2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('community_deal', sa.Column('email_proposed_to', sa.String(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('community_deal', 'email_proposed_to', schema='crm_test')
    # ### end Alembic commands ###