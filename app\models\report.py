from sqlalchemy import Column, String, Foreign<PERSON><PERSON>, Integer
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from app.models.extended_base_mixin import ExtendedBaseMixin
from app.db.base_class import Base
from app.config import settings


class Report(Base, ExtendedBaseMixin):
    __tablename__ = "reports"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    player_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.player_records.id"),
        index=True,
    )
    strengths = Column(String)
    weaknesses = Column(String)
    model_ranks = Column(String, nullable=True)
    current_ability = Column(Integer)
    lookalike = Column(String, nullable=True)
    conclusion = Column(String, nullable=True)
    report_type = Column(ARRAY(String))
    player = relationship("PlayerRecord", back_populates="scouting_reports")
    match_label = Column(ARRAY(String), nullable=True)
