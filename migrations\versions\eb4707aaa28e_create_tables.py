"""Create tables

Revision ID: eb4707aaa28e
Revises: 7bb6290dfc37
Create Date: 2023-12-18 17:37:14.212913

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'eb4707aaa28e'
down_revision = '7bb6290dfc37'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_players', sa.Column('index_position', sa.Integer(), nullable=True), schema='crm')
    op.add_column('field_players', sa.Column('transfer_fee', sa.Float(), nullable=True), schema='crm')
    op.add_column('field_players', sa.Column('asking_salary', sa.Float(), nullable=True), schema='crm')
    op.drop_column('field_players', 'field_position', schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('field_players', sa.Column('field_position', sa.VARCHAR(), autoincrement=False, nullable=True), schema='crm')
    op.drop_column('field_players', 'asking_salary', schema='crm')
    op.drop_column('field_players', 'transfer_fee', schema='crm')
    op.drop_column('field_players', 'index_position', schema='crm')
    # ### end Alembic commands ###