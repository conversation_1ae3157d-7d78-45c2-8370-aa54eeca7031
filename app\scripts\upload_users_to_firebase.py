from firebase_admin import auth, initialize_app, credentials
from app.config import settings
from sqlalchemy import create_engine
import pandas as pd
cnx = create_engine(settings.PG_URL)

current_schema = settings.PG_SCHEMA

stage_schema = 'crm_dev'
prod_schema = 'crm'
test_schema = 'crm_test'
cred = credentials.Certificate(settings.FIREBASE_CREDS)
initialize_app(credential=cred)

def import_users_to_firebase():
    
    
    df = pd.read_sql(f'select * from {current_schema}.user', cnx)
    for i, row in df.iterrows():
        auth.delete_user(str(row['id']))
    #     try:
    #         user = auth.get_user(str(row['id']))
    #         print(user.email)
    #     except:
    #         users.append(auth.ImportUserRecord(
    #                 uid=str(row['id']),
    #                 display_name='None' if ((row['first_name']==None) & (row['last_name'] == None)) else row['first_name'] + ' ' + row['last_name'],
    #                 email=row['email'],
    #                 email_verified=False,
    #                 password_hash=row['hashed_password'].encode(),
    #                 custom_claims={
    #                     'schema': settings.PG_SCHEMA,
    #                     'is_active': row['is_active'],
    #                     'is_superuser': row['is_superuser'],
    #                     'is_verified': row['is_verified'],
    #                     'organization_id': str(row['organization_id']),
    #                     'role_id': str(row['role_id'])
    #                 }
    #             ))

    # hash_alg = auth.UserImportHash.bcrypt()
    # result = auth.import_users(users, hash_alg=hash_alg)

    # print('Successfully imported {0} users. Failed to import {1} users.'.format(
    # result.success_count, result.failure_count))

    # for err in result.errors:
    #     print('Failed to import {0} due to {1}'.format(
    #         users[err.index].uid, err.reason))