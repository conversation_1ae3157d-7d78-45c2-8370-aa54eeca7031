from typing import List, Optional, TYPE_CHECKING
from datetime import datetime
import uuid
from pydantic import (
    validator,
    ValidationError,
    BaseModel,
)
from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)
from app.schemas.enums import ReportType

if TYPE_CHECKING:
    from app.schemas.player_record import PlayerRecord


class ReportUpdate(ExtendedUpdateBase):
    player_id: Optional[uuid.UUID]
    strengths: Optional[str]
    weaknesses: Optional[str]
    model_ranks: Optional[str]
    current_ability: Optional[int]
    lookalike: Optional[str]
    conclusion: Optional[str]
    report_type: List[ReportType] = []
    match_label: List[str] = []

    @validator("current_ability")
    def pontetial_range(cls, v):
        if v < 1 or v > 12:
            raise ValidationError("Current ability should be between 1 and 12")
        return v

    class Config:
        use_enum_values = True
        use_cache=True
        schema_extra = {
            "example": {
                "player_id": "c52c7ad2-6903-4617-b24b-446346570cf2",
                "strengths": "Can shoot",
                "weaknesses": "Cant pass",
                "model_ranks": "Top ranks",
                "current_ability": 1,
                "lookalike": "Messi",
                "conclusion": "Top choice",
                "notes": "Good connection with club",
                "report_type": [],
                "created_at": "2022-08-10T08:40:41.016Z",
                "match_label": 'Ludogorets II - Etar, 1 - 1'
            }
        }


class ReportCreate(ReportUpdate, ExtendedCreateBase):
    player_id: uuid.UUID
    strengths: str
    weaknesses: str
    model_ranks: Optional[str]
    current_ability: Optional[int]
    lookalike: Optional[str]
    conclusion: Optional[str]
    report_type: List[ReportType] = []
    match_label: Optional[List[str]]


class Report(ReportCreate, ExtendedBase):
    player: "PlayerRecord" 


class ReportShort(BaseModel):
    id: uuid.UUID
    player_id: uuid.UUID
    created_at: datetime

    class Config:
        orm_mode = True
        use_cache=True
