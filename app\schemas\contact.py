import uuid
from typing import Optional, List
from pydantic import BaseModel
from app.schemas.enums import ContactType
from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)


class ContactUpdate(ExtendedUpdateBase):
    id: Optional[uuid.UUID]
    first_name: Optional[str]
    last_name: Optional[str]
    contact_type: Optional[ContactType]
    title: Optional[str]
    email: Optional[str]
    contact_organization: Optional[str]
    phone_number: Optional[str]
    owner: Optional[str]


class ContactCreate(ExtendedCreateBase):
    first_name: str
    last_name: str
    contact_type: ContactType
    title: Optional[str] = ""
    email: Optional[str]
    contact_organization: Optional[str] = ""
    phone_number: Optional[str]
    owner: Optional[str]


class Contact(ContactCreate, ExtendedBase): ...


class ContactShort(BaseModel):
    id: uuid.UUID
    first_name: str
    last_name: str
    contact_organization: str
    contact_type: ContactType
    phone_number: Optional[str]

    class Config:
        orm_mode = True
        use_cache = True


class ContactMatch(BaseModel):
    contact_id: str
    email: str
    fullName: str
    similarity_score: float


class WhatsAppContactCheckResponse(BaseModel):
    contact_id: str
    email: str
    fullName: str
    similarity_score: float
    top_matches: List[ContactMatch]

    class Config:
        schema_extra = {
            "example": {
                "contact_id": "123e4567-e89b-12d3-a456-************",
                "email": "<EMAIL>",
                "fullName": "John Doe",
                "similarity_score": 0.95,
                "top_matches": [
                    {
                        "contact_id": "123e4567-e89b-12d3-a456-************",
                        "email": "<EMAIL>",
                        "fullName": "Jane Doe",
                        "similarity_score": 0.75,
                    },
                    {
                        "contact_id": "123e4567-e89b-12d3-a456-426614174200",
                        "email": "<EMAIL>",
                        "fullName": "John Smith",
                        "similarity_score": 0.65,
                    },
                ],
            }
        }
