import logging
from typing import Op<PERSON>, Dict, Any, List, Tuple
from sqlalchemy.orm import Session
from fuzzywuzzy import fuzz
from fuzzywuzzy import process

from app import crud, models

# Note: ConstrolStage enum not needed for this implementation

logger = logging.getLogger(__name__)


class EntityMatcher:
    """
    Service for matching extracted contract names to existing platform entities
    """

    def __init__(self):
        self.min_similarity_threshold = 0  # Always return best match

    def match_player(
        self,
        extracted_player_name: str,
        db: Session,
        org_id: str,
        can_access_sense: bool,
    ) -> Dict[str, Any]:
        """
        Match extracted player name to existing player records

        Returns:
        {
            "suggested_player_id": "uuid or None",
            "suggested_player_name": "matched name or None",
            "player_match_confidence": float
        }
        """
        try:
            if not extracted_player_name or not extracted_player_name.strip():
                return {
                    "suggested_player_id": None,
                    "suggested_player_name": None,
                    "player_match_confidence": 0.0,
                }

            # Get all players with the specified filters
            filters = {
                "control_stage": [
                    "singed",
                    "mandate",
                    "mandate_on_demand",
                ],  # Note: 'singed' is the actual enum value (typo in enum)
                "active": True,
            }

            logger.info(f"Searching for player match: '{extracted_player_name}'")

            players = crud.player_record.get_all_with_filters(
                db, org_id, can_access_sense, filters
            )

            if not players:
                logger.info("No players found in database")
                return {
                    "suggested_player_id": None,
                    "suggested_player_name": None,
                    "player_match_confidence": 0.0,
                }

            # Build list of player names for fuzzy matching
            player_choices = []
            player_map = {}

            for player in players:
                if hasattr(player, "player_info") and player.player_info:
                    # Combine firstName and lastName
                    first_name = getattr(player.player_info, "firstName", "") or ""
                    last_name = getattr(player.player_info, "lastName", "") or ""
                    full_name = f"{first_name} {last_name}".strip()

                    if full_name:
                        player_choices.append(full_name)
                        # Map full name to player record (use most recent if duplicates)
                        if (
                            full_name not in player_map
                            or player.created_at > player_map[full_name].created_at
                        ):
                            player_map[full_name] = player

            if not player_choices:
                logger.info("No valid player names found")
                return {
                    "suggested_player_id": None,
                    "suggested_player_name": None,
                    "player_match_confidence": 0.0,
                }

            # Find best match using fuzzy matching
            best_match, confidence = process.extractOne(
                extracted_player_name, player_choices, scorer=fuzz.ratio
            )

            matched_player = player_map[best_match]

            logger.info(
                f"Best player match: '{best_match}' (confidence: {confidence}%)"
            )

            return {
                "suggested_player_id": str(matched_player.id),
                "suggested_player_name": best_match,
                "player_match_confidence": float(confidence),
            }

        except Exception as e:
            logger.error(f"Error matching player '{extracted_player_name}': {str(e)}")
            return {
                "suggested_player_id": None,
                "suggested_player_name": None,
                "player_match_confidence": 0.0,
            }

    def match_contact(
        self,
        extracted_agent_name: str,
        db: Session,
        org_id: str,
        can_access_sense: bool = False,
    ) -> Dict[str, Any]:
        """
        Match extracted agent name to existing contacts

        Returns:
        {
            "suggested_contact_id": "uuid or None",
            "suggested_contact_name": "matched name or None",
            "agent_match_confidence": float
        }
        """
        try:
            if not extracted_agent_name or not extracted_agent_name.strip():
                return {
                    "suggested_contact_id": None,
                    "suggested_contact_name": None,
                    "agent_match_confidence": 0.0,
                }

            logger.info(f"Searching for contact match: '{extracted_agent_name}'")

            # Get all contacts for the organization
            contacts = crud.contact.get_all_w_org(db, org_id, can_access_sense)

            if not contacts:
                logger.info("No contacts found in database")
                return {
                    "suggested_contact_id": None,
                    "suggested_contact_name": None,
                    "agent_match_confidence": 0.0,
                }

            # Build list of contact names for fuzzy matching
            contact_choices = []
            contact_map = {}

            for contact in contacts:
                # Get contact name (adjust field names based on your Contact model)
                contact_name = self._get_contact_name(contact)

                if contact_name:
                    contact_choices.append(contact_name)
                    # Map name to contact record (use most recent if duplicates)
                    if (
                        contact_name not in contact_map
                        or contact.created_at > contact_map[contact_name].created_at
                    ):
                        contact_map[contact_name] = contact

            if not contact_choices:
                logger.info("No valid contact names found")
                return {
                    "suggested_contact_id": None,
                    "suggested_contact_name": None,
                    "agent_match_confidence": 0.0,
                }

            # Find best match using fuzzy matching
            best_match, confidence = process.extractOne(
                extracted_agent_name, contact_choices, scorer=fuzz.ratio
            )

            matched_contact = contact_map[best_match]

            logger.info(
                f"Best contact match: '{best_match}' (confidence: {confidence}%)"
            )

            return {
                "suggested_contact_id": str(matched_contact.id),
                "suggested_contact_name": best_match,
                "agent_match_confidence": float(confidence),
            }

        except Exception as e:
            logger.error(f"Error matching contact '{extracted_agent_name}': {str(e)}")
            return {
                "suggested_contact_id": None,
                "suggested_contact_name": None,
                "agent_match_confidence": 0.0,
            }

    def _get_contact_name(self, contact) -> Optional[str]:
        """
        Extract the full name from a contact record
        Adjust this method based on your Contact model structure
        """
        try:
            # Common contact name field patterns - adjust based on your model
            if hasattr(contact, "full_name") and contact.full_name:
                return contact.full_name.strip()

            if hasattr(contact, "name") and contact.name:
                return contact.name.strip()

            # If separate first/last name fields exist
            first_name = ""
            last_name = ""

            if hasattr(contact, "first_name"):
                first_name = contact.first_name or ""
            if hasattr(contact, "last_name"):
                last_name = contact.last_name or ""

            if first_name or last_name:
                return f"{first_name} {last_name}".strip()

            # If email is used as identifier
            if hasattr(contact, "email") and contact.email:
                return contact.email.strip()

            return None

        except Exception as e:
            logger.error(f"Error extracting contact name: {str(e)}")
            return None

    async def match_entities(
        self,
        extracted_data: Dict[str, Any],
        db: Session,
        org_id: str,
        can_access_sense: bool,
    ) -> Dict[str, Any]:
        """
        Match player, contact, and team entities from extracted contract data

        Returns enhanced extraction data with entity matches
        """
        result = extracted_data.copy()

        # Match player if player_name was extracted
        if extracted_data.get("player_name"):
            player_match = self.match_player(
                extracted_data["player_name"], db, org_id, can_access_sense
            )
            result.update(player_match)
        else:
            result.update(
                {
                    "suggested_player_id": None,
                    "suggested_player_name": None,
                    "player_match_confidence": 0.0,
                }
            )

        # Match agent if agent_name was extracted
        if extracted_data.get("agent_name"):
            agent_match = self.match_contact(extracted_data["agent_name"], db, org_id)
            result.update(agent_match)
        else:
            result.update(
                {
                    "suggested_contact_id": None,
                    "suggested_contact_name": None,
                    "agent_match_confidence": 0.0,
                }
            )

        # Match team if club_name was extracted
        if extracted_data.get("club_name"):
            team_match = await self.match_team(
                extracted_data["club_name"], db, org_id, can_access_sense
            )
            result.update(team_match)
        else:
            result.update(
                {
                    "suggested_team_id": None,
                    "suggested_team_name": None,
                    "team_match_confidence": 0.0,
                }
            )

        return result

    async def match_team(
        self, extracted_club_name: str, db: Session, org_id: str, can_access_sense: bool
    ) -> Dict[str, Any]:
        """
        Match extracted club name to platform team using lookup API

        Returns team matching results with suggested_team_id and confidence
        """
        if not extracted_club_name or not extracted_club_name.strip():
            return {
                "suggested_team_id": None,
                "suggested_team_name": None,
                "team_match_confidence": 0.0,
            }

        try:
            from app.utils import get_async_response
            from app.config import settings
            import urllib.parse

            # URL encode the club name to handle spaces and special characters
            # This is crucial for API calls with team names containing spaces
            encoded_club_name = urllib.parse.quote(extracted_club_name, safe="")

            logger.info(
                f"Searching for team match: '{extracted_club_name}' (encoded: '{encoded_club_name}')"
            )

            # Call the teams updated lookup endpoint
            resp = await get_async_response(
                f"{settings.LOOKUP_API_URL}/teams/updated_lookup/{encoded_club_name}",
                auth=(settings.LOOKUP_API_USR, settings.LOOKUP_API_PASS),
            )

            if resp.status_code != 200:
                logger.warning(
                    f"Team lookup failed with status {resp.status_code} for '{extracted_club_name}'"
                )
                # Try to get response text for debugging
                try:
                    response_text = (
                        resp.text
                        if hasattr(resp, "text")
                        else "No response text available"
                    )
                    logger.warning(f"Response text: {response_text}")
                except Exception:
                    pass
                return {
                    "suggested_team_id": None,
                    "suggested_team_name": None,
                    "team_match_confidence": 0.0,
                }

            # Handle JSON parsing with better error handling
            try:
                teams_data = resp.json()
            except Exception as json_error:
                logger.error(
                    f"Failed to parse JSON response for team '{extracted_club_name}': {str(json_error)}"
                )
                return {
                    "suggested_team_id": None,
                    "suggested_team_name": None,
                    "team_match_confidence": 0.0,
                }

            if not teams_data or len(teams_data) == 0:
                logger.info(f"No teams found for '{extracted_club_name}'")
                return {
                    "suggested_team_id": None,
                    "suggested_team_name": None,
                    "team_match_confidence": 0.0,
                }

            # Get the first team (best match)
            first_team = teams_data[0]
            team_id = first_team.get("teamId")
            team_name = first_team.get("officialName") or first_team.get("name")

            # Calculate confidence based on name similarity
            from fuzzywuzzy import fuzz

            confidence = (
                fuzz.ratio(extracted_club_name.lower(), team_name.lower())
                if team_name
                else 0
            )

            logger.info(
                f"Best team match: '{team_name}' (teamId: {team_id}, confidence: {confidence}%)"
            )

            return {
                "suggested_team_id": str(team_id) if team_id else None,
                "suggested_team_name": team_name,
                "team_match_confidence": float(confidence),
            }

        except Exception as e:
            logger.error(f"Error matching team '{extracted_club_name}': {str(e)}")
            return {
                "suggested_team_id": None,
                "suggested_team_name": None,
                "team_match_confidence": 0.0,
            }


# Global instance
entity_matcher = EntityMatcher()
