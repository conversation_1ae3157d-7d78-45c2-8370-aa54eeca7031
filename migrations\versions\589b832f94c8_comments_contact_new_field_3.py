"""Comments + contact new field 3

Revision ID: 589b832f94c8
Revises: b083e9391d1a
Create Date: 2024-08-15 15:41:48.081688

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '589b832f94c8'
down_revision = 'b083e9391d1a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('comments_activity',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('comment', sa.String(), nullable=True),
    sa.Column('time', sa.DateTime(), nullable=True),
    sa.Column('creator', sa.String(), nullable=True),
    sa.Column('activity_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['activity_id'], ['crm.activity.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_comments_activity_activity_id'), 'comments_activity', ['activity_id'], unique=False, schema='crm')
    op.create_index(op.f('ix_crm_comments_activity_time'), 'comments_activity', ['time'], unique=False, schema='crm')
    op.add_column('activity', sa.Column('description', sa.String(), nullable=True), schema='crm')
    op.add_column('contacts', sa.Column('owner', sa.String(), nullable=True), schema='crm')
    op.create_index(op.f('ix_crm_tracked_transfers_request_id'), 'tracked_transfers', ['request_id'], unique=False, schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_tracked_transfers_request_id'), table_name='tracked_transfers', schema='crm')
    op.drop_column('contacts', 'owner', schema='crm')
    op.drop_column('activity', 'description', schema='crm')
    op.drop_index(op.f('ix_crm_comments_activity_time'), table_name='comments_activity', schema='crm')
    op.drop_index(op.f('ix_crm_comments_activity_activity_id'), table_name='comments_activity', schema='crm')
    op.drop_table('comments_activity', schema='crm')
    # ### end Alembic commands ###