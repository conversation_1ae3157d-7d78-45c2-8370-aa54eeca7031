"""New contract type and other legal fixes

Revision ID: 45d7dfed2190
Revises: 6fa1dc400e45
Create Date: 2024-06-17 15:00:45.648252

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '45d7dfed2190'
down_revision = '6fa1dc400e45'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contracts', sa.Column('notify', postgresql.ARRAY(sa.String()), nullable=True), schema='crm_test')
    op.add_column('contracts', sa.Column('company', sa.String(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('contracts', 'company', schema='crm_test')
    op.drop_column('contracts', 'notify', schema='crm_test')
    # ### end Alembic commands ###