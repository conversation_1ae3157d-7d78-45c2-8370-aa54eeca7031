from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import models
from app.api import deps, utils
from app.crud.crud_admin_change import admin_change
from app.schemas.admin_change import AdminChange

router = APIRouter()


@router.get("/", response_model=List[AdminChange])
async def read_admin_changes(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    target_type: str = None,
    target_id: str = None,
    admin_id: str = None,
) -> Any:
    """
    Retrieve admin changes with optional filtering.
    Only superusers can access this endpoint.
    """
    utils.check_access_admin(current_user)

    if target_type and target_id:
        changes = admin_change.get_by_target(
            db=db,
            target_type=target_type,
            target_id=target_id,
        )
    elif admin_id:
        changes = admin_change.get_by_admin(
            db=db,
            admin_id=admin_id,
        )
    else:
        changes = admin_change.get_multi(db=db)

    return changes
