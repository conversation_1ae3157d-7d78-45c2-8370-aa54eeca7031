"""Master migrations

Revision ID: 669fa4a79966
Revises: 9db981c5c58d
Create Date: 2025-04-04 20:09:56.190075

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '669fa4a79966'
down_revision = '9db981c5c58d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('share_tokens',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('resource_type', sa.String(length=50), nullable=False),
    sa.Column('resource_id', sa.UUID(), nullable=False),
    sa.Column('token', sa.UUID(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.<PERSON>umn('is_disabled', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('token'),
    schema='crm'
    )
    op.create_index(op.f('ix_crm_share_tokens_id'), 'share_tokens', ['id'], unique=False, schema='crm')
    op.drop_index('idx_team_activities', table_name='activity', schema='crm')
    op.drop_index('idx_community_deal', table_name='community_deal', schema='crm')
    op.add_column('field_players', sa.Column('video_link', sa.String(), nullable=True), schema='crm')
    op.add_column('organizations', sa.Column('type', sa.String(), nullable=True), schema='crm')
    op.drop_index('idx_player_records', table_name='player_records', schema='crm')
    op.drop_index('idx_team_requests', table_name='team_requests', schema='crm')
    op.drop_index('idx_team_requests_position', table_name='team_requests', schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_team_requests_position', 'team_requests', ['position'], unique=False, schema='crm')
    op.create_index('idx_team_requests', 'team_requests', ['status'], unique=False, schema='crm')
    op.create_index('idx_player_records', 'player_records', ['control_stage'], unique=False, schema='crm')
    op.drop_column('organizations', 'type', schema='crm')
    op.drop_column('field_players', 'video_link', schema='crm')
    op.create_index('idx_community_deal', 'community_deal', ['type', 'feedback'], unique=False, schema='crm')
    op.drop_constraint(None, 'activity', schema='crm', type_='foreignkey')
    op.create_index('idx_team_activities', 'activity', ['stage'], unique=False, schema='crm')
    op.drop_index(op.f('ix_crm_share_tokens_id'), table_name='share_tokens', schema='crm')
    op.drop_table('share_tokens', schema='crm')
    # ### end Alembic commands ###