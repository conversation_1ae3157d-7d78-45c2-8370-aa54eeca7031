"""Change user table remove is enabled

Revision ID: b199f6a70b37
Revises: 1322d8510885
Create Date: 2023-08-29 13:33:31.811183

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b199f6a70b37'
down_revision = '1322d8510885'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('user', sa.Column('first_name', sa.String(), nullable=True), schema='crm_test')
    op.add_column('user', sa.Column('last_name', sa.String(), nullable=True), schema='crm_test')


def downgrade():
    op.drop_column('user', 'first_name', schema='crm_test')
    op.drop_column('user', 'last_name', schema='crm_test')