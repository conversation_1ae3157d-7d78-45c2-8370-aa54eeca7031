from app.crud.crud_base import CRUDBase, ModelType
from typing import Any, Optional
from app import models
from app.schemas.community_tokens import CommunityTokensCreate, CommunityTokensUpdate

from sqlalchemy.orm import Session, lazyload, Load
from typing import Any, Optional, TypeVar
from app.db.base_class import Base

ModelType = TypeVar("ModelType", bound=Base)

class CRUDCommunityTokens(CRUDBase[models.CommunityTokens, CommunityTokensCreate, CommunityTokensUpdate]):
    ...
    def get_for_org(self, db:Session, organization_id: Any) -> Optional[ModelType]:
        return db.query(self.model).options(
            Load(self.model).selectinload("*"),
            lazyload("*"),
            ).filter((self.model.organization_id == organization_id)).first()

community_tokens = CRUDCommunityTokens(models.CommunityTokens)