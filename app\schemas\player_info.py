from typing import Optional
from pydantic import BaseModel

from app.schemas.enums import Foot
from datetime import date

class PlayerInfoShort(BaseModel):
    playerId: int
    firstName: str
    lastName: str
    shortName: Optional[str] = None
    birthDate: Optional[str]
    team_name: Optional[str] = None
    currentTeamId: Optional[int] = None
    passport: Optional[str] = None
    tm_link: Optional[str] = None

    class Config:
        orm_mode = True
        use_cache=True
class PlayerInfo(BaseModel):
    playerId: int
    firstName: str
    lastName: str
    shortName: Optional[str] = None
    birth_area: Optional[str] = None
    birthDate: Optional[str]
    team_name: Optional[str] = None
    divisionLevel: Optional[int] = None
    team_area_name: Optional[str] = None
    currentTeamId: Optional[int] = None
    imageDataURL: Optional[str] = None
    passport: Optional[str] = None
    height: Optional[str] = None
    eu: Optional[bool]
    foot: Optional[Foot]
    tm_link: Optional[str] = None
    tm_value: Optional[float] = None
    agency: Optional[str]
    player_role: Optional[str]
    primary_ws_position: Optional[str]
    secondary_ws_position: Optional[str]
    third_ws_position: Optional[str]
    contract_expiry: Optional[date]
    date_joined_current_team: Optional[date]
    
    class Config:
        orm_mode = True
        use_cache=True

class PlayerInfoExtended(BaseModel):
    playerId: int
    firstName: str
    lastName: str
    shortName: Optional[str] = None
    birth_area: Optional[str] = None
    birthDate: Optional[str]
    team_name: Optional[str] = None
    passport: Optional[str] = None
    foot: Optional[Foot]
    tm_link: Optional[str] = None
    tm_value: Optional[float] = None
    agency: Optional[str]
    player_role: Optional[str]
    height: Optional[int]
    weight: Optional[int]
    role_name: Optional[str]
    divisionLevel: Optional[int]
    team_area_name: Optional[str]
    segment: Optional[int]
    smoothed_rating: Optional[float]
    primary_position: Optional[str]
    primary_position_percent: Optional[float]
    secondary_position: Optional[str]
    secondary_position_percent: Optional[float]
    third_position: Optional[str]
    third_position_percent: Optional[float]
    date_joined_current_team: Optional[date]
    contract_expiry: Optional[date]
    league_tier: Optional[int]
    league_country: Optional[str]
    current_value: Optional[float]
    player_value_last_update: Optional[date]
    highest_value: Optional[float]
    highest_value_date: Optional[date]
    all_injuries: Optional[str]
    currently_injured: Optional[int]
    latest_injury: Optional[str]
    num_injuries: Optional[int]
    most_games_missed: Optional[int]
    longest_duration: Optional[int]
    total_games_missed: Optional[int]
    total_days_missed: Optional[int]
    average_recovery_time: Optional[float]
    player_url : Optional[str]

    class Config:
        orm_mode = True
        use_cache=True