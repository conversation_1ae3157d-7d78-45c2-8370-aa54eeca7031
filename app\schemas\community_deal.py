from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)
from typing import Optional
import uuid
from app.schemas.enums import (
    Feedback
)
from app.schemas.enums import CommunityDealType
from app.schemas.community_proposal import CommunityProposal

class CommunityDealUpdate(ExtendedUpdateBase):
    feedback: Feedback
    email_proposed_to: Optional[str]
    phone_number: Optional[str]

class CommunityDealCreate(ExtendedCreateBase):
    type: CommunityDealType
    title: str
    community_deal_id: Optional[uuid.UUID]
    community_proposal_id: uuid.UUID
    organization_proposed_to: Optional[str]
    suitability_score: Optional[float]
    
class CommunityDeal(CommunityDealCreate, ExtendedBase):
    feedback: Optional[Feedback]
    community_proposal: CommunityProposal
    email_proposed_to: Optional[str]
    phone_number: Optional[str]
    
    class Config:
        orm_mode = True
        use_cache=True