import uuid

from sqlalchemy.orm import relationship
from sqlalchemy import Column, String, DateTime, ForeignKey, Boolean
from sqlalchemy.dialects.postgresql import UUID

from app.db.base_class import Base
from app.config import settings
from datetime import datetime


class RankRecord(Base):
    __tablename__ = "rank_records"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, default='')
    youth = Column(Boolean, default=False)
    target_position = Column(String, default='')
    default_quality = Column(Boolean, default=False)
    description = Column(String, default='')
    purpose = Column(String, default='Internal')
    for_who = Column(String, default='Enskai')
    config = Column(String)
    created_at = Column(DateTime, default=datetime.now)
    created_by = Column(UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.user.id"))
    organization_id = Column(UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.organizations.id"))
