
from fastapi import APIRouter, Depends, Body, Request
import requests
from fastapi.responses import JSONResponse
from app import  models
from app.api import deps
try:
    from ...config import settings
except:
    from app.config import settings

router = APIRouter(
    prefix="/staff",
    tags=["staff"],
    dependencies=[Depends(deps.generate_route_auth_check_func("agent_map"))]

)

@router.get("/all")
async def get_all_staff(
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Retrieve all staff.
    """
    resp = requests.get(
        f"{settings.AGENT_API_URL}/staff/all",
        auth=(settings.AGENT_API_USR, settings.AGENT_API_PASS),
    )
    return JSONResponse(status_code=resp.status_code, content=resp.json())

@router.post("/")
async def query_staff_data(
    staff=Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
    ):
    """
    Retrieve specific data for a staff.
    """
    resp = requests.post(
        f"{settings.AGENT_API_URL}/staff",
        auth=(settings.AGENT_API_USR, settings.AGENT_API_PASS),
        json=staff,
    )
    return JSONResponse(status_code=resp.status_code, content=resp.json())