"""Added firebase refresh token table

Revision ID: f6b5b75ead32
Revises: 1ab6014e3b54
Create Date: 2024-02-05 13:01:13.004913

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f6b5b75ead32'
down_revision = '1ab6014e3b54'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('refresh_token',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('refresh_token', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['id'], ['crm_test.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_refresh_token_id'), 'refresh_token', ['id'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_test_refresh_token_id'), table_name='refresh_token', schema='crm_test')
    op.drop_table('refresh_token', schema='crm_test')
    # ### end Alembic commands ###